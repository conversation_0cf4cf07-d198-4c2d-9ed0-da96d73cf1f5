<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.backend.dal.mapper.user.ClinicTrialMapper">
    <select id="pageHcgClinicTrialUserDTO" resultType="com.mira.backend.pojo.dto.ClinicTrialUserDTO">
        select t.email,u.id,u.`status`,i.nickname,i.birth_year,i.birth_month,i.birth_of_day,r.time_zone
        from user_product_trial t
        LEFT JOIN app_user u on t.email=u.email
        LEFT JOIN app_user_info i on u.id=i.user_id
        LEFT JOIN app_user_algorithm_result r on u.id=r.user_id
        where u.`deleted`=0
        and t.batch=#{batch}
        <include refid="listClinicTrialUserDTO-if"></include>
        order by t.create_time desc
        limit #{currIndex} , #{pageSize}
    </select>

    <select id="countHcgClinicTrialUserDTO" resultType="java.lang.Integer">
        select count(1)
        from user_product_trial t
        LEFT JOIN app_user u on t.email=u.email
        LEFT JOIN app_user_info i on u.id=i.user_id
        where u.`deleted`=0
        and t.batch=#{batch}
        <include refid="listClinicTrialUserDTO-if"></include>
    </select>

    <select id="pageMoodAndWellBeingTrialUserDTO" resultType="com.mira.backend.pojo.dto.ClinicTrialUserDTO">
        select t.email,u.id,u.`status`,i.nickname,i.birth_year,i.birth_month,i.birth_of_day,r.time_zone
        from user_mood_wellbeing_trial t
        LEFT JOIN app_user u on t.email=u.email
        LEFT JOIN app_user_info i on u.id=i.user_id
        LEFT JOIN app_user_algorithm_result r on u.id=r.user_id
        where u.`deleted`=0
        and t.batch=#{batch}
        <include refid="listClinicTrialUserDTO-if"></include>
        order by t.create_time desc
        limit #{currIndex} , #{pageSize}
    </select>

    <select id="countMoodAndWellBeingTrialUserDTO" resultType="java.lang.Integer">
        select count(1)
        from user_mood_wellbeing_trial t
        LEFT JOIN app_user u on t.email=u.email
        LEFT JOIN app_user_info i on u.id=i.user_id
        where u.`deleted`=0
        and t.batch=#{batch}
        <include refid="listClinicTrialUserDTO-if"></include>
    </select>

    <sql id="listClinicTrialUserDTO-if">
        <if test="keyWord!=null and keyWord != ''">
            and (i.nickname like concat('%', #{keyWord,jdbcType=VARCHAR}, '%')
            or u.email like concat('%',#{keyWord,jdbcType=VARCHAR}, '%')
            )
        </if>
    </sql>

    <select id="listClinicTrialBatch" resultType="com.mira.backend.pojo.dto.ClinicTrialBatchDTO">
        select c.type, c.name as 'batch_name', c.batch, c.create_time_str, t.name, t.icon, t.`code`
        from clinic_trial_batch c
        left join app_tenant t on c.tenant_code = t.code
        where t.code in
        <foreach collection="tenantCodes" item="item" index="index" open="(" separator="," close=")">
            '${item}'
        </foreach>
        order by c.create_time desc
    </select>

</mapper>
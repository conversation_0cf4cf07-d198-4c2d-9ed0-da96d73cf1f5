<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.backend.dal.mapper.admin.AdminRoleMapper">

    <select id="viewAdminRoles" resultType="com.mira.backend.pojo.vo.admin.AdminRoleVO">
        select r.id, r.code, r.name, r.name_en, r.description
        from admin_role r
                 LEFT JOIN admin_user_role ur on ur.role_id = r.id
        where ur.admin_id = #{adminId}
    </select>
</mapper>

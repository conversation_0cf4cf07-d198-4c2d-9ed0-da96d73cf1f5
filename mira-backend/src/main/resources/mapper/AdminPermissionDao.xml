<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.backend.dal.mapper.admin.AdminPermissionMapper">
    <select id="viewRolePermissions" resultType="com.mira.backend.pojo.vo.admin.AdminPermissionVO">
        select p.id, p.code, p.type, p.parent_id, p.description, p.description_en
        from admin_permission p
                 LEFT JOIN admin_role_permission rp on rp.permission_id = p.id
        where rp.role_id = #{roleId}
        order by p.type
    </select>
</mapper>

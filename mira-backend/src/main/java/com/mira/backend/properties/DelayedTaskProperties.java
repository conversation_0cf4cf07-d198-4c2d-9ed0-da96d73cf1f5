package com.mira.backend.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * delayed task properties
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "delayed.task")
public class DelayedTaskProperties {

    /**
     * 延迟时间 默认设置为60，单位：分钟
     */
    private Integer delayedTime;

}

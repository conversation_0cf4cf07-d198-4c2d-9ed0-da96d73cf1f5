package com.mira.backend.interceptor;

import com.mira.backend.exception.BackendException;
import com.mira.core.annotation.Anonymous;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JwtUtil;
import com.mira.web.properties.JwtProperties;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * oauth token interceptor
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Slf4j
public class TokenInterceptor implements HandlerInterceptor {
    @Resource
    private JwtProperties jwtProperties;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!handler.getClass().isAssignableFrom(HandlerMethod.class)) {
            return true;
        }

        // 匿名接口放行
        Method method = ((HandlerMethod) handler).getMethod();
        if (method.isAnnotationPresent(Anonymous.class)) {
            return true;
        }
        if (Objects.nonNull(request.getHeader(HeaderConst.ANONYMOUS))) {
            return true;
        }

        // check
        String authorization = request.getHeader(HeaderConst.AUTHORIZATION);
        if (StringUtils.isNotEmpty(authorization)) {
            Claims claims = JwtUtil.parseClaims(authorization, jwtProperties.getSignKey());
            if (Objects.isNull(claims)) {
                throw new BackendException(BizCodeEnum.TOKEN_INVALID);
            }
            // 用户类别
            String userType = request.getHeader(HeaderConst.USER_TYPE);
            // 保存登录信息上下文
            setContext(claims, userType);
            return true;
        }

        throw new BackendException(BizCodeEnum.TOKEN_INVALID);
    }

    private void setContext(Claims claims, String userType) {
        BaseLoginInfo baseLoginInfo = new BaseLoginInfo();
        ContextHolder.<BaseLoginInfo>setLoginInfo(baseLoginInfo);

        baseLoginInfo.setId(Double.valueOf(claims.get("id").toString()).longValue());

        baseLoginInfo.setUsername((String) claims.get("username"));
        baseLoginInfo.setTimeZone(ContextHolder.get(HeaderConst.TIME_ZONE));
        baseLoginInfo.setIp(ContextHolder.get(HeaderConst.IP));
        baseLoginInfo.setUserType(userType);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ContextHolder.removeAll();
    }
}

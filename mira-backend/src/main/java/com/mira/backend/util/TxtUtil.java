package com.mira.backend.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-12-22
 **/
@Slf4j
public class TxtUtil {

    public List<Long> readUserIds() throws IOException {
        String resourcePath = "file/menual_user_id.txt";
        List<Long> userIds = new ArrayList<>();

        // 使用 try-with-resources 语句确保 InputStream 被正确关闭
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourcePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                // 将字符串解析为 long 值并添加到列表中
                userIds.add(Long.parseLong(line));
            }
        } catch (NumberFormatException e) {
            throw new IOException("Error parsing userID to long: ", e);
        }
        return userIds;
    }

    public static void main(String[] args) {
        TxtUtil reader = new TxtUtil();
        try {
            List<Long> userIds = reader.readUserIds();
            // 打印出读取的用户 ID 列表
            userIds.forEach(System.out::println);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

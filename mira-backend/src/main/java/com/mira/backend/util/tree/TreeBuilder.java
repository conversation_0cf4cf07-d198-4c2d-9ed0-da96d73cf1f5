package com.mira.backend.util.tree;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 生成树形结构，使用方法：
 * List<TreeNode<T extends ParentIdEntity>> treeNodes = new TreeBuilder<T extends ParentIdEntity>().buildTree(tList);
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-05-08
 **/
public class TreeBuilder<T extends ParentIdEntity> {

    public List<TreeNode<T>> buildTree(List<T> dataList) {
        Map<Object, TreeNode<T>> nodeMap = new HashMap<>();

        // Step 1: Create tree nodes
        for (T data : dataList) {
            Object id = getId(data);
            nodeMap.put(id, new TreeNode<>(data));
        }

        // Step 2: Link tree nodes
        for (T data : dataList) {
            Object id = getId(data);
            Object parentId = getParentId(data);

            if (parentId.equals(0L)) {
                continue; // Root node
            }

            TreeNode<T> parent = nodeMap.get(parentId);
            TreeNode<T> child = nodeMap.get(id);

            if (parent != null && child != null) {
                parent.addChild(child);
            }
        }

        // Step 3: Find root nodes
        List<TreeNode<T>> roots = new ArrayList<>();

        for (TreeNode<T> node : nodeMap.values()) {
            if (getParentId(node.getData()).equals(0L)) {
                roots.add(node);
            }
        }

        return roots;
    }

    /**
     * 假设数据对象所在类的父类为ParentIdEntity，则使用该方法获取属性的值。
     *
     * @param data
     * @return
     */
    private Object getId(T data) {
        try {
            Field field = data.getClass().getSuperclass().getDeclaredField("id");
            field.setAccessible(true);
            return field.get(data);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get id from data object", e);
        }
    }

    private Object getParentId(T data) {
        try {
            Field field = data.getClass().getSuperclass().getDeclaredField("parentId");
            field.setAccessible(true);
            return field.get(data);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get id from data object", e);
        }
    }
}

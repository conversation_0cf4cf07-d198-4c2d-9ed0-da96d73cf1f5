package com.mira.backend.util;

import java.security.SecureRandom;
import java.util.*;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-06-15
 **/
public class PasswordGenerator {
    private static final String UPPER_CASE_LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWER_CASE_LETTERS = "abcdefghijklmnopqrstuvwxyz";
    private static final String NUMBERS = "0123456789";
    private static final String ALL_ALLOWED_CHARS = UPPER_CASE_LETTERS + LOWER_CASE_LETTERS + NUMBERS;

    /**
     * 生成一个随机密码
     *
     * @return 随机生成的密码
     */
    public static String generatePassword() {
        Random random = new Random();

        // 随机选取密码长度从9到15
        int length = 9 + random.nextInt(8);

        StringBuilder password = new StringBuilder(length);

        // 确保密码包括至少一个大写字母、一个小写字母和一个数字
        password.append(UPPER_CASE_LETTERS.charAt(random.nextInt(UPPER_CASE_LETTERS.length())));
        password.append(LOWER_CASE_LETTERS.charAt(random.nextInt(LOWER_CASE_LETTERS.length())));
        password.append(NUMBERS.charAt(random.nextInt(NUMBERS.length())));

        // 剩余的部分用所有允许的字符随机填充
        for (int i = 3; i < length; i++) {
            password.append(ALL_ALLOWED_CHARS.charAt(random.nextInt(ALL_ALLOWED_CHARS.length())));
        }

        // 将StringBuilder中的字符打乱顺序以增加随机性
        return shuffleString(password.toString());
    }

    /**
     * 打乱一个字符串。
     *
     * @param input 输入字符串
     * @return 打乱后的字符串
     */
    private static String shuffleString(String input) {
        List<Character> characters = new ArrayList<>();
        for (char c : input.toCharArray()) {
            characters.add(c);
        }
        Collections.shuffle(characters);
        StringBuilder shuffledString = new StringBuilder(input.length());
        for (char c : characters) {
            shuffledString.append(c);
        }
        return shuffledString.toString();
    }


    public static void main(String[] args) {
        for (int i = 0; i < 12; i++) {
            String password = generatePassword();
            System.out.println(i + ":Password: " + password);
        }
    }
}

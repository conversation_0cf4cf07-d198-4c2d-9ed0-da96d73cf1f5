package com.mira.backend.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CreateRoleDTO {
    @ApiModelProperty(value = "角色code", required = true)
    @NotBlank(message = "角色code不能为空")
    private String code;
    @ApiModelProperty(value = "角色名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    private String name;
    @ApiModelProperty(value = "角色英文名称", required = true)
    @NotBlank(message = "角色英文名称不能为空")
    private String nameEn;
    @ApiModelProperty(value = "角色描述", required = true)
    @NotBlank(message = "角色描述不能为空")
    private String description;
}

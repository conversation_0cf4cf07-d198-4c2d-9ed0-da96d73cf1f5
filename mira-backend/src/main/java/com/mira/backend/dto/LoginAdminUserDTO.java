package com.mira.backend.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
public class LoginAdminUserDTO implements Serializable {
    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String name;


    /**
     * 状态  0：禁用   1：正常
     */
    private Integer status;

    /**
     * 角色code
     */
    private Set<String> roleCode;

    /**
     * 权限code集合
     */
    private Set<String> permissionCodes;

}

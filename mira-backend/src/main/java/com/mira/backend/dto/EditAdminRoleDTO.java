package com.mira.backend.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class EditAdminRoleDTO {
    @ApiModelProperty(value = "管理员id", required = true)
    @NotNull(message = "管理员id不能为空")
    private Long adminId;
    @ApiModelProperty(value = "角色id集合", required = true)
    private List<Long> roleIds;
}

package com.mira.backend.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CreateSysConstantParam {
    @ApiModelProperty(value = "常量key", required = true)
    @NotBlank(message = "常量key不能为空")
    private String code;
    @ApiModelProperty(value = "常量value", required = true)
    @NotBlank(message = "常量value不能为空")
    private String value;
    @ApiModelProperty(value = "角色描述", required = true)
    @NotBlank(message = "角色描述不能为空")
    private String description;
}

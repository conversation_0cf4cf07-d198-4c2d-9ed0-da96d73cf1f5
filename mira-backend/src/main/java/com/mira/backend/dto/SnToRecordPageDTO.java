package com.mira.backend.dto;

import com.mira.core.request.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-09-14
 **/
@Data
@ApiModel(description = "SnToRecord分页请求")
public class SnToRecordPageDTO extends PageDTO {
    @ApiModelProperty(value = "sn", required = false)
    private String sn;

    /**
     * 批号
     */
    @ApiModelProperty(value = "batchId", required = false)
    private String batchId;

    /**
     * 去向
     */
    @ApiModelProperty(value = "direction", required = false)
    private String direction;

    /**
     * 发货日期
     */
    @ApiModelProperty(value = "deliverDate", required = false)
    private String deliverDate;

    /**
     * 内控编号
     */
    @ApiModelProperty(value = "controlId", required = false)
    private String controlId;
}

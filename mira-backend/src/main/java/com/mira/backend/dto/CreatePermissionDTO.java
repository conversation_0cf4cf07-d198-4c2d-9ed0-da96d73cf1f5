package com.mira.backend.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CreatePermissionDTO {
    @ApiModelProperty(value = "权限code", required = true)
    @NotBlank(message = "权限code不能为空")
    private String code;
    @ApiModelProperty(value = "类型：0：菜单权限；1：功能权限", required = true)
    @NotNull(message = "权限类型不能为空")
    @Max(value = 2, message = "权限类型只能为0或1或2")
    @Min(value = 0, message = "权限类型只能为0或1或2")
    //@Range(min = 0, max = 2, message = "权限类型只能为0或1或2")
    private Integer type;
    @ApiModelProperty(value = "权限权限描述", required = true)
    @NotBlank(message = "权限描述不能为空")
    private String description;
    @ApiModelProperty(value = "权限英文权限描述", required = true)
    @NotBlank(message = "权限英文描述不能为空")
    private String descriptionEn;
    @ApiModelProperty(value = "父权限id", required = false)
    private Long parentId;
}

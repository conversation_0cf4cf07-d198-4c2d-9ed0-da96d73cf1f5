package com.mira.backend.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AdminLoginDTO {
    @ApiModelProperty(value = "账号", required = true)
    @NotBlank(message = "Enter your name")
    private String name;
    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "Enter your password")
    private String password;

}

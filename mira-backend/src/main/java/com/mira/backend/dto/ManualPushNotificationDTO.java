package com.mira.backend.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-08-05
 **/
@Getter
@Setter
@ApiModel("手动推送通知")
public class ManualPushNotificationDTO {
    @ApiModelProperty("define id")
    @NotNull(message = "define id can not empty.")
    private Long defineId;

    @ApiModelProperty("静默通知，0-默认否，1-是")
    private Integer silent;

    @ApiModelProperty("用户id")
    @NotNull(message = "user id can not empty.")
    private Long userId;

    @ApiModelProperty("过期时间")
    private Long expireTimestamp;
}

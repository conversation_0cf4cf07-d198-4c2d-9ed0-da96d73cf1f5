package com.mira.backend.service;

import com.mira.backend.controller.vo.NotificationJobListVO;
import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.api.job.dto.NotificationPushPreviewDTO;
import com.mira.mybatis.response.PageResult;

/**
 * notification push interface
 *
 * <AUTHOR>
 */
public interface INotificationPushService {
    /**
     * 创建一个推送任务
     *
     * @param pushCreateDTO 参数
     */
    void create(NotificationPushCreateDTO pushCreateDTO);

    /**
     * 编辑推送任务
     *
     * @param pushCreateDTO 参数
     */
    void edit(NotificationPushCreateDTO pushCreateDTO);

    /**
     * 获取推送任务列表
     *
     * @param current 页码
     * @param size    数量
     * @return page
     */
    PageResult<NotificationJobListVO> list(long current, long size);

    /**
     * 激活推送任务
     *
     * @param taskId task id
     */
    void active(String taskId);

    /**
     * 取消激活推送任务
     *
     * @param taskId task id
     */
    void inactive(String taskId);

    /**
     * 删除推送任务
     *
     * @param taskId task id
     */
    void delete(String taskId);

    /**
     * 预览推送任务
     *
     * @param pushPreviewDTO param
     */
    void previews(NotificationPushPreviewDTO pushPreviewDTO);
}

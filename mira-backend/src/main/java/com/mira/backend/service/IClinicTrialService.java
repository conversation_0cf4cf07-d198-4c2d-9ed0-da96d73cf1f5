package com.mira.backend.service;

import com.mira.backend.pojo.dto.ClinicTrialBatchDTO;
import com.mira.backend.pojo.dto.ClinicTrialUserDTO;
import com.mira.backend.pojo.param.user.ClinicTrialPageParam;
import com.mira.mybatis.response.PageResult;

import java.util.List;
import java.util.Set;

/**
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2023-08-23 14:28
 **/
public interface IClinicTrialService {
    PageResult<? extends ClinicTrialUserDTO> dataPage(ClinicTrialPageParam pageParam);

    List<ClinicTrialBatchDTO> listClinicTrialBatch(Set<String> tenantCodes);
}
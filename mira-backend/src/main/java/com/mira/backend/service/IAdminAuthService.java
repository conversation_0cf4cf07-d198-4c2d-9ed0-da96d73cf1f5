package com.mira.backend.service;

import com.mira.backend.dto.EditAdminRoleDTO;
import com.mira.backend.dto.EditRolePermissionDTO;
import com.mira.backend.pojo.vo.admin.AdminRoleVO;
import com.mira.backend.pojo.vo.admin.AdminUserVO;

import java.util.List;

/**
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2023-08-23 13:36
 **/
public interface IAdminAuthService {
    List<AdminUserVO> listAdmins();

    void editAdminRoles(EditAdminRoleDTO editAdminRoleDTO, String timeZone);

    List<AdminRoleVO> listRoles();

    void editRolePermission(EditRolePermissionDTO editRolePermissionDTO, String timeZone);

    Long create(String name, String realName, String timeZone);

    void delete(Long adminId, String timeZone);


}
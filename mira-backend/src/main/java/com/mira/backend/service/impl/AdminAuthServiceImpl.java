package com.mira.backend.service.impl;


import com.mira.api.iam.provider.IAuthProvider;
import com.mira.backend.dal.dao.admin.*;
import com.mira.backend.dal.entity.admin.*;
import com.mira.backend.dto.EditAdminRoleDTO;
import com.mira.backend.dto.EditRolePermissionDTO;
import com.mira.backend.exception.BackendException;
import com.mira.backend.manager.CacheManager;
import com.mira.backend.pojo.vo.admin.AdminPermissionVO;
import com.mira.backend.pojo.vo.admin.AdminRoleVO;
import com.mira.backend.pojo.vo.admin.AdminUserVO;
import com.mira.backend.service.IAdminAuthService;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-25
 **/
@Service
@Slf4j
public class AdminAuthServiceImpl implements IAdminAuthService {
    @Resource
    private AdminUserDAO adminUserDAO;
    @Resource
    private AdminRoleDAO adminRoleDAO;

    @Resource
    private AdminUserRoleDAO adminUserRoleDAO;

    @Resource
    private AdminRolePermissionDAO adminRolePermissionDAO;

    @Resource
    private AdminPermissionDAO adminPermissionDAO;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private IAuthProvider authProvider;

    @Override
    public List<AdminUserVO> listAdmins() {
        List<AdminUserEntity> userEntityList = adminUserDAO.list();
        List<AdminRoleEntity> roleEntityList = adminRoleDAO.list();
        List<AdminUserRoleEntity> adminUserRoleEntityList = adminUserRoleDAO.list();
        List<AdminUserVO> adminUserVOS = new ArrayList<>();
        for (AdminUserEntity adminUserEntity : userEntityList) {
            Long userEntityId = adminUserEntity.getId();
            AdminUserVO adminUserVO = new AdminUserVO();
            BeanUtils.copyProperties(adminUserEntity, adminUserVO);
            List<Long> roleIds = adminUserRoleEntityList.stream().filter(adminUserRoleEntity -> adminUserRoleEntity.getAdminId().equals(userEntityId)).map(AdminUserRoleEntity::getRoleId).collect(Collectors.toList());
            roleEntityList.stream().filter(adminRoleEntity -> roleIds.contains(adminRoleEntity.getId())).forEach(adminRoleEntity -> {
                AdminRoleVO adminRoleVO = new AdminRoleVO();
                BeanUtils.copyProperties(adminRoleEntity, adminRoleVO);
                adminUserVO.getRoles().add(adminRoleVO);
            });
            adminUserVOS.add(adminUserVO);
        }
        return adminUserVOS;
    }

    @Override
    public void editAdminRoles(EditAdminRoleDTO editAdminRoleDTO, String timeZone) {
        Long adminId = editAdminRoleDTO.getAdminId();
        List<Long> roleIds = editAdminRoleDTO.getRoleIds();
        List<AdminUserRoleEntity> dbAdminUserRoleEntities = adminUserRoleDAO.listByAdminId(adminId);
        List<Long> adminUserRoleIds = dbAdminUserRoleEntities
                .stream()
                .map(AdminUserRoleEntity::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(adminUserRoleIds)) {
            adminUserRoleDAO.removeByIds(adminUserRoleIds);
        }
        List<AdminUserRoleEntity> adminUserRoleEntities = new ArrayList<>();
        for (Long roleId : roleIds) {
            AdminUserRoleEntity adminUserRoleEntity = new AdminUserRoleEntity();
            adminUserRoleEntity.setRoleId(roleId);
            adminUserRoleEntity.setAdminId(adminId);
            long now = System.currentTimeMillis();
            adminUserRoleEntity.setTimeZone(timeZone);
            adminUserRoleEntity.setCreateTime(now);
            adminUserRoleEntity.setModifyTime(now);
            adminUserRoleEntity.setCreateTimeStr(ZoneDateUtil.format(timeZone, now, DatePatternConst.DATE_TIME_PATTERN));
            adminUserRoleEntity.setModifyTimeStr(adminUserRoleEntity.getCreateTimeStr());
            adminUserRoleEntities.add(adminUserRoleEntity);
        }
        adminUserRoleDAO.saveBatch(adminUserRoleEntities);
    }

    @Override
    public List<AdminRoleVO> listRoles() {
        List<AdminRoleEntity> roleEntityList = adminRoleDAO.list();
        List<AdminPermissionEntity> permissionEntityList = adminPermissionDAO.list();
        List<AdminRolePermissionEntity> adminRolePermissionEntityList = adminRolePermissionDAO.list();
        List<AdminRoleVO> adminRoleVOS = new ArrayList<>();
        for (AdminRoleEntity adminRoleEntity : roleEntityList) {
            Long roleEntityId = adminRoleEntity.getId();
            AdminRoleVO adminRoleVO = new AdminRoleVO();
            BeanUtils.copyProperties(adminRoleEntity, adminRoleVO);

            List<Long> permissionIds = adminRolePermissionEntityList.stream().filter(adminRolePermissionEntity -> adminRolePermissionEntity.getRoleId().equals(roleEntityId)).map(AdminRolePermissionEntity::getPermissionId).collect(Collectors.toList());
            permissionEntityList.stream().filter(adminPermissionEntity -> permissionIds.contains(adminPermissionEntity.getId())).forEach(adminPermissionEntity -> {
                AdminPermissionVO adminPermissionVO = new AdminPermissionVO();
                BeanUtils.copyProperties(adminPermissionEntity, adminPermissionVO);
                adminRoleVO.getPermissions().add(adminPermissionVO);
            });

            adminRoleVOS.add(adminRoleVO);
        }
        return adminRoleVOS;
    }

    @Override
    public void editRolePermission(EditRolePermissionDTO editRolePermissionDTO, String timeZone) {
        Long roleId = editRolePermissionDTO.getRoleId();
        List<Long> permissionIds = editRolePermissionDTO.getPermissionIds();
        List<AdminRolePermissionEntity> dbRolePermissionEntities = adminRolePermissionDAO.listByRoleId(roleId);
        List<Long> rolePermissionIds = dbRolePermissionEntities
                .stream().map(AdminRolePermissionEntity::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(rolePermissionIds)) {
            adminRolePermissionDAO.removeByIds(rolePermissionIds);
        }
        List<AdminRolePermissionEntity> adminRolePermissionEntities = new ArrayList<>();
        for (Long permissionId : permissionIds) {
            AdminRolePermissionEntity adminRolePermissionEntity = new AdminRolePermissionEntity();
            adminRolePermissionEntity.setRoleId(roleId);
            adminRolePermissionEntity.setPermissionId(permissionId);
            long now = System.currentTimeMillis();
            adminRolePermissionEntity.setTimeZone(timeZone);
            adminRolePermissionEntity.setCreateTime(now);
            adminRolePermissionEntity.setModifyTime(now);
            adminRolePermissionEntity.setCreateTimeStr(ZoneDateUtil.format(timeZone, now, DatePatternConst.DATE_TIME_PATTERN));
            adminRolePermissionEntity.setModifyTimeStr(adminRolePermissionEntity.getCreateTimeStr());
            adminRolePermissionEntities.add(adminRolePermissionEntity);
        }
        adminRolePermissionDAO.saveBatch(adminRolePermissionEntities);
    }

    @Override
    public Long create(String name, String realName, String timeZone) {
        AdminUserEntity adminUser = adminUserDAO.getByName(name);
        if (!ObjectUtils.isEmpty(adminUser)) {
            throw new BackendException("该admin用户已存在");
        }
        AdminUserEntity adminUserEntity = new AdminUserEntity();
        adminUserEntity.setName(name);
        adminUserEntity.setRealName(realName);
        adminUserEntity.setStatus(1);
        String salt = RandomStringUtils.randomAlphanumeric(20);
        String randomPw = "t12345678";
        adminUserEntity.setSalt(salt);
        adminUserEntity.setPassword(new Sha256Hash(randomPw, salt).toHex());

        Long operatorId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        adminUserEntity.setCreator(operatorId);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, adminUserEntity);
        adminUserDAO.save(adminUserEntity);
        return adminUserEntity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long adminId, String timeZone) {
        AdminUserEntity adminUserEntity = adminUserDAO.getById(adminId);
        if (ObjectUtils.isEmpty(adminUserEntity)) {
            throw new BackendException("该admin用户不存在");
        }
        if (adminUserEntity.getName().startsWith("xizhao.dai@")) {
            throw new BackendException("该admin用户不能被删除");
        }
        adminUserEntity.setStatus(0);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, adminUserEntity);
        Long operator = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        adminUserEntity.setModifier(operator);
        adminUserEntity.setDeleted(1);
        adminUserDAO.updateById(adminUserEntity);
        adminUserDAO.removeById(adminUserEntity);
        log.info("后台管理员：{}删除admin用户。用户:{}", ContextHolder.<BaseLoginInfo>getLoginInfo().getUsername(), adminId);

        //删除和角色关联关系
        List<AdminUserRoleEntity> adminUserRoleEntities = adminUserRoleDAO.listByAdminId(adminId);
        List<Long> adminUserRoleIds = adminUserRoleEntities.stream().map(AdminUserRoleEntity::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(adminUserRoleIds)) {
            adminUserRoleDAO.removeByIds(adminUserRoleIds);
        }

        //修改授权缓存
        cacheManager.deleteLoginAdminUserDTO(adminId);

        //清除登录token
        String adminMarkToken = cacheManager.getAdminMarkToken(adminId);
        if (StringUtils.isNotBlank(adminMarkToken)) {
            authProvider.deleteToken(adminMarkToken, UserTypeEnum.BACKEND_ADMIN.getType());
        }
    }
}

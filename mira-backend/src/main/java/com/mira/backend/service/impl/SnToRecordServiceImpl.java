package com.mira.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mira.backend.service.ISnToRecordService;
import com.mira.backend.dal.dao.user.SnToRecordDAO;
import com.mira.backend.dto.SnToRecordPageDTO;
import com.mira.backend.dal.entity.user.SnToRecordEntity;
import com.mira.backend.pojo.vo.user.SnToRecordVO;
import com.mira.mybatis.response.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-09-14
 **/
@Service
public class SnToRecordServiceImpl implements ISnToRecordService {
    @Resource
    private SnToRecordDAO snToRecordDAO;

    @Override
    public SnToRecordVO getBySn(String sn) {
        if (StringUtils.isBlank(sn)) {
            return null;
        }
        SnToRecordEntity snToRecordEntity = snToRecordDAO.getBySn(sn);
        if (ObjectUtils.isEmpty(snToRecordEntity)) {
            return null;
        }
        SnToRecordVO snToRecordVO = new SnToRecordVO();
        BeanUtils.copyProperties(snToRecordEntity, snToRecordVO);
        return snToRecordVO;
    }

    @Override
    public List<SnToRecordVO> listBySn(List<String> snlist) {
        if (CollectionUtils.isEmpty(snlist)) {
            return null;
        }
        QueryWrapper<SnToRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("sn", snlist);
        List<SnToRecordEntity> snToRecordEntities = snToRecordDAO.list(queryWrapper);
        if (CollectionUtils.isEmpty(snToRecordEntities)) {
            return null;
        }
        List<SnToRecordVO> snToRecordVOS = new ArrayList<>();
        for (SnToRecordEntity snToRecordEntity : snToRecordEntities) {
            SnToRecordVO snToRecordVO = new SnToRecordVO();
            BeanUtils.copyProperties(snToRecordEntity, snToRecordVO);
            snToRecordVOS.add(snToRecordVO);
        }

        return snToRecordVOS;
    }

    @Override
    public PageResult<SnToRecordVO> snToRecordPage(SnToRecordPageDTO pageDTO) {
        return snToRecordDAO.snToRecordPage(pageDTO);
    }
}

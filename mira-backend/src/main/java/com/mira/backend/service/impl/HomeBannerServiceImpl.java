package com.mira.backend.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.backend.consts.CommonStatusConsts;
import com.mira.backend.dal.entity.user.SysHomeBannerEntity;
import com.mira.backend.dal.mapper.user.HomeBannerMapper;
import com.mira.backend.exception.BackendException;
import com.mira.backend.handler.event.InParamValidHandle;
import com.mira.api.user.dto.backend.HomeBannerDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.StringListUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.redis.cache.RedisComponent;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Description banner 服务层
 */
@Service("deskHomeBannerService")
public class HomeBannerServiceImpl extends ServiceImpl<HomeBannerMapper, SysHomeBannerEntity> {
    @Resource
    private InParamValidHandle inParamValidHandle;
    @Resource
    private RedisComponent redisComponent;

    /**
     * groupUid 长度
     */
    private final static int GROUP_UID_LENGTH = 12;

    /**
     * 查询banner列表
     *
     * @return list
     */
    public List<HomeBannerDTO> listRecord() {
        // 结果
        List<HomeBannerDTO> result = new ArrayList<>();
        // 取数据
        Map<String, List<SysHomeBannerEntity>> collect = list().stream()
                .collect(Collectors.groupingBy(SysHomeBannerEntity::getGroupUid));
        // build并筛选
        for (List<SysHomeBannerEntity> list : collect.values()) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }

            boolean expire = false;
            HomeBannerDTO homeBannerDTO = new HomeBannerDTO();
            List<HomeBannerDTO.HomeBannerSubDTO> bannerSubVOList = new ArrayList<>();
            for (SysHomeBannerEntity bannerEntity : list) {
                if (StringUtils.isEmpty(homeBannerDTO.getGroupUid())) {
                    BeanUtils.copyProperties(bannerEntity, homeBannerDTO);
                }
                if (verifyExpire(bannerEntity)) {
                    expire = true;
                }
                HomeBannerDTO.HomeBannerSubDTO homeBannerSubVO = new HomeBannerDTO.HomeBannerSubDTO();
                BeanUtils.copyProperties(bannerEntity, homeBannerSubVO);
                if (StringUtils.isNotBlank(bannerEntity.getSurveyIdsStr())) {
                    homeBannerSubVO.setSurveyIds(StringListUtil.strToList(bannerEntity.getSurveyIdsStr(), ","));
                }
                if (StringUtils.isNotBlank(bannerEntity.getConditions())) {
                    List<Integer> integers = StringListUtil.strToIntegerList(bannerEntity.getConditions(), ",");
                    homeBannerSubVO.setHealthCondition(integers.toArray(new Integer[0]));
                }
                if (StringUtils.isNotBlank(bannerEntity.getAge())) {
                    List<Integer> integers = StringListUtil.strToIntegerList(bannerEntity.getAge(), ",");
                    homeBannerSubVO.setAge(integers.toArray(new Integer[0]));
                }
                String stories = bannerEntity.getStories();
                if (StringUtils.isNotBlank(stories)) {
                    homeBannerSubVO.setStories(stories.charAt(0) == '['
                            ? JsonUtil.toArray(stories, Object.class)
                            : JsonUtil.toObject(stories, Object.class));
                }
                if (StringUtils.isNotBlank(bannerEntity.getClinics())) {
                    List<HomeBannerDTO.ClinicDTO> bannerClinicDTOList = JsonUtil.toArray(bannerEntity.getClinics(),
                            HomeBannerDTO.ClinicDTO.class);
                    homeBannerSubVO.setClinics(bannerClinicDTOList);
                }
                bannerSubVOList.add(homeBannerSubVO);
            }

            homeBannerDTO.setExpire(expire ? CommonStatusConsts.EXPIRE : CommonStatusConsts.NO_EXPIRE);
            homeBannerDTO.setGroup(bannerSubVOList);
            result.add(homeBannerDTO);
        }
        // 排序并返回
        return result.stream()
                .sorted(Comparator.comparing(HomeBannerDTO::getOrders))
                .collect(Collectors.toList());
    }

    /**
     * 添加banner
     *
     * @param homeBannerDTO DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void addRecord(HomeBannerDTO homeBannerDTO) {
        // 参数校验
        inParamValidHandle.handle(homeBannerDTO);

        // 新增的banner排序值为1，在最前面，其他记录全部累加1
        String groupUid = simpleUUID();
        List<SysHomeBannerEntity> bannerEntityList = homeBannerDTO.getGroup().stream().map(o -> {
            SysHomeBannerEntity bannerEntity = new SysHomeBannerEntity();

            BeanUtils.copyProperties(o, bannerEntity);
            bannerEntity.setCreateTime(new Date());
            bannerEntity.setModifyTime(bannerEntity.getCreateTime());
            bannerEntity.setGroupUid(groupUid);
            bannerEntity.setOrders(1);
            if (Objects.nonNull(o.getHealthCondition())) {
                bannerEntity.setConditions(StringListUtil.listToString(List.of(o.getHealthCondition()), ","));
            }
            if (Objects.nonNull(o.getAge())) {
                bannerEntity.setAge(StringListUtil.listToString(List.of(o.getAge()), ","));
            }
            if (!CollectionUtils.isEmpty(o.getSurveyIds())) {
                bannerEntity.setSurveyIdsStr(StringListUtil.listToString(o.getSurveyIds(), ","));
            }
            if (ObjectUtils.isNotEmpty(o.getStories())) {
                bannerEntity.setStories(JsonUtil.toJson(o.getStories()));
            }
            if (!CollectionUtils.isEmpty(o.getClinics())) {
                bannerEntity.setClinics(JsonUtil.toJson(o.getClinics()));
            }

            checkCurrency(bannerEntity);
            return bannerEntity;
        }).collect(Collectors.toList());

        // incr orders for mysql
        update(Wrappers.<SysHomeBannerEntity>lambdaUpdate().setSql("orders = orders + 1"));
        // save mysql
        saveBatch(bannerEntityList);
        // update group uid status
        updateGroupUIDStatus(groupUid, CommonStatusConsts.IN_ACTIVE.toString());
    }

    /**
     * 编辑banner
     *
     * @param homeBannerDTO DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void editRecord(HomeBannerDTO homeBannerDTO) {
        // 参数校验
        inParamValidHandle.handle(homeBannerDTO);
        if (StringUtils.isEmpty(homeBannerDTO.getGroupUid())) {
            throw new BackendException("group uid cannot be empty");
        }

        // 原数据编号
        List<SysHomeBannerEntity> originBannerList = list(Wrappers.<SysHomeBannerEntity>lambdaQuery()
                .eq(SysHomeBannerEntity::getGroupUid, homeBannerDTO.getGroupUid()));
        if (CollectionUtils.isEmpty(originBannerList)) {
            throw new BackendException("This banner has been deleted or cannot be found");
        }
        List<Long> originBannerIdList = originBannerList.stream()
                .map(SysHomeBannerEntity::getId)
                .collect(Collectors.toList());

        // 待更新的数据
        List<SysHomeBannerEntity> updateList = homeBannerDTO.getGroup().stream()
                .filter(banner -> originBannerIdList.contains(banner.getId()))
                .map(banner -> {
                    SysHomeBannerEntity bannerEntity = new SysHomeBannerEntity();
                    BeanUtils.copyProperties(banner, bannerEntity);
                    if (Objects.nonNull(banner.getHealthCondition())) {
                        bannerEntity.setConditions(StringListUtil.listToString(List.of(banner.getHealthCondition()), ","));
                    }
                    if (Objects.nonNull(banner.getAge())) {
                        bannerEntity.setAge(StringListUtil.listToString(List.of(banner.getAge()), ","));
                    }
                    if (!CollectionUtils.isEmpty(banner.getSurveyIds())) {
                        bannerEntity.setSurveyIdsStr(StringListUtil.listToString(banner.getSurveyIds(), ","));
                    }
                    bannerEntity.setModifyTime(new Date());
                    checkCurrency(bannerEntity);
                    if (ObjectUtils.isNotEmpty(banner.getStories())) {
                        bannerEntity.setStories(JsonUtil.toJson(banner.getStories()));
                    }
                    bannerEntity.setClinics(!CollectionUtils.isEmpty(banner.getClinics())
                            ? JsonUtil.toJson(banner.getClinics()) : "");
                    return bannerEntity;
                }).collect(Collectors.toList());

        // 待删除的数据
        originBannerIdList.removeAll(homeBannerDTO.getGroup().stream()
                .map(HomeBannerDTO.HomeBannerSubDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));

        // 待新增的数据
        List<SysHomeBannerEntity> createList = homeBannerDTO.getGroup().stream()
                .filter(banner -> Objects.isNull(banner.getId()))
                .map(banner -> {
                    SysHomeBannerEntity bannerEntity = new SysHomeBannerEntity();
                    BeanUtils.copyProperties(banner, bannerEntity);
                    if (!CollectionUtils.isEmpty(banner.getSurveyIds())) {
                        bannerEntity.setSurveyIdsStr(StringListUtil.listToString(banner.getSurveyIds(), ","));
                    }
                    bannerEntity.setCreateTime(new Date());
                    bannerEntity.setModifyTime(bannerEntity.getCreateTime());
                    bannerEntity.setGroupUid(homeBannerDTO.getGroupUid());
                    bannerEntity.setOrders(originBannerList.get(0).getOrders());
                    bannerEntity.setStatus(getGroupUIDStatus(bannerEntity.getGroupUid()));
                    if (Objects.nonNull(banner.getHealthCondition())) {
                        bannerEntity.setConditions(StringListUtil.listToString(List.of(banner.getHealthCondition()), ","));
                    }
                    if (Objects.nonNull(banner.getAge())) {
                        bannerEntity.setAge(StringListUtil.listToString(List.of(banner.getAge()), ","));
                    }
                    checkCurrency(bannerEntity);
                    if (ObjectUtils.isNotEmpty(banner.getStories())) {
                        bannerEntity.setStories(JsonUtil.toJson(banner.getStories()));
                    }
                    bannerEntity.setClinics(!CollectionUtils.isEmpty(banner.getClinics())
                            ? JsonUtil.toJson(banner.getClinics()) : null);
                    return bannerEntity;
                }).collect(Collectors.toList());

        // update mysql
        if (!CollectionUtils.isEmpty(updateList)) {
            updateBatchById(updateList);
        }
        // delete mysql
        if (!CollectionUtils.isEmpty(originBannerIdList)) {
            update(Wrappers.<SysHomeBannerEntity>lambdaUpdate()
                    .set(SysHomeBannerEntity::getDeleted, CommonStatusConsts.DELETED)
                    .in(SysHomeBannerEntity::getId, originBannerIdList));
        }
        // add mysql
        if (!CollectionUtils.isEmpty(createList)) {
            saveBatch(createList);
        }
    }

    /**
     * 删除banner
     *
     * @param ids banner编号列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteRecord(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // delete mysql
        update(Wrappers.<SysHomeBannerEntity>lambdaUpdate()
                .set(SysHomeBannerEntity::getDeleted, CommonStatusConsts.DELETED)
                .in(SysHomeBannerEntity::getId, ids));
    }

    /**
     * 修改排序
     * 根据元素顺序更新排序
     *
     * @param groupUidList 所有banner的groupUid
     */
    @Transactional(rollbackFor = Exception.class)
    public void editOrders(List<String> groupUidList) {
        // 排序重置，从1开始
        List<SysHomeBannerEntity> bannerEntityList = list();
        for (SysHomeBannerEntity banner : bannerEntityList) {
            banner.setOrders(groupUidList.indexOf(banner.getGroupUid()) + 1);
        }

        // 更新排序
        updateBatchById(bannerEntityList);
    }

    /**
     * 激活banner
     *
     * @param groupUid 组唯一编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void active(String groupUid) {
        List<SysHomeBannerEntity> bannerEntityList = list(Wrappers.<SysHomeBannerEntity>lambdaQuery()
                .eq(SysHomeBannerEntity::getGroupUid, groupUid));
        bannerEntityList.forEach(banner -> banner.setStatus(CommonStatusConsts.ACTIVE));
        updateBatchById(bannerEntityList);

        // update group uid status
        updateGroupUIDStatus(groupUid, CommonStatusConsts.ACTIVE.toString());
    }

    /**
     * banner设为未激活
     *
     * @param groupUid 组唯一编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void inactive(String groupUid) {
        List<SysHomeBannerEntity> bannerEntityList = list(Wrappers.<SysHomeBannerEntity>lambdaQuery()
                .eq(SysHomeBannerEntity::getGroupUid, groupUid));
        bannerEntityList.forEach(banner -> banner.setStatus(CommonStatusConsts.IN_ACTIVE));
        updateBatchById(bannerEntityList);

        // update group uid status
        updateGroupUIDStatus(groupUid, CommonStatusConsts.IN_ACTIVE.toString());
    }

    /**
     * 更新groupUID状态
     *
     * @param groupUID group uid
     * @param status   激活状态
     */
    private void updateGroupUIDStatus(String groupUID, String status) {
        redisComponent.hSet(RedisCacheKeyConst.HOME_BANNER_GROUPUID, groupUID, status);
    }

    /**
     * 获取groupUID状态
     *
     * @param groupUID group uid
     * @return status
     */
    private Integer getGroupUIDStatus(String groupUID) {
        String status = redisComponent.hGet(RedisCacheKeyConst.HOME_BANNER_GROUPUID, groupUID, String.class);
        if (StringUtils.isEmpty(status) || "null".equals(status)) {
            return CommonStatusConsts.IN_ACTIVE;
        }
        return Integer.valueOf(status);
    }

    /**
     * 是否设置了币种
     *
     * @param homeBannerEntity SysHomeBannerEntity
     */
    private void checkCurrency(SysHomeBannerEntity homeBannerEntity) {
        if (StringUtils.isNotEmpty(homeBannerEntity.getCurrency())) {
            homeBannerEntity.setCurrencyFlag(1);
        }
    }

    /**
     * 生成groupUID
     *
     * @return groupUID
     */
    private String simpleUUID() {
        // 去除-符号，取前12位
        String uuid = UUID.randomUUID().toString();
        uuid = uuid.replaceAll("-", "");
        return uuid.substring(0, GROUP_UID_LENGTH);
    }

    /**
     * 检查未激活、未开始和已过期的记录
     *
     * @param bannerEntity banner
     * @return true/false
     */
    public boolean verifyInvalid(SysHomeBannerEntity bannerEntity) {
        String zone = "America/Los_Angeles";
        long currentTime = System.currentTimeMillis();
        // 未激活
        Predicate<SysHomeBannerEntity> conditionInActive = (banner) -> banner.getStatus().equals(CommonStatusConsts.IN_ACTIVE);
        // 未开始
        Predicate<SysHomeBannerEntity> conditionNotStart = (banner) -> {
            long startTime = ZoneDateUtil.timestamp(zone, banner.getStartTime(), DatePatternConst.DATE_TIME_PATTERN);
            return currentTime < startTime;
        };
        // 已过期
        Predicate<SysHomeBannerEntity> conditionExpire = (banner) -> {
            long endTime = ZoneDateUtil.timestamp(zone, banner.getEndTime(), DatePatternConst.DATE_TIME_PATTERN);
            return currentTime > endTime;
        };

        boolean result = conditionInActive.test(bannerEntity);
        if (StringUtils.isNotEmpty(bannerEntity.getStartTime())) {
            result = result || conditionNotStart.test(bannerEntity);
        }
        if (StringUtils.isNotEmpty(bannerEntity.getEndTime())) {
            result = result || conditionExpire.test(bannerEntity);
        }
        return result;
    }

    /**
     * 校验是否已经失效过期
     *
     * @param bannerEntity banner
     * @return true 过期
     */
    private boolean verifyExpire(SysHomeBannerEntity bannerEntity) {
        String zone = "America/Los_Angeles";
        if (StringUtils.isNotBlank(bannerEntity.getEndTime())) {
            long currentTime = System.currentTimeMillis();
            long endTime = ZoneDateUtil.timestamp(zone, bannerEntity.getEndTime(), DatePatternConst.DATE_TIME_PATTERN);
            return currentTime > endTime;
        }
        return false;
    }
}

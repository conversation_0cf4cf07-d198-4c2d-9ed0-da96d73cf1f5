package com.mira.backend.service;

import com.mira.backend.dto.SnToRecordPageDTO;
import com.mira.backend.pojo.vo.user.SnToRecordVO;
import com.mira.mybatis.response.PageResult;

import java.util.List;

/**
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2023-09-14 10:36
 **/
public interface ISnToRecordService {
    SnToRecordVO getBySn(String sn);

    List<SnToRecordVO> listBySn(List<String> snlist);

    PageResult<SnToRecordVO> snToRecordPage(SnToRecordPageDTO pageDTO);
}
package com.mira.backend.service.impl;

import com.mira.api.message.dto.PushNotificationDTO;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.backend.dto.ManualPushNotificationDTO;
import com.mira.backend.service.IManualNotificationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-08-05
 **/
@Service
public class ManualNotificationServiceImpl implements IManualNotificationService {
    @Resource
    private IMessageProvider messageProvider;
    @Resource
    private ISsoProvider ssoProvider;

    @Override
    public void pushNotification(ManualPushNotificationDTO manualPushNotificationDTO) {
        Long userId = manualPushNotificationDTO.getUserId();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();

        PushNotificationDTO pushNotificationDTO = new PushNotificationDTO()
                .setUserId(userId)
                .setTimeZone(loginUserInfoDTO.getTimeZone())
                .setPlatform(loginUserInfoDTO.getPlatform())
                .setDefineId(manualPushNotificationDTO.getDefineId())
                .setPushFirebase(Boolean.TRUE)
                .setSaveRecord(Boolean.TRUE)
                .setTokens(Collections.singletonList(loginUserInfoDTO.getPushToken()));

//        if (manualPushNotificationDTO.getExpireTimestamp() != 0
//                || manualPushNotificationDTO.getExpireTimestamp() != null) {
            pushNotificationDTO.setExpireTime(manualPushNotificationDTO.getExpireTimestamp());
//        }

        if (1 == manualPushNotificationDTO.getSilent()) {
            pushNotificationDTO.setSilent(true);
        }

        messageProvider.sendNotification(pushNotificationDTO);
    }
}

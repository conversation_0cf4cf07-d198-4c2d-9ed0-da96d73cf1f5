package com.mira.backend.service;

import com.mira.api.clinic.dto.*;

/**
 * desk clinic service
 *
 * <AUTHOR>
 */
public interface IClinicService {
    void initCreateClinic(InitCreateClinicDTO initCreateClinicDTO);

    void inviteClinic(InviteClinicDTO inviteClinicDTO, int sendFlag);

    ClinicPageResponseDTO clinicPage(ClinicPageRequestDTO clinicPageRequestDTO);

    ClinicPatientPageResponseDTO clinicPatientPage(ClinicPatientPageRequestDTO clinicPatientPageRequestDTO);

    void editClinicInfo(EditClinicDTO editClinicDTO);

    void deleteClinicInfo(Long id);

    void resetClinicPassword(String email);
}

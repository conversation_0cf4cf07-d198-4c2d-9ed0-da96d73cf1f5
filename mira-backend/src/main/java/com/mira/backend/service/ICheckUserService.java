package com.mira.backend.service;

import com.mira.api.user.dto.user.UserBindLogDTO;
import com.mira.backend.pojo.param.user.ModifyEmailParam;
import com.mira.backend.pojo.vo.user.UserInfoVO;
import com.mira.backend.pojo.vo.user.UserSearchVO;

import java.util.List;

/**
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2023-07-10 14:25
 **/
public interface ICheckUserService {
    String createCheckToken(Long userId) throws Exception;

    String createCheckTokenV2(String email) throws Exception;

    List<UserSearchVO> searchDeskUser(String keyword);

    UserInfoVO info(Long userId);

    void resetPwd(Long userId);

    void enable(Long userId);

    Long delete(Long userId);

    Long modifyEmail(ModifyEmailParam modifyEmailParam);

    List<UserBindLogDTO> list10BindLog(String email, String code);

    void addTestingScheduleExchange(Long userId);


}
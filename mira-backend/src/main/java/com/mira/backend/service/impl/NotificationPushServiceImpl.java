package com.mira.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.job.dto.NotificationJobPageDTO;
import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.api.job.dto.NotificationPushJobDTO;
import com.mira.api.job.dto.NotificationPushPreviewDTO;
import com.mira.api.job.provider.IJobProvider;
import com.mira.backend.controller.vo.NotificationJobListVO;
import com.mira.backend.service.INotificationPushService;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.mybatis.response.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * notification push interface impl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotificationPushServiceImpl implements INotificationPushService {
    @Resource
    private IJobProvider jobProvider;

    @Override
    public void create(NotificationPushCreateDTO pushCreateDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        NotificationPushJobDTO notificationPushJobDTO = new NotificationPushJobDTO();
        notificationPushJobDTO.setPushCreateDTO(pushCreateDTO);
        notificationPushJobDTO.setAdminId(loginInfo.getId());
        notificationPushJobDTO.setAdminUsername(loginInfo.getUsername());
        jobProvider.addJob(notificationPushJobDTO);
    }

    @Override
    public void edit(NotificationPushCreateDTO pushCreateDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        NotificationPushJobDTO notificationPushJobDTO = new NotificationPushJobDTO();
        notificationPushJobDTO.setTaskId(pushCreateDTO.getTaskId());
        notificationPushJobDTO.setPushCreateDTO(pushCreateDTO);
        notificationPushJobDTO.setAdminId(loginInfo.getId());
        notificationPushJobDTO.setAdminUsername(loginInfo.getUsername());
        jobProvider.updateJob(notificationPushJobDTO);
    }

    @Override
    public PageResult<NotificationJobListVO> list(long current, long size) {
        NotificationJobPageDTO notificationJobPageDTO = jobProvider.listJob(current, size).getData();
        long recordCount = notificationJobPageDTO.getRecordCount();
        List<NotificationJobListVO> jobListVOList = notificationJobPageDTO.getJobListDTOS().stream()
                .map(dto -> BeanUtil.toBean(dto, NotificationJobListVO.class))
                .collect(Collectors.toList());

        return new PageResult<>(jobListVOList, recordCount, size, current);
    }

    @Override
    public void active(String taskId) {
        NotificationPushJobDTO notificationPushJobDTO = new NotificationPushJobDTO();
        notificationPushJobDTO.setTaskId(taskId);
        jobProvider.startJob(notificationPushJobDTO);
    }

    @Override
    public void inactive(String taskId) {
        NotificationPushJobDTO notificationPushJobDTO = new NotificationPushJobDTO();
        notificationPushJobDTO.setTaskId(taskId);
        jobProvider.stopJob(notificationPushJobDTO);
    }

    @Override
    public void delete(String taskId) {
        NotificationPushJobDTO notificationPushJobDTO = new NotificationPushJobDTO();
        notificationPushJobDTO.setTaskId(taskId);
        jobProvider.removeJob(notificationPushJobDTO);
    }

    @Override
    public void previews(NotificationPushPreviewDTO pushPreviewDTO) {
        jobProvider.preview(pushPreviewDTO);
    }
}

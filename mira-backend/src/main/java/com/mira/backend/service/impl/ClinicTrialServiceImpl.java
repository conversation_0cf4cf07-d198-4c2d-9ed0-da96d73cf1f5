package com.mira.backend.service.impl;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleAnalysisDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.user.dto.backend.MoodsAndSymptomsCountDTO;
import com.mira.api.user.dto.user.UserPeriodDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.api.user.provider.IUserToBackendProvider;
import com.mira.backend.dal.mapper.user.ClinicTrialMapper;
import com.mira.backend.manager.CacheManager;
import com.mira.backend.pojo.dto.ClinicTrialBatchDTO;
import com.mira.backend.pojo.dto.ClinicTrialUserDTO;
import com.mira.backend.pojo.param.user.ClinicTrialPageParam;
import com.mira.backend.pojo.vo.user.HcgClinicTrialUserVO;
import com.mira.backend.pojo.vo.user.MoodAndWellBeingTrialUserVO;
import com.mira.backend.service.IClinicTrialService;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.AgeUtil;
import com.mira.core.util.JsonUtil;
import com.mira.mybatis.response.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-25
 **/
@Service
public class ClinicTrialServiceImpl implements IClinicTrialService {
    @Resource
    private IUserToBackendProvider userToBackendProvider;
    @Resource
    private IUserProvider userProvider;
    @Resource
    private ClinicTrialMapper clinicTrialMapper;
    @Resource
    private CacheManager cacheManager;

    @Override
    public PageResult<? extends ClinicTrialUserDTO> dataPage(ClinicTrialPageParam pageParam) {
        Integer currPage = pageParam.getCurrent();
        Integer pageSize = pageParam.getSize();
        String keyword = pageParam.getKeyword();
        int currIndex = (currPage - 1) * pageSize;

        Integer batch = pageParam.getBatch();
        Integer type = pageParam.getType();
        Integer count = null;
        List<ClinicTrialUserDTO> clinicTrialUserDTOS = null;
        if (type == null || type == 0) {
            count = clinicTrialMapper.countHcgClinicTrialUserDTO(keyword, batch);
            clinicTrialUserDTOS = clinicTrialMapper.pageHcgClinicTrialUserDTO(currIndex, pageSize, keyword, batch);
        } else if (type == 1) {
            count = clinicTrialMapper.countMoodAndWellBeingTrialUserDTO(keyword, batch);
            clinicTrialUserDTOS = clinicTrialMapper.pageMoodAndWellBeingTrialUserDTO(currIndex, pageSize, keyword, batch);
        }
        List<Long> userIds = clinicTrialUserDTOS.stream().map(ClinicTrialUserDTO::getId).collect(Collectors.toList());
        if (userIds.isEmpty()) {
            return new PageResult<>(new ArrayList<>(), count, pageParam.getSize(), pageParam.getCurrent());
        }
        PageResult<? extends ClinicTrialUserDTO> pageResult = null;
        if (type == null || type == 0) {
            List<HcgClinicTrialUserVO> hcgClinicTrialUserVOS = getHcgClinicTrialUserVOS(clinicTrialUserDTOS);
            pageResult = new PageResult<>(hcgClinicTrialUserVOS, count, pageParam.getSize(), pageParam.getCurrent());
        } else if (type == 1) {
            List<MoodAndWellBeingTrialUserVO> moodAndWellBeingTrialUserVOS = getMoodAndWellBeingTrialUserVOS(clinicTrialUserDTOS);
            pageResult = new PageResult<>(moodAndWellBeingTrialUserVOS, count, pageParam.getSize(), pageParam.getCurrent());
        }
        return pageResult;
    }

    private List<MoodAndWellBeingTrialUserVO> getMoodAndWellBeingTrialUserVOS(List<ClinicTrialUserDTO> clinicTrialUserDTOS) {
        List<MoodAndWellBeingTrialUserVO> moodAndWellBeingTrialUserVOS = new ArrayList<>();
        for (ClinicTrialUserDTO clinicTrialUserDTO : clinicTrialUserDTOS) {
            MoodAndWellBeingTrialUserVO moodAndWellBeingTrialUserVO = new MoodAndWellBeingTrialUserVO();
            BeanUtils.copyProperties(clinicTrialUserDTO, moodAndWellBeingTrialUserVO);
            Long userId = moodAndWellBeingTrialUserVO.getId();
            MoodsAndSymptomsCountDTO moodsAndSymptomsCountDTO = userToBackendProvider.getMoodsAndSymptomsCount(userId).getData();
            moodAndWellBeingTrialUserVO.setCountOfMoodAndWellBeing(moodsAndSymptomsCountDTO.getDiaryMoodsCount());
            moodAndWellBeingTrialUserVO.setCountOfSymptoms(moodsAndSymptomsCountDTO.getDiarySymptomsCount());
            moodAndWellBeingTrialUserVOS.add(moodAndWellBeingTrialUserVO);
        }
        return moodAndWellBeingTrialUserVOS;
    }

    private List<HcgClinicTrialUserVO> getHcgClinicTrialUserVOS(List<ClinicTrialUserDTO> clinicTrialUserDTOS) {
        List<HcgClinicTrialUserVO> hcgClinicTrialUserVOS = new ArrayList<>();
        List<Long> userIds = clinicTrialUserDTOS.stream().map(ClinicTrialUserDTO::getId).collect(Collectors.toList());
        List<AlgorithmResultDTO> algorithmResultDTOS = new ArrayList<>();
        for (Long userId : userIds) {
            AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
            algorithmResultDTOS.add(algorithmResultDTO);
        }

        List<UserPeriodDTO> userPeriodDTOS = userProvider.listPeriodByUserIds(new HashSet<>(userIds)).getData();
        for (Long userId : userIds) {
            ClinicTrialUserDTO clinicTrialUserDTO = clinicTrialUserDTOS.stream().filter(clinicTrialUser -> clinicTrialUser.getId().equals(userId)).findFirst().orElse(null);
            if (clinicTrialUserDTO == null) {
                continue;
            }
            HcgClinicTrialUserVO hcgClinicTrialUserVO = new HcgClinicTrialUserVO();
            BeanUtils.copyProperties(clinicTrialUserDTO, hcgClinicTrialUserVO);
            String birthday = clinicTrialUserDTO.getBirthYear() + "-" + clinicTrialUserDTO.getBirthMonth() + "-" + clinicTrialUserDTO.getBirthOfDay();
            hcgClinicTrialUserVO.setBirthday(birthday);
            hcgClinicTrialUserVO.setAge(AgeUtil.calculateAge(clinicTrialUserDTO.getBirthYear(), clinicTrialUserDTO.getBirthMonth(), clinicTrialUserDTO.getBirthOfDay()));

            AlgorithmResultDTO algorithmResultDTO = algorithmResultDTOS
                    .stream()
                    .filter(algorithmResult -> algorithmResult.getUserId().equals(hcgClinicTrialUserVO.getId()))
                    .findFirst()
                    .orElse(null);
            int resultsCount = 0;
            int countOfHcg = 0;
            AtomicReference<Integer> countOfPositiveHcg = new AtomicReference<>(0);
            if (algorithmResultDTO != null) {
                List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
                List<HormoneDTO> validHormoneDTOS =
                        hormoneDatas.stream()
                                .filter(hormoneDTO -> hormoneDTO.getFlag() == 1)
                                .collect(Collectors.toList());
                resultsCount = validHormoneDTOS.size();
                List<HormoneDTO> validHcgHormoneDTOS =
                        validHormoneDTOS.stream()
                                .filter(hormoneDTO -> WandTypeEnum.HCG_QUALITATIVE.getInteger().equals(hormoneDTO.getTest_results().getWand_type()))
                                .collect(Collectors.toList());
                countOfHcg = validHcgHormoneDTOS.size();
                validHcgHormoneDTOS.stream()
                        .forEach(
                                hormoneDTO -> {
                                    Float value1 = hormoneDTO.getTest_results().getValue1();
                                    if (value1 >= 10) {
                                        countOfPositiveHcg.getAndSet(countOfPositiveHcg.get() + 1);
                                    }
                                }
                        );

                String cycleAnalysisStr = algorithmResultDTO.getCycleAnalysis();
                CycleAnalysisDTO cycleAnalysis = JsonUtil.toObject(cycleAnalysisStr, CycleAnalysisDTO.class);
                if (cycleAnalysis != null) {
                    hcgClinicTrialUserVO.setAnalysisLenCycle(cycleAnalysis.getCycle_len());
                    hcgClinicTrialUserVO.setAnalysisLenPeriod(cycleAnalysis.getPeriod_len());
                }
            }
            hcgClinicTrialUserVO.setResultsCount(resultsCount);
            hcgClinicTrialUserVO.setCountOfHcg(countOfHcg);
            hcgClinicTrialUserVO.setCountOfPositiveHcg(countOfPositiveHcg.get());

            userPeriodDTOS.stream()
                    .filter(userPeriodDTO -> userPeriodDTO.getUserId().equals(hcgClinicTrialUserVO.getId()))
                    .findFirst()
                    .ifPresent(
                            userPeriodDTO -> {
                                hcgClinicTrialUserVO.setAvgLenPeriod(userPeriodDTO.getAvgLenPeriod());
                                hcgClinicTrialUserVO.setAvgLenCycle(userPeriodDTO.getAvgLenCycle());
                            }
                    );

            hcgClinicTrialUserVOS.add(hcgClinicTrialUserVO);
        }
        return hcgClinicTrialUserVOS;
    }

    @Override
    public List<ClinicTrialBatchDTO> listClinicTrialBatch(Set<String> tenantCodes) {
        return clinicTrialMapper.listClinicTrialBatch(tenantCodes);
    }
}

package com.mira.backend.service.impl;

import com.mira.api.iam.dto.AuthTokenDTO;
import com.mira.api.iam.provider.IAuthProvider;
import com.mira.api.user.consts.UserStatusConst;
import com.mira.backend.consts.DemoUserConsts;
import com.mira.backend.dal.dao.admin.*;
import com.mira.backend.dal.entity.admin.*;
import com.mira.backend.dto.AdminLoginDTO;
import com.mira.backend.dto.LoginAdminUserDTO;
import com.mira.backend.dto.ModifyPwdDTO;
import com.mira.backend.exception.BackendException;
import com.mira.backend.manager.CacheManager;
import com.mira.backend.service.IAdminLoginService;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.util.PasswordUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.web.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 后台人员登录接口实现
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-06
 **/
@Slf4j
@Service
public class AdminLoginServiceImpl implements IAdminLoginService {
    @Resource
    private AdminUserDAO adminUserDAO;
    @Resource
    private AdminRoleDAO adminRoleDAO;

    @Resource
    private AdminUserRoleDAO adminUserRoleDAO;

    @Resource
    private AdminRolePermissionDAO adminRolePermissionDAO;
    @Resource
    private AdminPermissionDAO adminPermissionDAO;
    @Resource
    private AdminOperateLogDAO adminOperateLogDAO;

    @Resource
    private IAuthProvider authProvider;
    @Resource
    private CacheManager cacheManager;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String login(AdminLoginDTO adminLoginDTO) throws Exception {
        //用户信息
        AdminUserEntity adminUserEntity = adminUserDAO.getByName(adminLoginDTO.getName());
        if (Objects.isNull(adminUserEntity)) {
            throw new BackendException("The backend account doesn't exist.");
        }
        checkStatus(adminUserEntity);
        // 密码校验
        if (!PasswordUtil.match(adminLoginDTO.getPassword(), adminUserEntity.getPassword(), adminUserEntity.getSalt())) {
            throw new BackendException("Email address or password don't match.");
        }

        String method = "com.mira.backend.desk.controller.common.AdminUserController.login()";
        String params = adminLoginDTO.getName();
        String operate = "管理员登录";
        saveAdminOperateLog(method, params, operate);

        // id-uuid
        return getAdminToken(adminUserEntity, adminLoginDTO);
    }

    private String getAdminToken(AdminUserEntity adminUserEntity, AdminLoginDTO adminLoginDTO) throws Exception {
        // 删除旧令牌
        String oldToken = cacheManager.getAdminMarkToken(adminUserEntity.getId());
        if (StringUtils.isNotBlank(oldToken)) {
            authProvider.deleteToken(oldToken, UserTypeEnum.BACKEND_ADMIN.getType());
        }
        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.BACKEND_ADMIN.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(adminLoginDTO.getName(), adminLoginDTO.getPassword());
        return adminUserEntity.getId().toString().concat("-").concat(tokenResult.getData().getAccess_token());
    }

    private void checkStatus(AdminUserEntity adminUserEntity) {
        if (UserStatusConst.DISABLE == adminUserEntity.getStatus()) {
            throw new BackendException("Account has been locked, please contact the administrator");
        }
        if (UserStatusConst.INACTIVE == adminUserEntity.getStatus()) {
            throw new BackendException("Please check your email to activate your Mira account.");
        }
    }

    private void saveAdminOperateLog(String method, String params, String operate) {
        AdminOperateLogEntity adminLogEntity = new AdminOperateLogEntity();
        adminLogEntity.setIp(ContextHolder.get(HeaderConst.IP));
        adminLogEntity.setCreateTime(new Date());
        adminLogEntity.setModifyTime(new Date());
        adminLogEntity.setMethod(method);
        adminLogEntity.setParams(params);
        adminLogEntity.setOperation(operate);
        adminOperateLogDAO.save(adminLogEntity);
    }

    @Override
    public LoginAdminUserDTO adminInfo() {
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        LoginAdminUserDTO loginAdminUserDTO = new LoginAdminUserDTO();
        loginAdminUserDTO.setId(adminId);

        LoginAdminUserDTO loginAdminUserDTOCache = cacheManager.getLoginAdminUserDTOCache(adminId);
        if (Objects.nonNull(loginAdminUserDTOCache)) {
            return loginAdminUserDTOCache;
        }

        AdminUserEntity adminUser = adminUserDAO.getById(adminId);
        loginAdminUserDTO.setName(adminUser.getName());
        loginAdminUserDTO.setStatus(adminUser.getStatus());

        List<AdminUserRoleEntity> adminUserRoleEntities = adminUserRoleDAO.listByAdminId(adminId);
        if (!CollectionUtils.isEmpty(adminUserRoleEntities)) {
            Set<Long> roleIds = adminUserRoleEntities.stream().map(AdminUserRoleEntity::getRoleId).collect(Collectors.toSet());
            Collection<AdminRoleEntity> adminRoleEntities = adminRoleDAO.listByIds(roleIds);
            Set<String> roleCodes = adminRoleEntities.stream().map(AdminRoleEntity::getCode).collect(Collectors.toSet());
            loginAdminUserDTO.setRoleCode(roleCodes);
            Set<AdminRolePermissionEntity> totalRolePermissionEntities = new HashSet<>();
            for (Long roleId : roleIds) {
                Collection<AdminRolePermissionEntity> adminRolePermissionEntities =
                        adminRolePermissionDAO.listByRoleId(roleId);
                if (!CollectionUtils.isEmpty(adminRolePermissionEntities)) {
                    totalRolePermissionEntities.addAll(adminRolePermissionEntities);
                }
            }
            if (!CollectionUtils.isEmpty(totalRolePermissionEntities)) {
                Set<Long> permissionIds = totalRolePermissionEntities.stream().map(AdminRolePermissionEntity::getPermissionId).collect(Collectors.toSet());
                Collection<AdminPermissionEntity> adminPermissionEntities = adminPermissionDAO.listByIds(permissionIds);
                Set<String> permissionCodes = adminPermissionEntities.stream().map(AdminPermissionEntity::getCode).collect(Collectors.toSet());
                loginAdminUserDTO.setPermissionCodes(permissionCodes);
            }
        }
        cacheManager.cacheLoginAdminUserDTO(adminId, loginAdminUserDTO);
        return loginAdminUserDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logout() {
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        //删除缓存，清理权限
        cacheManager.deleteLoginAdminUserDTO(adminId);
        //删除token
        List<String> tokenHeaderList = UserTypeEnum.BACKEND_ADMIN.getTokenHeaderList();
        for (String tokenHeader : tokenHeaderList) {
            String authorization = RequestUtil.getRequest().getHeader(tokenHeader);
            if (StringUtils.isNotEmpty(authorization)) {
                authProvider.deleteToken(authorization, UserTypeEnum.BACKEND_ADMIN.getType());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPwd(ModifyPwdDTO modifyPwdDTO) {
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AdminUserEntity adminUserEntity = adminUserDAO.getById(adminId);
        if (Objects.isNull(adminUserEntity)) {
            throw new BackendException("The backend account doesn't exist.");
        }

        String oldPassword = modifyPwdDTO.getOldPwd();
        String newPassword = modifyPwdDTO.getNewPwd();

        if (!PasswordUtil.match(oldPassword, adminUserEntity.getPassword(), adminUserEntity.getSalt())) {
            throw new BackendException("Old password incorrect.");
        }

        adminUserEntity.setSalt(PasswordUtil.generateSalt(20));
        adminUserEntity.setPassword(PasswordUtil.encryptPassword(newPassword, adminUserEntity.getSalt()));
        UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), adminUserEntity);
        adminUserDAO.updateById(adminUserEntity);

        // 记录日志
        String method = "com.mira.backend.desk.controller.common.AdminUserController.resetPwd()";
        String params = "adminId:" + adminId + ",name:" + ContextHolder.<BaseLoginInfo>getLoginInfo().getUsername();
        String operate = "修改管理员密码";
        saveAdminOperateLog(method, params, operate);
    }

    @Override
    public void checkUserInfoPermission(Long userId) {
        LoginAdminUserDTO adminUserDTO = this.adminInfo();
        Set<String> permissionCodes = adminUserDTO.getPermissionCodes();
        Set<String> checkTokenRangeSet = permissionCodes
                .stream()
                .filter(permissionCode -> permissionCode.startsWith("data:checkToken:"))
                .map(permissionCode -> permissionCode.substring("data:checkToken:".length()))
                .collect(Collectors.toSet());
        boolean permission = false;
        if (checkTokenRangeSet.contains("PCOSdemo")) {
            //可以查看 demo用户
            if (DemoUserConsts.HEALTHY_USERIDS.contains(userId) || DemoUserConsts.PCOS_USERIDS.contains(userId)) {
                permission = true;
            }
        } else if (checkTokenRangeSet.contains("QC")) {
            //可以查看 QC用户
            if (DemoUserConsts.QC_USERIDS.contains(userId)) {
                permission = true;
            }
        }
        if (checkTokenRangeSet.contains("all")) {
            //可以查看所有用户
            permission = true;
        }
        if (!permission) {
            throw new BackendException("without permission: data:checkToken");
        }
    }
}

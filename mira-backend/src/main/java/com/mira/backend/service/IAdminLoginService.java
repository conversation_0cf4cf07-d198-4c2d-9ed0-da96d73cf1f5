package com.mira.backend.service;

import com.mira.backend.dto.AdminLoginDTO;
import com.mira.backend.dto.LoginAdminUserDTO;
import com.mira.backend.dto.ModifyPwdDTO;

/**
 * @program: mira_server_microservices
 * @description: 后台人员登录接口
 * @author: xizhao.dai
 * @create: 2023-07-06 13:48
 **/
public interface IAdminLoginService {
    /**
     * 后台人员密码登录
     *
     * @param adminLoginDTO 登录信息
     * @return 令牌
     */
    String login(AdminLoginDTO adminLoginDTO) throws Exception;

    LoginAdminUserDTO adminInfo();

    void logout();

    void resetPwd(ModifyPwdDTO modifyPwdDTO);

    /**
     * 查询是否有获取用户信息的权限
     *
     * @param userId
     */
    void checkUserInfoPermission(Long userId);
}
package com.mira.backend.dal.mapper.admin;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.backend.dal.entity.admin.AdminRoleEntity;
import com.mira.backend.pojo.vo.admin.AdminRoleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AdminRoleMapper extends BaseMapper<AdminRoleEntity> {

    List<AdminRoleVO> viewAdminRoles(@Param("adminId") Long adminId);
}

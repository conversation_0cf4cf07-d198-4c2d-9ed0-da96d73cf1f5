package com.mira.backend.dal.dao.admin;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.job.consts.JobStatusConsts;
import com.mira.backend.dal.entity.admin.AdminNotificationJobEntity;
import com.mira.backend.dal.mapper.admin.AdminNotificationJobMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AdminNotificationJobDAO extends ServiceImpl<AdminNotificationJobMapper, AdminNotificationJobEntity> {
    public List<AdminNotificationJobEntity> jobList(long currIndex, long pageSize) {
        return list(Wrappers.<AdminNotificationJobEntity>lambdaQuery()
                .last("limit " + currIndex + "," + pageSize)
                .orderByDesc(AdminNotificationJobEntity::getJobType)
                .orderByDesc(AdminNotificationJobEntity::getCreateTime));
    }

    public List<AdminNotificationJobEntity> jobListByStart() {
        // 查询启动中的任务列表
        return list(Wrappers.<AdminNotificationJobEntity>lambdaQuery()
                .eq(AdminNotificationJobEntity::getJobStatus, JobStatusConsts.STARTING));
    }

    public AdminNotificationJobEntity getByTaskId(String taskId) {
        return getOne(Wrappers.<AdminNotificationJobEntity>lambdaQuery()
                .eq(AdminNotificationJobEntity::getTaskId, taskId));
    }
}

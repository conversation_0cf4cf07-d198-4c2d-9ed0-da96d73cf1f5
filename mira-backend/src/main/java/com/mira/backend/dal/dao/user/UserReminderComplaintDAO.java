package com.mira.backend.dal.dao.user;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.backend.dal.entity.user.UserReminderComplaintEntity;
import com.mira.backend.dal.mapper.user.UserReminderComplaintMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-02-27
 **/
@Repository
public class UserReminderComplaintDAO extends ServiceImpl<UserReminderComplaintMapper, UserReminderComplaintEntity> {
    /**
     * 校验用户是否在testing schedule exchange 列表中
     *
     * @param userId
     * @return true or false
     */
    public Boolean inReminderComplaint(Long userId) {
        // 用户是否在 user_reminder_compaint 表中
        Long count =
                baseMapper.selectCount(Wrappers.<UserReminderComplaintEntity>lambdaQuery().eq(UserReminderComplaintEntity::getUserId, userId));
        Boolean inReminderComplaint = Boolean.FALSE;
        if (count > 0) {
            inReminderComplaint = Boolean.TRUE;
        }
        return inReminderComplaint;
    }

    /**
     * 将用户添加到testing schedule exchange 列表中
     *
     * @param userId
     */
    public void addTestingScheduleExchange(Long userId) {
        UserReminderComplaintEntity userReminderComplaintEntity = baseMapper.selectOne(Wrappers.<UserReminderComplaintEntity>lambdaQuery().eq(UserReminderComplaintEntity::getUserId,
                userId));
        if (userReminderComplaintEntity == null) {
            userReminderComplaintEntity = new UserReminderComplaintEntity();
            userReminderComplaintEntity.setUserId(userId);
            baseMapper.insert(userReminderComplaintEntity);
        }
    }
}

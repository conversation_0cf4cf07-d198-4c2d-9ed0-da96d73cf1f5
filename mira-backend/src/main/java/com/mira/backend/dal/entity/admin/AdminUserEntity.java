package com.mira.backend.dal.entity.admin;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 系统管理用户
 */
@TableName("admin_user")
@Data
public class AdminUserEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String name;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 盐
     */
    @NotBlank(message = "盐不能为空")
    private String salt;


    /**
     * 状态  0：禁用   1：正常	2:未激活
     */
    @NotBlank(message = "状态不能为空")
    private Integer status;


}

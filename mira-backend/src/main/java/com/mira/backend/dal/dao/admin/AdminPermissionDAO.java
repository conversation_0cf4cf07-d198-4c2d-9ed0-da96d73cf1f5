package com.mira.backend.dal.dao.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.backend.dal.entity.admin.AdminPermissionEntity;
import com.mira.backend.dal.mapper.admin.AdminPermissionMapper;
import com.mira.backend.exception.BackendException;
import com.mira.backend.pojo.vo.admin.AdminPermissionVO;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-06
 **/
@Repository
public class AdminPermissionDAO extends ServiceImpl<AdminPermissionMapper, AdminPermissionEntity> {
    public List<AdminPermissionVO> listVO() {
        QueryWrapper<AdminPermissionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("type");
        List<AdminPermissionEntity> permissionEntityList = this.list(queryWrapper);
        List<AdminPermissionVO> adminPermissionVOS = new ArrayList<>();
        for (AdminPermissionEntity adminPermissionEntity : permissionEntityList) {
            AdminPermissionVO adminPermissionVO = new AdminPermissionVO();
            BeanUtils.copyProperties(adminPermissionEntity, adminPermissionVO);
            adminPermissionVOS.add(adminPermissionVO);
        }
        return adminPermissionVOS;
    }

    public AdminPermissionEntity getByCode(String code) {
        QueryWrapper<AdminPermissionEntity> adminPermissionEntityQueryWrapper = new QueryWrapper<>();
        adminPermissionEntityQueryWrapper.eq("code", code);
        AdminPermissionEntity dbPermissionEntity = this.getOne(adminPermissionEntityQueryWrapper);
        return dbPermissionEntity;
    }

    public List<AdminPermissionVO> viewRolePermissions(Long roleId) {
        return baseMapper.viewRolePermissions(roleId);
    }

    public Long create(String code, Integer type, String description, String descriptionEn, Long parentId,
                       String timeZone) {
        AdminPermissionEntity dbPermissionEntity = this.getByCode(code);
        if (!ObjectUtils.isEmpty(dbPermissionEntity)) {
            throw new BackendException("该权限code已存在");
        }
        AdminPermissionEntity adminPermissionEntity = new AdminPermissionEntity();
        adminPermissionEntity.setCode(code);
        adminPermissionEntity.setDescription(description);
        adminPermissionEntity.setDescriptionEn(descriptionEn);
        adminPermissionEntity.setType(type);
        adminPermissionEntity.setParentId(parentId == null ? -1 : parentId);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, adminPermissionEntity);
        this.save(adminPermissionEntity);
        return adminPermissionEntity.getId();
    }
}

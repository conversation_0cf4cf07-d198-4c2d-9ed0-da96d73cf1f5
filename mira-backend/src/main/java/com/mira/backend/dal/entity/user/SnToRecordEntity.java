package com.mira.backend.dal.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 仪器去向管理
 */
@Data
@TableName("sn_to_record")
public class SnToRecordEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 序号
     */
    private Long sequence;

    /**
     * sn
     */
    private String sn;

    /**
     * 批号
     */
    private String batchId;

    /**
     * 去向
     */
    private String direction;

    /**
     * 发货日期
     */
    private String deliverDate;

    /**
     * 内控编号
     */
    private String controlId;

    /**
     * 国家/地区
     */
    private String area;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 仓库
     */
    private String warehouse;

    @ApiModelProperty(value = "创建者 默认0代表系统创建")
    private Long creator;

    @ApiModelProperty(value = "修改者 默认0代表系统修改")
    private Long modifier;

    @TableLogic
    @ApiModelProperty(value = "状态 0 正常状态 1 删除状态")
    private Integer deleted;

    /*
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    protected Date createTime;

    /*
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Date modifyTime;

}

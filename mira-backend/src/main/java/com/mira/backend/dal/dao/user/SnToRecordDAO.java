package com.mira.backend.dal.dao.user;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.backend.dal.entity.user.SnToRecordEntity;
import com.mira.backend.dal.mapper.user.SnToRecordMapper;
import com.mira.backend.dto.SnToRecordPageDTO;
import com.mira.backend.pojo.vo.user.SnToRecordVO;
import com.mira.core.consts.DatePatternConst;
import com.mira.mybatis.response.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class SnToRecordDAO extends ServiceImpl<SnToRecordMapper, SnToRecordEntity> {
    public SnToRecordEntity getBySn(String sn) {
        QueryWrapper<SnToRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", sn);
        SnToRecordEntity snToRecordEntity = this.getOne(queryWrapper);
        return snToRecordEntity;
    }

    public PageResult<SnToRecordVO> snToRecordPage(SnToRecordPageDTO pageDTO) {
        LambdaQueryWrapper<SnToRecordEntity> lambdaQuery = Wrappers.<SnToRecordEntity>lambdaQuery();
        if (StringUtils.isNotBlank(pageDTO.getSn())) {
            lambdaQuery.likeRight(SnToRecordEntity::getSn, pageDTO.getSn());
        }
        if (StringUtils.isNotBlank(pageDTO.getDirection())) {
            lambdaQuery.eq(SnToRecordEntity::getDirection, pageDTO.getDirection());
        }
        if (StringUtils.isNotBlank(pageDTO.getControlId())) {
            lambdaQuery.eq(SnToRecordEntity::getControlId, pageDTO.getControlId());
        }
        if (StringUtils.isNotBlank(pageDTO.getBatchId())) {
            lambdaQuery.eq(SnToRecordEntity::getBatchId, pageDTO.getBatchId());
        }
        if (StringUtils.isNotBlank(pageDTO.getDeliverDate())) {
            lambdaQuery.eq(SnToRecordEntity::getDeliverDate, pageDTO.getDeliverDate());
        }
        lambdaQuery.orderByDesc(SnToRecordEntity::getId);
        Page<SnToRecordEntity> page = page(new Page<>(pageDTO.getCurrent(), pageDTO.getSize()), lambdaQuery);
        List<SnToRecordEntity> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageResult<>();
        }
        List<SnToRecordVO> snToRecordVOS = new ArrayList<>();
        for (SnToRecordEntity snToRecordEntity : records) {
            SnToRecordVO snToRecordVO = new SnToRecordVO();
            BeanUtil.copyProperties(snToRecordEntity, snToRecordVO);
            Date createTime = snToRecordEntity.getCreateTime();
            snToRecordVO.setCreateTimeStr(new SimpleDateFormat(DatePatternConst.DATE_TIME_PATTERN).format(createTime));
            snToRecordVOS.add(snToRecordVO);
        }
        return new PageResult<>(snToRecordVOS, page.getTotal(), page.getSize(), page.getCurrent());
    }
}

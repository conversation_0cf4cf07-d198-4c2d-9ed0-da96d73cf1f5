package com.mira.backend.dal.dao.user;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.backend.dal.entity.user.SysHomeBannerEntity;
import com.mira.backend.dal.mapper.user.SysHomeBannerMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * sys_home_banner DAO
 *
 * <AUTHOR>
 */
@Repository
public class SysHomeBannerDAO extends ServiceImpl<SysHomeBannerMapper, SysHomeBannerEntity> {
    public List<SysHomeBannerEntity> findByGroupId(String groupId) {
        return list(Wrappers.<SysHomeBannerEntity>lambdaQuery()
                .eq(SysHomeBannerEntity::getGroupUid, groupId));
    }
}

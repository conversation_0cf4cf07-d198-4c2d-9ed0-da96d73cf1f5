package com.mira.backend.dal.entity.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户通知提醒开关,这里只记录更新的内容
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2021-08-05
 **/
@TableName("app_user_reminder")
@Data
public class AppUserReminderEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 通知标示:0 no remind;1remind
     */
    private Integer remindFlag;

    /**
     * 隐藏通知内容标识，默认0关闭，1开启
     */
    private Integer hideContentFlag;

    /**
     * Cycle Phase 通知标识，默认0关闭，1开启
     */
    private Integer cyclePhaseFlag;

    /**
     * Purchase 购买建议通知标识，默认0关闭，1开启
     */
    private Integer purchaseFlag;

    /**
     * 隐藏购买建议通知开关，0-关闭，1-打开,默认关闭
     */
    private Integer hidePurchaseFlag;


    /**
     * 测试日schedule总开关，0-关闭，不执行，1-按照schedule执行(默认1)
     */
    private Integer testingScheduleFlag;
    /**
     * 温度测试推送总开关，0-关闭，1-打开(默认0)
     */
    private Integer bbtTestingFlag;

    /**
     * 隐藏温度测试推送开关，0-关闭，1-打开
     */
    private Integer hideBbtFlag;

    /**
     * 测试日schedule通知时间戳
     */
    private Long testingScheduleRemindTime;

    /**
     * 测试日schedule通知时间
     */
    private String testingScheduleRemindTimeStr;


    /**
     * BBT测试日通知时间戳
     */
    private Long bbtTestingRemindTime;

    /**
     * BBT测试日通知时间（不含年月日）
     */
    private String bbtTestingRemindTimeStr;


    /*******************************************/


    /**
     * 通知时间
     */
    @Deprecated
    private Long remindTime;

    /**
     * 通知时间
     */
    @Deprecated
    private String remindTimeStr;

    /**
     * 测试日推送总开关，0-关闭，1-打开(默认1)
     */
    @Deprecated
    private Integer testingDayFlag;

    /**
     * Fertility Plus 试剂测试通知开关，默认0关闭，1开启
     */
    @Deprecated
    private Integer fertilityPlusWands;

    /**
     * Fertility Confirm 试剂测试通知开关，默认0关闭，1开启
     */
    @Deprecated
    private Integer fertilityConfirmWands;

    /**
     * Max 试剂测试通知开关，默认0关闭，1开启
     */
    @Deprecated
    private Integer maxWands;

    /**
     * Ovum 试剂测试通知开关，默认0关闭，1开启
     */
    @Deprecated
    private Integer ovumWands;
}

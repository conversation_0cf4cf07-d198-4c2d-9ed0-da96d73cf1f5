package com.mira.backend.dal.entity.admin;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Data;

import java.io.Serializable;

@TableName("admin_user_role")
@Data
public class AdminUserRoleEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 管理员id
     */
    private Long adminId;

    /**
     * 创建者 默认0代表系统创建
     */
    private Long creator;

    /**
     * 修改者 默认0代表系统修改
     */
    private Long modifier;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建时间
     */
    private String createTimeStr;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 修改时间
     */
    private String modifyTimeStr;

    /**
     * 时区
     */
    private String timeZone;


}

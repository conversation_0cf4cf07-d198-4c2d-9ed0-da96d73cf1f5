package com.mira.backend.dal.entity.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 首页banner
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2021-11-17
 **/
@TableName("sys_home_banner")
@Data
public class SysHomeBannerEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 展示顺序，数值越小越靠前(可为空)
     */
    private Integer orders;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容标题
     */
    private String mainTitle;

    /**
     * 文字颜色
     */
    private String fontColor;

    /**
     * 背景色
     */
    private String backgroundColor;

    /**
     * 首页banner中的图片
     */
    private String bannerImage;

    /**
     * banner弹窗的主图
     */
    private String mainImage;

    /**
     * 跳转链接地址
     */
    private String link;

    /**
     * 类型：1:产品promo；2:article
     */
    private Integer type;

    /**
     * 用户类型列表:1:ALL;2:Did not connect Mira;3:TTA;4:TTC
     */
    private String userTypes;

    /**
     * 绑定校验：0不需要校验；1需要历史没有绑定过；2需要历史绑定过
     */
    private Integer bindCheck;

    /**
     * 图片是否覆盖整个banner，默认false:0
     */
    private Integer imageCover;

    /**
     * 是否跟货币相关：0：无关；1：有关
     */
    private Integer currencyFlag;

    /**
     * Currency
     */
    private String currency;

    /**
     * 状态 0 正常状态 1 删除状态
     */
    @TableLogic
    private Integer deleted;

    /**
     * 内容按钮
     */
    private String mainButtonText;

    /**
     * 描述
     */
    private String description;

    /**
     * showIf 与 notShowIf
     */
    private String showIf;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 系统操作备注
     */
    private String sysNote;

    /**
     * 所属组的标识
     */
    private String groupUid;

    /**
     * 开始时间（USD）
     */
    private String startTime;

    /**
     * 结束时间（USD）
     */
    private String endTime;

    /**
     * 激活状态，0未激活，1激活中
     */
    private Integer status;

    /**
     * 图片padding
     */
    private String bannerImagePadding;

    /**
     * 是否弹窗，0不弹窗，1弹窗
     */
    private Integer hasModal;

    /**
     * List<String> surveyIds的字符串拼接列表
     */
    private String surveyIdsStr;

    /**
     * 是否跟踪更年期，null无标识;0 不跟踪;1跟踪
     */
    private Integer trackingMenopause;

    /**
     * H5用扩展字段
     */
    private String mixedString;

    /**
     * H5 Stories
     */
    private String stories;

    /**
     * conditions
     */
    private String conditions;

    /**
     * age
     */
    private String age;

    /**
     * 诊所列表
     */
    private String clinics;
}

package com.mira.backend.dal.entity.admin;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Data;

import java.io.Serializable;

@TableName("admin_role")
@Data
public class AdminRoleEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 角色code
     */
    private String code;

    /**
     * 角色名称
     */
    private String name;
    private String nameEn;

    /**
     * 角色描述
     */
    private String description;


}

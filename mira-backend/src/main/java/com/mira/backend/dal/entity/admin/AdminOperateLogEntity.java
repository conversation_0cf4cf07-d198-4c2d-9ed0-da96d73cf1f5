/**
 * Copyright 2018 renren opensource http://www.renren.io
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package com.mira.backend.dal.entity.admin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 *
 */
@TableName("admin_operate_log")
@Data
public class AdminOperateLogEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;

    //管理员id
    @TableField("admin_id")
    private Long adminId;
    //管理员name
    @TableField("name")
    private String name;
    //用户操作
    @TableField("operation")
    private String operation;
    //请求方法
    @TableField("method")
    private String method;
    //请求参数
    @TableField("params")
    private String params;
    //执行时长(毫秒)
    @TableField("time")
    private Long time;
    //IP地址
    @TableField("ip")
    private String ip;

    @ApiModelProperty(value = "创建者 默认0代表系统创建")
    private Long creator;

    @ApiModelProperty(value = "修改者 默认0代表系统修改")
    private Long modifier;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}

package com.mira.backend.dal.dao.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.backend.dal.entity.admin.AdminRolePermissionEntity;
import com.mira.backend.dal.mapper.admin.AdminRolePermissionMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-06
 **/
@Repository
public class AdminRolePermissionDAO extends ServiceImpl<AdminRolePermissionMapper, AdminRolePermissionEntity> {
    public List<AdminRolePermissionEntity> listByRoleId(Long roleId) {
        QueryWrapper<AdminRolePermissionEntity> adminPermissionEntityQueryWrapper = new QueryWrapper<>();
        adminPermissionEntityQueryWrapper.eq("role_id", roleId);
        List<AdminRolePermissionEntity> rolePermissionEntityList = this.list(adminPermissionEntityQueryWrapper);
        return rolePermissionEntityList;
    }
}

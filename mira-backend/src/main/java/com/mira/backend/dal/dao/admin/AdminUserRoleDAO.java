package com.mira.backend.dal.dao.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.backend.dal.entity.admin.AdminUserRoleEntity;
import com.mira.backend.dal.mapper.admin.AdminUserRoleMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-06
 **/
@Repository
public class AdminUserRoleDAO extends ServiceImpl<AdminUserRoleMapper, AdminUserRoleEntity> {
    public List<AdminUserRoleEntity> listByAdminId(Long adminId) {
        QueryWrapper<AdminUserRoleEntity> userRoleEntityQueryWrapper = new QueryWrapper<>();
        userRoleEntityQueryWrapper.eq("admin_id", adminId);
        List<AdminUserRoleEntity> adminUserRoleEntities = this.list(userRoleEntityQueryWrapper);
        return adminUserRoleEntities;
    }
}

package com.mira.backend.dal.mapper.admin;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.backend.dal.entity.admin.AdminPermissionEntity;
import com.mira.backend.pojo.vo.admin.AdminPermissionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AdminPermissionMapper extends BaseMapper<AdminPermissionEntity> {

    List<AdminPermissionVO> viewRolePermissions(@Param("roleId") Long roleId);
}

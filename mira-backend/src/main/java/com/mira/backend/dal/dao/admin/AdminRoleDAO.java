package com.mira.backend.dal.dao.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.backend.dal.entity.admin.AdminRoleEntity;
import com.mira.backend.dal.mapper.admin.AdminRoleMapper;
import com.mira.backend.exception.BackendException;
import com.mira.backend.pojo.vo.admin.AdminRoleVO;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-06
 **/
@Repository
public class AdminRoleDAO extends ServiceImpl<AdminRoleMapper, AdminRoleEntity> {
    public AdminRoleEntity getByCode(String code) {
        QueryWrapper<AdminRoleEntity> adminRoleEntityQueryWrapper = new QueryWrapper<>();
        adminRoleEntityQueryWrapper.eq("code", code);
        AdminRoleEntity dbRoleEntity = this.getOne(adminRoleEntityQueryWrapper);
        return dbRoleEntity;
    }

    public List<AdminRoleVO> viewAdminRoles(Long adminId) {
        return baseMapper.viewAdminRoles(adminId);
    }

    public Long create(String code, String name,String nameEn, String description, String timeZone) {
        AdminRoleEntity dbRoleEntity = this.getByCode(code);
        if (!ObjectUtils.isEmpty(dbRoleEntity)) {
            throw new BackendException("该角色code已存在");
        }
        AdminRoleEntity adminRoleEntity = new AdminRoleEntity();
        adminRoleEntity.setCode(code);
        adminRoleEntity.setDescription(description);
        adminRoleEntity.setName(name);
        adminRoleEntity.setNameEn(nameEn);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, adminRoleEntity);
        this.save(adminRoleEntity);
        return adminRoleEntity.getId();
    }
}

package com.mira.backend.dal.dao.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.backend.dal.entity.admin.AdminUserEntity;
import com.mira.backend.dal.mapper.admin.AdminUserMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-06
 **/
@Repository
public class AdminUserDAO extends ServiceImpl<AdminUserMapper, AdminUserEntity> {
    public AdminUserEntity getByName(String name) {
        AdminUserEntity adminUserEntity = this.getOne(new QueryWrapper<AdminUserEntity>().eq("name", name));
        return adminUserEntity;
    }
}

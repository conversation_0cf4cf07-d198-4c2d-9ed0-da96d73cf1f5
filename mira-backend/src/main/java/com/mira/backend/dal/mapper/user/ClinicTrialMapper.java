package com.mira.backend.dal.mapper.user;

import com.mira.backend.pojo.dto.ClinicTrialBatchDTO;
import com.mira.backend.pojo.dto.ClinicTrialUserDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @program: mira_server
 * @description:
 * @author: xizhao.dai
 * @create: 2023-04-25 16:17
 **/
@Mapper
public interface ClinicTrialMapper {
    /**
     * 诊所临床的所有用户分页列表
     *
     * @param currIndex
     * @param pageSize
     * @param keyWord
     * @param batch
     * @return
     */
    List<ClinicTrialUserDTO> pageHcgClinicTrialUserDTO(@Param("currIndex") int currIndex,
                                                       @Param("pageSize") int pageSize,
                                                       @Param("keyWord") String keyWord,
                                                       @Param("batch") int batch);

    /**
     * 诊所临床的所有用户
     *
     * @param
     * @param keyWord
     * @param batch
     * @return
     */
    Integer countHcgClinicTrialUserDTO(@Param("keyWord") String keyWord, @Param("batch") int batch);

    List<ClinicTrialUserDTO> pageMoodAndWellBeingTrialUserDTO(@Param("currIndex") int currIndex,
                                                       @Param("pageSize") int pageSize,
                                                       @Param("keyWord") String keyWord,
                                                       @Param("batch") int batch);

    Integer countMoodAndWellBeingTrialUserDTO(@Param("keyWord") String keyWord, @Param("batch") int batch);

    List<ClinicTrialBatchDTO> listClinicTrialBatch(@Param("tenantCodes") Set<String> tenantCodes);

}
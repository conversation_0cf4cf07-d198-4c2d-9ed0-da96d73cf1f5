package com.mira.backend.dal.entity.admin;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Getter
@Setter
@TableName("admin_notification_job")
public class AdminNotificationJobEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * Task ID
     */
    private String taskId;

    /**
     * define ID
     */
    private Long defineId;

    /**
     * Admin ID
     */
    private Long adminId;

    /**
     * 后台人员名字
     */
    private String adminName;

    /**
     * 开始执行时间
     */
    private String startTime;

    /**
     * 任务参数详情
     */
    private String jobJson;

    /**
     * 任务状态，0-未启动，1-启动中，3-已完成
     */
    private Integer jobStatus;

    /**
     * 任务类型，0-一次性，1-固定频率
     */
    private Integer jobType;

    /**
     * Xxl job的任务id
     */
    private Integer xxljobId;
}

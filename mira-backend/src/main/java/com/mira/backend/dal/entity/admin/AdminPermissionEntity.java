package com.mira.backend.dal.entity.admin;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Data;

import java.io.Serializable;

@TableName("admin_permission")
@Data
public class AdminPermissionEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long parentId;


    /**
     * 权限code
     */
    private String code;

    /**
     * 类型：0：菜单权限；1：功能权限
     */
    private Integer type;

    /**
     * 权限描述
     */
    private String description;
    /**
     * 权限英文描述
     */
    private String descriptionEn;


}

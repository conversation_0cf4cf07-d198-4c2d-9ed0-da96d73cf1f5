package com.mira.backend.manager;

import com.mira.backend.properties.DelayedTaskProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class DelayedTaskScheduler {
    private ThreadPoolTaskScheduler taskScheduler;

    @Resource
    private DelayedTaskProperties delayedTaskProperties;

    // 存储未来任务引用的map（可用于取消、检查）
    private final ConcurrentHashMap<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    // 初始化调度器
    @PostConstruct
    public void init() {
        taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(50); // 可调
        taskScheduler.setRemoveOnCancelPolicy(true); // 自动移除取消的任务
        taskScheduler.setWaitForTasksToCompleteOnShutdown(true);
        taskScheduler.setThreadNamePrefix("DelayedTask-");
        taskScheduler.initialize();
    }

    /**
     * 添加一个将在一小时后执行的任务
     * @param taskId 唯一任务标识
     * @param task 要执行的任务
     * @return 是否添加成功
     */
    public boolean addTask(String taskId, Runnable task) {
        // 检查已有相同任务是否存在
        if (scheduledTasks.containsKey(taskId)) {
            log.info("手动添加数据：Task already exists with ID: " + taskId);
            return false;
        }

        // 包装原始任务，加上任务完成时的清理逻辑
        Runnable wrappedTask = () -> {
            try {
                task.run();
            } catch (Exception e) {
                log.error("手动添加数据：Task scheduled 任务执行异常: {}", taskId, e);
            } finally {
                // 执行完成后移除任务记录
                scheduledTasks.remove(taskId);
            }
        };

        // 60分钟后执行
        Integer delayedTime = delayedTaskProperties.getDelayedTime();
        long delay = TimeUnit.MINUTES.toMillis(delayedTime); // 3600000ms
        Instant executionTime = Instant.now().plusMillis(delay);

        ScheduledFuture<?> future = taskScheduler.schedule(wrappedTask, executionTime);

        // 放入 map 管理
        scheduledTasks.put(taskId, future);

        log.info("手动添加数据：Task scheduled: " + taskId + ", will execute in "+delayedTime+" MINUTES.");
        return true;
    }

    /**
     * 取消一个已调度的任务
     * @param taskId 任务 ID
     * @return 是否取消成功
     */
    public boolean cancelTask(String taskId) {
        ScheduledFuture<?> future = scheduledTasks.remove(taskId);
        if (future != null) {
            boolean cancelled = future.cancel(false);
            log.info("手动添加数据：Task " + taskId + " cancelled: " + cancelled);
            return cancelled;
        }
        return false;
    }

    /**
     * 检查任务是否存在
     */
    public boolean hasTask(String taskId) {
        return scheduledTasks.containsKey(taskId);
    }

    public Map scheduledTasksStatus() {
        HashMap<String, ScheduledFuture<?>> stringScheduledFutureHashMap = new HashMap<>(scheduledTasks);
        scheduledTasks.forEach((taskId, future) -> {
            log.info("Task " + taskId + " scheduled: " + future.toString());
        });
        return stringScheduledFutureHashMap;
    }
}

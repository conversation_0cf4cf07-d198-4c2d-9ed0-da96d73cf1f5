package com.mira.backend.manager;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.provider.IAlgorithmProvider;
import com.mira.api.iam.consts.TokenCacheConst;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.AppUserInfoDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.backend.dto.LoginAdminUserDTO;
import com.mira.backend.exception.BackendException;
import com.mira.backend.properties.CacheExpireProperties;
import com.mira.redis.cache.RedisComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <p>平；
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-06
 **/
@Slf4j
@Component
@SuppressWarnings("all")
public class CacheManager {
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private CacheExpireProperties cacheExpireProperties;

    @Resource
    private IAlgorithmProvider algorithmProvider;
    @Resource
    private IUserProvider userProvider;

    /**
     * 获取Admin User标记令牌
     *
     * @param adminId 用户编号
     * @return 令牌
     */
    public String getAdminMarkToken(Long adminId) {
        String cacheKey = TokenCacheConst.BACKEND_ID_TOKEN_MAPPING + adminId;
        return redisComponent.get(cacheKey);
    }


    /**
     * 获取admin用户缓存
     *
     * @param adminId 用户id
     * @return 算法结果
     */
    public LoginAdminUserDTO getLoginAdminUserDTOCache(Long adminId) {
        String cacheKey = RedisCacheKeyConst.ADMIN_USER_TOKEN + adminId;
        LoginAdminUserDTO loginAdminUserDTO;
        try {
            loginAdminUserDTO = redisComponent.get(cacheKey, LoginAdminUserDTO.class);
        } catch (Exception e) {
            log.error("get login admin user cache error", e);
            loginAdminUserDTO = null;
        }
        return loginAdminUserDTO;
    }

    /**
     * 缓存admin用户
     *
     * @param adminId            用户编号
     * @param algorithmResultDTO 算法表记录
     */
    public void cacheLoginAdminUserDTO(Long adminId, LoginAdminUserDTO algorithmResultDTO) {
        String cacheKey = RedisCacheKeyConst.ADMIN_USER_TOKEN + adminId;
        redisComponent.setEx(cacheKey, algorithmResultDTO, cacheExpireProperties.getAdminUser(), TimeUnit.MINUTES);
    }

    /**
     * 删除admin用户缓存
     *
     * @param adminId 用户编号
     * @return true/false
     */
    public Boolean deleteLoginAdminUserDTO(Long adminId) {
        String cacheKey = RedisCacheKeyConst.ADMIN_USER_TOKEN + adminId;
        return redisComponent.delete(cacheKey);
    }


    /**
     * 获取算法结果表缓存
     *
     * @param userId 用户编号
     * @return AlgorithmResultDTO
     */
    public AlgorithmResultDTO getCacheAlgorithmResult(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        AlgorithmResultDTO algorithmResultDTO = null;
        try {
            algorithmResultDTO = redisComponent.get(cacheKey, AlgorithmResultDTO.class);
        } catch (Exception e) {
            log.error("get algorithm result cache error", e);
        }
        if (ObjectUtils.isEmpty(algorithmResultDTO)) {
            algorithmResultDTO = algorithmProvider.getAlgorithmResultCache(userId).getData();
            if (algorithmResultDTO == null) {
                throw new BackendException("user algorithmResult not exist");
            }
        }
        return algorithmResultDTO;
    }

    /**
     * 删除算法结果表缓存
     *
     * @param userId 用户编号
     */
    public void delCacheAlgorithmResult(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        redisComponent.delete(cacheKey);
    }

    /**
     * 获取App User标记令牌
     *
     * @param userId 用户编号
     * @return 令牌
     */
    public String getAppMarkToken(Long userId) {
        String cacheKey = TokenCacheConst.APP_ID_TOKEN_MAPPING + userId;
        return redisComponent.get(cacheKey);
    }

    /**
     * 获取用户的 push token
     *
     * @param userId 用户id
     * @return PushTokenDTO
     */
    public PushTokenDTO getPushToken(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_FIREBASE_TOKEN + userId;
        PushTokenDTO cache;
        try {
            cache = redisComponent.get(cacheKey, PushTokenDTO.class);
        } catch (Exception e) {
            log.error("get push token cache error", e);
            cache = null;
        }
        if (ObjectUtils.isNotEmpty(cache) && StringUtils.isNotBlank(cache.getPushToken())) {
            return cache;
        }

        AppUserInfoDTO appUserInfoDTO = userProvider.getUserInfoById(userId).getData();
        PushTokenDTO pushTokenDTO = new PushTokenDTO();
        pushTokenDTO.setPushToken(appUserInfoDTO.getPushToken());
        pushTokenDTO.setPlatform(appUserInfoDTO.getPlatform());
        redisComponent.setEx(cacheKey, pushTokenDTO, cacheExpireProperties.getPushToken(), TimeUnit.DAYS);

        return pushTokenDTO;
    }

}

package com.mira.backend.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mira.api.bluetooth.provider.IDataManualProvider;
import com.mira.core.util.JsonUtil;
import com.mira.redis.cache.RedisComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class EventSubscriber {

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private DelayedTaskScheduler delayedTaskScheduler;

    @Resource
    private IDataManualProvider dataManualProvider;

    public void onMessage(String message) {
        // convert
        String json = JsonUtil.toObject(message, String.class);
        Map<String, String> eventMap = JsonUtil.toObject(json, Map.class);

        // params
        Long userId = Long.valueOf(eventMap.get("userId"));
        String testWandType = eventMap.get("testWandType");
        String completeTime = eventMap.get("completeTime");
        String dataManualJson = eventMap.get("dataManualJson");

        log.info("手动添加数据：consumer addDelayedTask, user_id:{}, testWandType:{}, completeTime:{}",
                userId, testWandType, completeTime);

        try {
            processEvent(userId, testWandType, completeTime, dataManualJson);
        } catch (Exception e) {
            log.error("手动添加数据：process event error, message:{}", message, e);
        }
    }

    private void processEvent(Long userId, String testWandType,String completeTime,String dataManualJson) {
        boolean result = delayedTaskScheduler.addTask("task-" + userId + "-" + completeTime, () -> {
            try {
                log.info("手动添加数据：任务延迟执行: " + System.currentTimeMillis() + "__" + "task-" + userId + "-" + completeTime);

                dataManualProvider.addDelayedTaskHormoneData(userId, testWandType, completeTime, dataManualJson);
            } catch (Exception e) {
                log.error("手动添加数据：延迟任务执行失败", e);
            }
        });
        log.info("手动添加数据：addTask result: {}", result);
    }

}

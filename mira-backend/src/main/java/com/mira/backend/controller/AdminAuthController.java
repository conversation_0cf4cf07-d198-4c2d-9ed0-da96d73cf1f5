package com.mira.backend.controller;

import com.mira.backend.config.logs.AdminLog;
import com.mira.backend.dal.dao.admin.AdminPermissionDAO;
import com.mira.backend.dal.dao.admin.AdminRoleDAO;
import com.mira.backend.dto.*;
import com.mira.backend.pojo.vo.admin.AdminPermissionVO;
import com.mira.backend.pojo.vo.admin.AdminRoleV2VO;
import com.mira.backend.pojo.vo.admin.AdminRoleVO;
import com.mira.backend.pojo.vo.admin.AdminUserVO;
import com.mira.backend.service.IAdminAuthService;
import com.mira.backend.util.tree.TreeBuilder;
import com.mira.backend.util.tree.TreeNode;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-07
 **/
@Api(tags = "02.admin用户授权管理")
@RestController
@RequestMapping("admin/auth")
public class AdminAuthController {
    @Resource
    private IAdminAuthService adminAuthService;
    @Resource
    private AdminRoleDAO adminRoleDAO;
    @Resource
    private AdminPermissionDAO adminPermissionDAO;


    @ApiOperation("1. 获取管理员列表")
    @GetMapping("/list")
    @AdminLog("获取管理员列表")
    public List<AdminUserVO> listAdmins() {
        return adminAuthService.listAdmins();
    }

    @ApiOperation("2. 获取某个管理员的角色列表")
    @GetMapping("/view/admin/roles")
    @AdminLog("获取某个管理员的角色列表")
    public List<AdminRoleVO> viewAdminRoles(@RequestParam Long adminId) {
        return adminRoleDAO.viewAdminRoles(adminId);
    }

    @ApiOperation("3. 编辑某个管理员的角色列表")
    @PostMapping("/edit/admin/roles")
    @AdminLog("编辑某个管理员的角色列表")
    public void editAdminRoles(@Valid @RequestBody EditAdminRoleDTO editAdminRoleDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        adminAuthService.editAdminRoles(editAdminRoleDTO, timeZone);
    }

    @ApiOperation("4. 获取系统的角色列表")
    @GetMapping("/role/list")
    @AdminLog("获取系统的角色列表")
    public List<AdminRoleVO> listRoles() {
        return adminAuthService.listRoles();
    }

    @ApiOperation("4.2. 获取系统的角色列表(权限tree)")
    @GetMapping("/role/list/v2")
    @AdminLog("获取系统的角色列表")
    public List<AdminRoleV2VO> listRolesV2() {
        List<AdminRoleVO> adminRoleVOS = adminAuthService.listRoles();
        List<AdminRoleV2VO> adminRoleV2VOS = new ArrayList<>();
        for (AdminRoleVO adminRoleVO : adminRoleVOS) {
            AdminRoleV2VO adminRoleV2VO = new AdminRoleV2VO();
            adminRoleV2VO.setId(adminRoleVO.getId());
            adminRoleV2VO.setCode(adminRoleVO.getCode());
            adminRoleV2VO.setName(adminRoleVO.getName());
            adminRoleV2VO.setNameEn(adminRoleVO.getNameEn());
            adminRoleV2VO.setDescription(adminRoleVO.getDescription());
            List<TreeNode<AdminPermissionVO>> treeNodes = new TreeBuilder<AdminPermissionVO>().buildTree(adminRoleVO.getPermissions());
            adminRoleV2VO.setPermissions(treeNodes);
            adminRoleV2VOS.add(adminRoleV2VO);
        }
        return adminRoleV2VOS;
    }

    @ApiOperation("5. 获取某个角色的权限列表")
    @GetMapping("/view/role/permissions")
    @AdminLog("获取某个角色的权限列表")
    public List<AdminPermissionVO> viewRolePermissions(@RequestParam Long roleId) {
        return adminPermissionDAO.viewRolePermissions(roleId);
    }

    @ApiOperation("5.2. 获取某个角色的权限tree")
    @GetMapping("/view/role/permissions/v2")
    @AdminLog("获取某个角色的权限列表")
    public List<TreeNode<AdminPermissionVO>> viewRolePermissionsV2(@RequestParam Long roleId) {
        List<AdminPermissionVO> adminPermissionVOS = adminPermissionDAO.viewRolePermissions(roleId);
        List<TreeNode<AdminPermissionVO>> treeNodes = new TreeBuilder<AdminPermissionVO>().buildTree(adminPermissionVOS);
        return treeNodes;
    }

    @ApiOperation("6. 编辑某个角色的权限列表")
    @PostMapping("/edit/role/permissions")
    @AdminLog("编辑某个角色的权限列表")
    public void editRolePermission(@Valid @RequestBody EditRolePermissionDTO editRolePermissionDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        adminAuthService.editRolePermission(editRolePermissionDTO, timeZone);
    }


    @ApiOperation("7. 获取系统的权限列表")
    @GetMapping("/permission/list")
    @AdminLog("获取系统的权限列表")
    public List<AdminPermissionVO> listPermissions() {
        return adminPermissionDAO.listVO();
    }

    @ApiOperation("7.2. 获取系统的权限tree")
    @GetMapping("/permission/list/v2")
    @AdminLog("获取系统的权限列表")
    public List<TreeNode<AdminPermissionVO>> listPermissionsV2() {
        List<AdminPermissionVO> adminPermissionVOS = adminPermissionDAO.listVO();
        List<TreeNode<AdminPermissionVO>> treeNodes = new TreeBuilder<AdminPermissionVO>().buildTree(adminPermissionVOS);
        return treeNodes;
    }

    @ApiOperation("8. 创建admin管理员")
    @PostMapping("/create/admin")
    @AdminLog("创建admin管理员")
    public Long createAdmin(@Valid @RequestBody CreateAdminDTO createAdminDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        return adminAuthService.create(createAdminDTO.getName(), createAdminDTO.getRealName(), timeZone);
    }

    @ApiOperation("9. 创建角色")
    @PostMapping("/create/role")
    @AdminLog("创建角色")
    public Long createRole(@Valid @RequestBody CreateRoleDTO createRoleDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        return adminRoleDAO.create(createRoleDTO.getCode(), createRoleDTO.getName(),
                createRoleDTO.getNameEn(), createRoleDTO.getDescription(), timeZone);
    }


    @ApiOperation("10. 创建权限")
    @PostMapping("/create/permission")
    @AdminLog("创建权限")
    public Long createPermission(@Valid @RequestBody CreatePermissionDTO createPermissionDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        return adminPermissionDAO.create(createPermissionDTO.getCode(), createPermissionDTO.getType(),
                createPermissionDTO.getDescription(),
                createPermissionDTO.getDescriptionEn(), createPermissionDTO.getParentId(),
                timeZone);
    }

    @ApiOperation("11. 删除admin管理员")
    @PostMapping("/delete/admin")
    @AdminLog("删除admin管理员")
    public void deleteAdmin(@Valid @RequestBody DeleteAdminDTO deleteAdminDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        adminAuthService.delete(deleteAdminDTO.getAdminId(), timeZone);
    }


}

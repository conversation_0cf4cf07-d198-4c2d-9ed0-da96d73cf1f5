package com.mira.backend.controller;

import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.api.job.dto.NotificationPushPreviewDTO;
import com.mira.backend.config.logs.AdminLog;
import com.mira.backend.controller.vo.NotificationJobListVO;
import com.mira.backend.service.INotificationPushService;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * notification push
 *
 * <AUTHOR>
 */
@Api(tags = "16.推送任务")
@RestController
@RequestMapping("/notification")
public class NotificationPushController {
    @Resource
    private INotificationPushService notificationPushService;

    @AdminLog("创建推送任务")
    @ApiOperation("创建推送任务")
    @PostMapping("/task/create")
    public void create(@RequestBody NotificationPushCreateDTO notificationPushCreateDTO) {
        notificationPushService.create(notificationPushCreateDTO);
    }

    @AdminLog("编辑推送任务")
    @ApiOperation("编辑推送任务")
    @PostMapping("/task/edit")
    public void edit(@RequestBody NotificationPushCreateDTO notificationPushCreateDTO) {
        notificationPushService.edit(notificationPushCreateDTO);
    }

    @ApiOperation("推送任务列表")
    @GetMapping("/task/list")
    public PageResult<NotificationJobListVO> list(@RequestParam("current") long current,
                                                  @RequestParam("size") long size) {
        return notificationPushService.list(current, size);
    }

    @AdminLog("激活推送任务")
    @ApiOperation("激活推送任务")
    @PostMapping("/task/active")
    public void active(@RequestParam("taskId") String taskId) {
        notificationPushService.active(taskId);
    }

    @AdminLog("取消激活推送任务")
    @ApiOperation("取消激活推送任务")
    @PostMapping("/task/inactive")
    public void inactive(@RequestParam("taskId") String taskId) {
        notificationPushService.inactive(taskId);
    }

    @AdminLog("删除推送任务")
    @ApiOperation("删除推送任务")
    @PostMapping("/task/delete")
    public void delete(@RequestParam("taskId") String taskId) {
        notificationPushService.delete(taskId);
    }

    @AdminLog("预览推送任务")
    @ApiOperation("预览推送任务")
    @PostMapping("/task/previews")
    public void previews(@RequestBody NotificationPushPreviewDTO pushPreviewDTO) {
        notificationPushService.previews(pushPreviewDTO);
    }
}

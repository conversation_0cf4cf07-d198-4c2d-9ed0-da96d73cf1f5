package com.mira.backend.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mira.api.bluetooth.dto.backend.*;
import com.mira.api.bluetooth.provider.IDataManualProvider;
import com.mira.api.bluetooth.provider.IDataUploadProvider;
import com.mira.backend.config.logs.AdminLog;
import com.mira.backend.dal.dao.admin.AdminUserDAO;
import com.mira.backend.dal.dao.user.UserReminderComplaintDAO;
import com.mira.backend.dal.entity.admin.AdminUserEntity;
import com.mira.backend.dal.entity.user.AppUserReminderEntity;
import com.mira.backend.dal.entity.user.UserReminderComplaintEntity;
import com.mira.backend.dal.mapper.user.AppUserReminderMapper;
import com.mira.backend.pojo.param.user.DataManualNotificationParam;
import com.mira.core.annotation.Anonymous;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-03-21
 **/
@Api(tags = "05.手动添加测试数据")
@RestController
@RequestMapping("app/manual-data")
@Slf4j
public class AppUserManualDataController {
    @Resource
    private IDataUploadProvider dataUploadProvider;
    @Resource
    private IDataManualProvider dataManualProvider;

    @Resource
    private UserReminderComplaintDAO userReminderComplaintDAO;
    @Resource
    private AppUserReminderMapper appUserReminderMapper;

    @Resource
    private AdminUserDAO adminUserDAO;


    @ApiOperation("1.用户测试数据分页列表")
    @PostMapping("/data-page")
    public PageResult<DataManualDTO> dataPage(@RequestBody DataManualPageRequestDTO pageParam) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        ManualDataResponseDTO manualDataResponseDTO = dataManualProvider.manualDataPage(pageParam).getData();
        List<DataManualDTO> dataManualDTOS = manualDataResponseDTO.getDataManualDTOS();
        if (!ObjectUtils.isEmpty(dataManualDTOS)){
            Set<Long> modifiers = dataManualDTOS
                    .stream().map(DataManualDTO::getModifier)
                    .filter(id -> !id.equals(-1L))
                    .collect(Collectors.toSet());
            //查询这些人的名字
            List<AdminUserEntity> adminUserEntities = new ArrayList<>();
            if (!modifiers.isEmpty()){
                adminUserEntities = adminUserDAO.listByIds(modifiers);
            }

            for (DataManualDTO dataManualDTO : dataManualDTOS) {
                dataManualDTO.setCreateTimeStr(ZoneDateUtil.format(timeZone, dataManualDTO.getCreateTime(),
                        DatePatternConst.DATE_TIME_PATTERN));
                Long modifier = dataManualDTO.getModifier();
                if(modifier == -1L){
                    dataManualDTO.setModifierName("system");
                }else {
                    AdminUserEntity adminUserEntity1 = adminUserEntities.stream()
                            .filter(adminUserEntity -> adminUserEntity.getId().equals(modifier))
                            .findAny().orElse(null);
                    if(adminUserEntity1 != null){
                        dataManualDTO.setModifierName(adminUserEntity1.getRealName());
                    }
                }
                if (dataManualDTO.getDoubleCheck()==0){
                    dataManualDTO.setModifierName(null);
                }
            }
        }

        return new PageResult<>(dataManualDTOS, manualDataResponseDTO.getTotal(), pageParam.getSize(),
                pageParam.getCurrent());
    }

    @ApiOperation("2.查看详情")
    @GetMapping("/detail")
    public DataManualDTO detail(@RequestParam Long id) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        return dataManualProvider.detail(id,adminId).getData();
    }

    @ApiOperation("3.审核")
    @PostMapping("/audit")
    @AdminLog("审核手动添加数据")
    public Integer audit(@RequestBody DataManualAuditDTO dataManualAuditDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        return dataManualProvider.audit(dataManualAuditDTO,adminId).getData();
    }


    @ApiOperation("4.发送添加数据结果通知")
    @PostMapping("/notification")
    @AdminLog("发送添加数据结果通知")
    public void sendNotification(@RequestBody DataManualNotificationParam dataManualNotificationParam) {
        dataManualProvider.sendNotification(dataManualNotificationParam.getId(), dataManualNotificationParam.getNotificationDefineId());
    }

    @ApiOperation("5.状态改为忽略")
    @PostMapping("/ignore")
    @AdminLog("状态改为忽略")
    public void ignore(@RequestParam Long id) {
        dataManualProvider.ignore(id);
    }

    @ApiOperation("6.获取试剂批次列表")
    @GetMapping("/wand-batch/list")
    public List<BackendWandsParamRecordDTO> wandBatchList(@RequestParam String testWandType) {
        return dataManualProvider.wandBatchList(testWandType).getData();
    }

    @ApiOperation("7.修改数据的试剂批次")
    @PostMapping("/change-wand-batch")
    @AdminLog("修改数据的试剂批次")
    public void changeWandBatch(@RequestBody ChangeWandBatchDTO changeWandBatchDTO) {
        dataUploadProvider.changeWandBatch(changeWandBatchDTO);
    }


    @ApiOperation("8.添加测试数据")
    @PostMapping("/add/testData")
    @AdminLog("添加测试数据")
    public void addTestData(@Valid @RequestBody List<ManualAddTestDataDTO> testDataParamList) {
        dataManualProvider.addTestData(testDataParamList);
    }

    @ApiOperation("9.数据改为有效")
    @PostMapping("/update/valid/{id}")
    public void updateTestDataValid(@PathVariable("id") Long id) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        dataUploadProvider.updateTestDataValid(id, loginInfo.getId(), loginInfo.getUsername());
    }


    @ApiOperation("10.投诉用户添加列表")
    @PostMapping("/add/reminder_complaint/{userId}")
    public String addReminderComplaint(@PathVariable Long userId) {
        UserReminderComplaintEntity userReminderComplaintEntity = new UserReminderComplaintEntity();
        userReminderComplaintEntity.setUserId(userId);
        userReminderComplaintDAO.save(userReminderComplaintEntity);

        AppUserReminderEntity appUserReminderEntity =
                appUserReminderMapper.selectOne(Wrappers.<AppUserReminderEntity>lambdaQuery()
                                                        .eq(AppUserReminderEntity::getUserId, userId));
        // 有 Max Wands 测试记录
        Integer maxWandsCount = dataUploadProvider.countMaxWands(userId).getData();
        if (maxWandsCount > 0) {
            appUserReminderEntity.setMaxWands(1);
            appUserReminderEntity.setFertilityPlusWands(0);
            appUserReminderEntity.setFertilityConfirmWands(0);
            appUserReminderMapper.updateById(appUserReminderEntity);
        }
        return "添加 complaint 数据表成功";
    }

    @ApiOperation("10.将未执行的手动添加数据task，添加到执行队列")
    @PostMapping("/add-unprocess-manual-data")
    @Anonymous
    public Integer addUnprocessManualData2Task(@RequestParam Integer count) {
        return dataManualProvider.addUnprocessManualData2Task(count).getData();
    }
}

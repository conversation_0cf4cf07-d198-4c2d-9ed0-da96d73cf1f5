package com.mira.backend.controller.vo;

import com.mira.api.message.enums.SysNotificationTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * notification push job list vo
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("推送任务详情")
public class NotificationJobListVO {
    @ApiModelProperty("任务ID")
    private String taskId;

    // ------------------------- 通知定义 -------------------------
    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("图标")
    private String icon;

    /**
     * @see SysNotificationTypeEnum
     */
    @ApiModelProperty("推送类型")
    private Integer type;

    @ApiModelProperty("静默通知，0-不是，1-是")
    private Integer silent;

    @ApiModelProperty("推送的文字内容")
    private String content;

    @ApiModelProperty("样式类型，0-默认，1-全屏，2-bottom image，3-半屏")
    private Integer styleType;

    @ApiModelProperty("按钮1")
    private String button1;

    @ApiModelProperty("按钮2")
    private String button2;

    @ApiModelProperty("链接1")
    private String button1Link;

    @ApiModelProperty("链接2")
    private String button2Link;

    @ApiModelProperty("图片链接")
    private String pictureUrl;

    @ApiModelProperty("过期时间 yyyy-MM-dd HH:mm:ss")
    private String expireTime;

    @ApiModelProperty("背景图")
    private String backgroundImage;

    @ApiModelProperty("背景颜色")
    private String backgroundColor;

    // ------------------------- 筛选条件 -------------------------
    /**
     * see UserGoalEnum
     */
    @ApiModelProperty("用户目标")
    private Integer[] mode;

    /**
     * see UserConditionEnum
     */
    @ApiModelProperty("健康选项")
    private Integer[] healthCondition;

    /**
     * 1:(18 - 25), 2:(26 - 30), 3:(31 - 35), 4:(36 - 40), 5:(>40)
     */
    @ApiModelProperty("年龄范围")
    private Integer[] age;

    /**
     * see CurrencyEnum
     */
    @ApiModelProperty("国家货币")
    private String[] currency;

    /**
     * 0:未绑定, 1:已绑定
     */
    @ApiModelProperty("绑定仪器")
    private Integer bindAnalyzer;

    /**
     * 1:Predicted ovulation, 2:LH peak detected
     * 3:LH peak not detected, 4:Ovulation confirmed
     */
    @ApiModelProperty("排卵日")
    private Integer[] ovulation;

    /**
     * 1:Predicted, 2:logged beginning,logged end, 3:predicted end
     */
    @ApiModelProperty("经期")
    private Integer[] period;

    /**
     * 1:As recommended (more than 70%)
     * 2:Not as recommended (less than 70%)
     * 3:Not testing (at least 10 recommended tests skipped)
     */
    @ApiModelProperty("试剂测试")
    private Integer testing;

    /**
     * 0:Negative, 1:Positive
     */
    @ApiModelProperty("是否怀孕")
    private Integer pregnancy;

    /**
     * User inactive in the app for 30 / 60 / 90 days
     * 1:30, 2:60, 3:90
     */
    @ApiModelProperty("用户流失")
    private Integer churn;

    /**
     * 1:1 in a cycle, 2:2 in a cycle
     * 3:3 in a cycle, 4:4 in a cycle, 5:>4 in a cycle
     */
    @ApiModelProperty("试剂错误")
    private Integer wandError;

    /**
     * 1:1 in a cycle, 2:2 in a cycle
     * 3:3 in a cycle, 4:4 in a cycle, 5:>4 in a cycle
     */
    @ApiModelProperty("同步错误")
    private Integer syncingError;

    /**
     * 0:未安排, 1:已安排
     */
    @ApiModelProperty("是否已安排辅导课程")
    private Integer coachingSession;

    /**
     * 0:未订阅, 1:已订阅
     */
    @ApiModelProperty("是否订阅")
    private Integer subscription;

    /**
     * 周期内测试次数，1,2,3,4,5...33,34,35,35+
     */
    @ApiModelProperty("周期内测试次数")
    private Integer[] testsCount;

    /**
     * 预测月经前的天数，1,2,3...9,10
     */
    @ApiModelProperty("预测月经前的天数")
    private Integer[] beforePredictedPeriod;

    @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
    private Integer trackingMenopause;

    @ApiModelProperty("诊所id列表")
    private List<ClinicDTO> clinics;

    // ------------------------- 触发器：一次性 -------------------------
    @ApiModelProperty("选择的日期时间 yyyy-MM-dd HH:mm:ss")
    private String dateTime;

    // ------------------------- 触发器：固定频率 -------------------------
    @ApiModelProperty("周期内哪几天")
    private Integer[] dayInCycle;

    @ApiModelProperty("哪几个周期开始")
    private Integer[] cycle;

    // ------------------------- Task -------------------------
    @ApiModelProperty("0-未启动，1-已启动，2-运行中，3-已完成")
    private Integer taskStatus;

    @ApiModelProperty("task类型，0-促销推送，1-固定频率推送")
    private Integer taskType;

    @Getter
    @Setter
    public static class ClinicDTO {
        @ApiModelProperty("id")
        private Long id;

        @ApiModelProperty("code")
        private String code;

        @ApiModelProperty("name")
        private String name;
    }
}

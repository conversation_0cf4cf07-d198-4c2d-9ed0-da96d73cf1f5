package com.mira.backend.controller;

import com.mira.backend.service.ISnToRecordService;
import com.mira.backend.config.logs.AdminLog;
import com.mira.backend.controller.excel.ExcelFileHandler;
import com.mira.backend.dto.SnToRecordPageDTO;
import com.mira.backend.pojo.vo.user.SnToRecordVO;
import com.mira.core.annotation.Anonymous;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "09.sn管理")
@RestController
@RequestMapping("sn")
@Slf4j
public class SnToRecordController {
    @Resource
    private ISnToRecordService snToRecordService;

    @Resource
    private ExcelFileHandler excelFileHandler;

    @ApiOperation("1. 根据sn号获取发货记录")
    @GetMapping("/getBySn")
    @Anonymous
    @AdminLog("根据sn号获取发货记录")
    public SnToRecordVO getBySn(@RequestParam String sn) {
        return snToRecordService.getBySn(sn);
    }

    @ApiOperation("2. 根据批量sn号批量获取发货记录")
    @GetMapping("/listBySn")
    @Anonymous
    @AdminLog("根据批量sn号批量获取发货记录")
    public List<SnToRecordVO> listBySn(@RequestParam List<String> snlist) {
        return snToRecordService.listBySn(snlist);
    }

    /**
     * 上传sn发货记录
     *
     * @param request 请求
     */
    @Anonymous
    @ApiOperation(value = "上传sn发货记录", notes = "上传sn发货记录")
    @PostMapping(value = "/upload")
    @ResponseBody
    @AdminLog("上传sn发货记录")
    public void uploadUsers(HttpServletRequest request) {
        excelFileHandler.processUploadFile(request);
    }

    /**
     * 上传sn发货记录
     *
     * @param file excel文件
     */
    @Anonymous
    @ApiOperation(value = "上传sn发货记录", notes = "上传sn发货记录")
    @PostMapping(value = "/upload-sn")
    @AdminLog("上传sn发货记录")
    public void uploadSnRecord(@RequestParam MultipartFile file) {
        if (file == null) {
            return;
        }
        excelFileHandler.processUploadFile(file);
    }

    @ApiOperation("4. sn发货记录分页列表")
    @PostMapping("/page")
    public PageResult<SnToRecordVO> snToRecordPage(@RequestBody SnToRecordPageDTO pageDTO) {
        return snToRecordService.snToRecordPage(pageDTO);
    }
}

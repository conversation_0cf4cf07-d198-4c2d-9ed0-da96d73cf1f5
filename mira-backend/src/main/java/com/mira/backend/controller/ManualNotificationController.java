package com.mira.backend.controller;

import com.mira.backend.dto.ManualPushNotificationDTO;
import com.mira.backend.service.IManualNotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-08-05
 **/
@Api(tags = "13.手动发送单条通知")
@RestController
@RequestMapping("/manual-notification")
public class ManualNotificationController {
    @Resource
    private IManualNotificationService manualNotificationService;

    @ApiOperation("01.手动测试推动通知")
    @PostMapping("/push")
    public String pushNotification(@Valid @RequestBody ManualPushNotificationDTO manualPushNotificationDTO) {
        manualNotificationService.pushNotification(manualPushNotificationDTO);
        return "success";
    }
}

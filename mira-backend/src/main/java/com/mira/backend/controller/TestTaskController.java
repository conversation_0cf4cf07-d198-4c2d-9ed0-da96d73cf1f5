package com.mira.backend.controller;

import com.mira.backend.manager.DelayedTaskScheduler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Api(tags = "18.test task")
@RestController
@RequestMapping("/test-task")
public class TestTaskController {
    @Resource
    private DelayedTaskScheduler delayedTaskScheduler;

    @ApiOperation("1.listTask")
    @GetMapping("/listTask")
    public Map listTask() {
        return delayedTaskScheduler.scheduledTasksStatus();
    }
}

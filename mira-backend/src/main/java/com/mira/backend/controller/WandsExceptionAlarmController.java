package com.mira.backend.controller;

import com.mira.api.mongo.dto.WandsExceptionAlarmPageRequestDTO;
import com.mira.api.mongo.dto.WandsExceptionAlarmPageVO;
import com.mira.api.mongo.dto.WandsExceptionAlarmVO;
import com.mira.api.mongo.provider.IWandsExceptionAlarmProvider;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 试剂异常告警
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-02-18
 **/
@Api(tags = "15.试剂异常告警")
@RestController
@RequestMapping("wands-exception-alarm")
public class WandsExceptionAlarmController {
    @Resource
    private IWandsExceptionAlarmProvider wandsExceptionAlarmProvider;

    @ApiOperation("1.分页列表")
    @PostMapping("/page")
    public PageResult<WandsExceptionAlarmVO> page(@RequestBody WandsExceptionAlarmPageRequestDTO pageParam) {
        WandsExceptionAlarmPageVO wandsExceptionAlarmPageVO = wandsExceptionAlarmProvider.wandsExceptionAlarmPage(pageParam).getData();
        return new PageResult<>(wandsExceptionAlarmPageVO.getWandsExceptionAlarmVOS(), wandsExceptionAlarmPageVO.getTotal(),
                pageParam.getSize(),
                pageParam.getCurrent());
    }
}

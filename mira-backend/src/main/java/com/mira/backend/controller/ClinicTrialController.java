package com.mira.backend.controller;


import com.mira.backend.dto.LoginAdminUserDTO;
import com.mira.backend.config.logs.AdminLog;
import com.mira.backend.exception.BackendException;
import com.mira.backend.pojo.dto.ClinicTrialBatchDTO;
import com.mira.backend.pojo.dto.ClinicTrialUserDTO;
import com.mira.backend.pojo.param.user.ClinicTrialPageParam;
import com.mira.backend.service.IAdminLoginService;
import com.mira.backend.service.IClinicTrialService;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-25
 **/
@Api(tags = "11.诊所临床管理")
@RestController
@RequestMapping("clinic/trial")
public class ClinicTrialController {
    @Resource
    private IClinicTrialService clinicTrialService;
    @Resource
    private IAdminLoginService adminLoginService;

    @ApiOperation("1. 临床批次列表")
    @GetMapping("/list-batch")
    @AdminLog("临床批次列表")
    public List<ClinicTrialBatchDTO> listClinicTrialBatch() {
        LoginAdminUserDTO adminUserDTO = adminLoginService.adminInfo();
        Set<String> permissionCodes = adminUserDTO.getPermissionCodes();
        Set<String> tenantCodes = permissionCodes.stream()
                .filter(permissionCode -> permissionCode.startsWith("data:list-batch:"))
                .map(permissionCode -> permissionCode.substring("data:list-batch:".length()))
                .collect(Collectors.toSet());
        if (tenantCodes.isEmpty()) {
            throw new BackendException("without permission: data:list-batch");
        }
        return clinicTrialService.listClinicTrialBatch(tenantCodes);
    }

    @ApiOperation("2. 临床用户分页列表")
    @PostMapping("/user-page")
    @AdminLog("临床用户分页列表")
    public PageResult<? extends ClinicTrialUserDTO> dataPage(@RequestBody ClinicTrialPageParam pageParam) {
        LoginAdminUserDTO adminUserDTO = adminLoginService.adminInfo();
        Set<String> permissionCodes = adminUserDTO.getPermissionCodes();
        String permission = permissionCodes.stream()
                .filter(permissionCode -> permissionCode.startsWith("data:list-batch:"))
                .findFirst()
                .orElseThrow(() -> new BackendException("without permission: data:list-batch"));
        return clinicTrialService.dataPage(pageParam);
    }
}

package com.mira.backend.controller;

import com.mira.api.file.provider.IFileProvider;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * Description 文件管理控制器
 */
@Api(tags = "10.文件管理")
@Slf4j
@RestController
@RequestMapping("file")
public class StorageController {
    @Resource
    private IFileProvider fileProvider;

    @ApiOperation("上传文件")
    @PostMapping("/upload")
    public String upload(@RequestParam("file") MultipartFile file, @RequestParam(value = "type") String type) {
        return fileProvider.upload(file, type).getData();
    }
}

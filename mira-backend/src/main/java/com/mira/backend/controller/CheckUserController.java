package com.mira.backend.controller;

import com.mira.api.user.dto.user.UserBindLogDTO;
import com.mira.backend.config.logs.AdminLog;
import com.mira.backend.pojo.param.user.ModifyEmailParam;
import com.mira.backend.pojo.vo.user.UserInfoVO;
import com.mira.backend.pojo.vo.user.UserSearchVO;
import com.mira.backend.service.ICheckUserService;
import com.mira.core.annotation.Anonymous;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-10
 **/
@Api(tags = "03.check app用户管理")
@RestController
@RequestMapping("app/user")
@Slf4j
public class CheckUserController {
    @Resource
    private ICheckUserService checkUserService;

    @ApiOperation("1. 创建checkToken")
    @PostMapping("/check-token")
    public String createCheckToken(@RequestParam Long userId) throws Exception {
        return checkUserService.createCheckToken(userId);
    }

    /**
     * 给survey使用
     * @param email
     * @return
     * @throws Exception
     */
    @ApiOperation("1. 创建checkToken v2")
    @PostMapping("/check-token-v2")
    public String createCheckTokenV2(@RequestParam String email) throws Exception {
        return checkUserService.createCheckTokenV2(email);
    }

    /**
     * 可以按照email模糊搜索, userId固定搜索, sn固定搜索
     * @param keyword
     * @return
     */
    @ApiOperation("2. 搜索用户")
    @GetMapping("/search")
    public List<UserSearchVO> searchDeskUser(@RequestParam String keyword) {
        return checkUserService.searchDeskUser(keyword);
    }

    @ApiOperation("3. 用户基本信息")
    @GetMapping("/info")
    @AdminLog("获取用户基本信息")
    public UserInfoVO info(@RequestParam Long userId) {
        return checkUserService.info(userId);
    }

    @ApiOperation("4. 重置密码为后台指定密码")
    @GetMapping("/reset-pwd")
    @AdminLog("重置用户密码")
    public void resetPwd(@RequestParam Long userId) {
        checkUserService.resetPwd(userId);
    }

    @ApiOperation("5. 激活用户")
    @GetMapping("/enable")
    @AdminLog("激活用户")
    public void enable(@RequestParam Long userId) {
        checkUserService.enable(userId);
    }

    @ApiOperation("6. 删除用户")
    @PostMapping("/delete")
    @AdminLog("删除用户")
    public Long delete(@RequestParam Long userId) {
        return checkUserService.delete(userId);
    }

    @ApiOperation("7. 修改用户email")
    @PostMapping("/modifyEmail")
    @AdminLog("修改用户email")
    public Long modifyEmail(@Valid @RequestBody ModifyEmailParam modifyEmailParam) {
        return checkUserService.modifyEmail(modifyEmailParam);
    }

    @ApiOperation(value = "8. 查询用户的10条仪器绑定历史记录", notes = "查询用户的绑定历史记录")
    @GetMapping(value = "/bind-log")
    @Transactional(rollbackFor = Exception.class)
    @Anonymous
    public List<UserBindLogDTO> list10BindLog(@ApiParam("用户邮箱") @RequestParam String email, @RequestParam(required = false) String code) {
        return checkUserService.list10BindLog(email, code);
    }

    @ApiOperation("9. 将用户添加到testing schedule exchange 列表中")
    @PostMapping("/testing-schedule-exchange")
    @AdminLog("将用户添加到testing schedule exchange 列表中")
    public void addTestingScheduleExchange(@RequestParam Long userId) {
        checkUserService.addTestingScheduleExchange(userId);
    }
}

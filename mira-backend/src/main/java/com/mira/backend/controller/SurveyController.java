package com.mira.backend.controller;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.mongo.dto.SurveyBaseDTO;
import com.mira.api.mongo.dto.SurveyDTO;
import com.mira.api.mongo.dto.SurveyPageDTO;
import com.mira.api.mongo.dto.SurveyPageVO;
import com.mira.api.mongo.provider.ISurveyProvider;
import com.mira.backend.pojo.param.survey.SurveyParam;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import com.mira.core.request.PageDTO;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-03-22
 **/
@Api(tags = "17.survey")
@RestController
@RequestMapping("/survey")
public class SurveyController {
    // 假设存在SurveyService类处理业务，此处通过依赖注入引入
    @Resource
    private ISurveyProvider surveyProvider;

    @ApiOperation("1.创建或者修改survey")
    @PostMapping("save-or-update")
    public void saveOrUpdateSurvey(@RequestBody SurveyParam surveyParam) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        SurveyDTO surveyDTO = new SurveyDTO();
        BeanUtil.copyProperties(surveyParam, surveyDTO);
        surveyDTO.setTimeZone(timeZone);
        surveyProvider.saveOrUpdateSurvey(surveyDTO);
    }

    @ApiOperation("2.分页列表")
    @PostMapping("/page")
    public PageResult<SurveyPageDTO> page(@RequestBody PageDTO pageParam) {
        SurveyPageVO surveyPageVO = surveyProvider.surveyPage(pageParam).getData();
        return new PageResult<>(surveyPageVO.getSurveyPageDTOS(), surveyPageVO.getTotal(),
                pageParam.getSize(),
                pageParam.getCurrent());
    }

    @ApiOperation("3.通过id获取survey")
    @GetMapping("/{id}")
    public SurveyDTO getSurveyById(@PathVariable String id) {
        return surveyProvider.getSurveyById(id).getData();
    }

    @ApiOperation("4.修改survey激活状态")
    @PostMapping("/{id}/change-status")
    public void changeSurveyStatus(@PathVariable String id, @RequestParam Integer status) {
        surveyProvider.changeSurveyStatus(id, status);
    }

    @ApiOperation("5.删除survey")
    @DeleteMapping("/{id}")
    public void deleteSurvey(@PathVariable String id) {
        surveyProvider.deleteSurvey(id);
    }

    @ApiOperation("6.survey中可以选择的survey列表")
    @GetMapping("/list-all")
    public List<SurveyBaseDTO> listAll() {
        List<SurveyBaseDTO> surveyBaseDTOS = surveyProvider.listAll().getData();
        return surveyBaseDTOS;
    }

    @ApiOperation("7.banner中可以选择的survey列表")
    @GetMapping("/banner/select-list")
    public List<SurveyBaseDTO> bannerSelectList() {
        List<SurveyBaseDTO> surveyBaseDTOS = surveyProvider.bannerSelectList().getData();
        return surveyBaseDTOS;
    }
}

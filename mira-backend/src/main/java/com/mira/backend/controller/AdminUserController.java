package com.mira.backend.controller;


import com.mira.backend.dto.AdminLoginDTO;
import com.mira.backend.dto.LoginAdminUserDTO;
import com.mira.backend.dto.ModifyPwdDTO;
import com.mira.backend.service.IAdminLoginService;
import com.mira.core.annotation.Anonymous;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "01.admin用户管理")
@RestController
@RequestMapping("admin/user")
public class AdminUserController {
    @Resource
    private IAdminLoginService adminLoginService;

    /**
     * 登陆
     */
    @ApiOperation("1. 登陆")
    @PostMapping("/login")
    @Anonymous
    public String login(@Valid @RequestBody AdminLoginDTO adminLoginDTO) throws Exception {
        return adminLoginService.login(adminLoginDTO);
    }

    @ApiOperation(value = "2. 获取登录管理员详情")
    @GetMapping("/loginAdminInfo")
    public LoginAdminUserDTO info() {
        return adminLoginService.adminInfo();
    }


    @ApiOperation(value = "3. 退出登陆")
    @PostMapping("/logout")
    public void logout() {
        adminLoginService.logout();
    }

    @ApiOperation("4. 修改管理员密码")
    @PostMapping("/resetPwd")
    public void resetPwd(@Valid @RequestBody ModifyPwdDTO modifyPwdDTO) {
        adminLoginService.resetPwd(modifyPwdDTO);
    }
}


package com.mira.backend.controller.excel;

import com.mira.backend.dal.dao.user.SnToRecordDAO;
import com.mira.backend.dal.entity.user.SnToRecordEntity;
import com.mira.backend.exception.BackendException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Service
@Slf4j
public class ExcelFileHandler {
    @Resource
    private SnToRecordDAO snToRecordDAO;

    /**
     * SN导入的excel表头模板
     */
    private static final String[] SN_COLUMNS = {"序号", "SN号", "去向", "发货日期", "内控编号", "批号"};


    /**
     * 当前上传文件的文件名称
     */
    private String fileName;

    /**
     * 当前上传文件的文件名称
     */
    private final String fileType = ".xlsx";

    public String processUploadFile(MultipartFile file) {
        if (file == null) {
            log.warn("上传的文件为空。");
            throw new RuntimeException("上传的文件为空。");
        }
        //取得当前上传文件的文件名称
        fileName = file.getOriginalFilename();
        log.info("Received upload file: " + fileName);
        if (StringUtils.isBlank(fileName)) {
            log.error(String.format("Upload file is empty. "));
            throw new RuntimeException("上传的文件为空。");
        }
        //检查文件类型，如果后缀不为.xls则报错
        if (!fileName.toLowerCase().endsWith(fileType)) {
            log.info(String.format("Upload file is not a valid Excel file: ", fileName));
            throw new RuntimeException("上传的文件不是Excel.xlsx文件：" + fileName);
        }
        //记录上传过程起始时的时间，用来计算上传时间
        long pre = System.currentTimeMillis();
        InputStream inputStream;
        try {
            //-->step2:解析上传的excel数据文件。
            inputStream = parseFile2InputStream(file);
        } catch (IOException e) {
            log.info("Upload file read error.", e);
            throw new RuntimeException(String.format("读取上传文件:%s时出错，原因：%s。", fileName, e.getMessage()));
        } catch (Exception e) {
            log.info("Process data failed", e);
            throw new RuntimeException(String.format("处理上传文件数据时出错，原因：%s。", e.getMessage()));
        }

        Workbook workbook = null;
        try {
            workbook = new XSSFWorkbook(inputStream); // 解析2007版本
        } catch (Exception ex) {
            log.info("importData------------------------>XSSFWorkbook Read InputStream Exception");
            try {
                POIFSFileSystem pfs = new POIFSFileSystem(inputStream); // 解析2003版本
                workbook = new HSSFWorkbook(pfs);
            } catch (IOException e) {
                log.info("importData------------------------>HSSFWorkbook Read InputStream Exception");
            }
        }
        //只读取第一个sheet页。
        String sn = workbook.getSheetName(0);

        log.info("Parsing and process sn of {}...", sn);
        Sheet sheet = workbook.getSheetAt(0);
        //-->step3:读取每个sheet中的数据。
        readSheetAndConvert(sheet);

        try {
            workbook.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        //记录上传该文件后的时间
        long finalTime = System.currentTimeMillis();
        log.info("Parsed  file: {}, elapsed time ：{}ms.", fileName, finalTime - pre);
        return fileName;
    }

    /**
     * step1:处理上传文件请求request。
     *
     * @return
     */
    public String processUploadFile(HttpServletRequest request) {

        //获得上传的文件
        MultipartFile file = UploadFileUtils.getFirstUploadFile(request);
        return processUploadFile(file);
    }


    /**
     * step2:解析上传的excel数据文件。
     */
    public InputStream parseFile2InputStream(MultipartFile file) throws IOException {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream(); // 取得输入流
        } catch (IOException e) {
            log.info("importData------------------------>InputStream IOException");
        }
        return inputStream;
    }

    /**
     * step3:读取每个sheet中的数据。
     *
     * @param sheet Sheet实例。
     * @return
     */
    private void readSheetAndConvert(Sheet sheet) {
        //第一行: 列名。
        int firstRowIndex = sheet.getFirstRowNum();
        Row row = sheet.getRow(firstRowIndex);

        //列名的index和名字之间的对应关系为actualColNames。
        Map<Integer, String> actualColNames = new HashMap<>(9);
        for (int i = row.getFirstCellNum(); i <= row.getLastCellNum(); i++) {
            Cell cc = row.getCell(i);
            String value = ExcelUtil.getValueFromCell(cc);
            if (StringUtils.isBlank(value)) {
                continue;
            }
            actualColNames.put(i, value.trim());
        }
        List<SnToRecordEntity> snList = new ArrayList<>();
        for (int i = firstRowIndex + 1; i <= sheet.getLastRowNum(); i++) {
            row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            //-->step4:处理每一行数据
            SnToRecordEntity sn = parseUserRow(row, actualColNames);
            if (sn == null) {
                //此行有问题，忽略。
                continue;
            }
            sn.setCreateTime(new Date());
            sn.setModifyTime(new Date());
            snList.add(sn);
        }
        if (!CollectionUtils.isEmpty(snList)) {
            for (SnToRecordEntity snToRecordEntity : snList) {
                log.info("sn " + snToRecordEntity.toString());
            }
        }
        saveSnToRecord(snList);
    }

    private void saveSnToRecord(List<SnToRecordEntity> snList) {
        List<SnToRecordEntity> snSaveList = new ArrayList<>();
        for (SnToRecordEntity snToRecordEntity : snList) {
            SnToRecordEntity dbRecord = snToRecordDAO.getBySn(snToRecordEntity.getSn());
            if (!ObjectUtils.isEmpty(dbRecord)) {
                if (snToRecordEntity.getBatchId().equals(dbRecord.getBatchId())) {
                    //更新旧的，但不添加新的（不采用删除）
                    dbRecord.setSequence(snToRecordEntity.getSequence());
                    dbRecord.setControlId(snToRecordEntity.getControlId());
                    dbRecord.setDeliverDate(snToRecordEntity.getDeliverDate());
                    dbRecord.setDirection(snToRecordEntity.getDirection());
                    dbRecord.setModifyTime(new Date());
                    snToRecordDAO.updateById(dbRecord);
                    continue;
                } else {
                    //更新
                    String newSn;
                    SnToRecordEntity newRecord;
                    do {
                        newSn = dbRecord.getSn() + "-" + new Random().nextInt(100);
                        newRecord = snToRecordDAO.getBySn(newSn);
                        //newRecord != null,说明该随机生成的数据在数据库存在
                    } while (newRecord != null);
                    dbRecord.setSn(newSn);
                    dbRecord.setModifyTime(new Date());
                    snToRecordDAO.updateById(dbRecord);
                }
            }
            snSaveList.add(snToRecordEntity);
        }
        snToRecordDAO.saveBatch(snSaveList);
    }

    /**
     * step4:处理每一行数据，处理前先验证数据对应的列是否在预定的列中。
     *
     * @param row
     * @param actualColNames
     * @return
     */
    private SnToRecordEntity parseUserRow(Row row, Map<Integer, String> actualColNames) {
        SnToRecordEntity sn = new SnToRecordEntity();
        for (Map.Entry<Integer, String> kv : actualColNames.entrySet()) {
            int col = kv.getKey();
            String colName = kv.getValue();
            Cell cell = row.getCell(col);
            //数据对应的列是否在预定的列中。
            boolean exist = false;
            for (int i = 0; i < SN_COLUMNS.length; i++) {
                if (colName.equals(SN_COLUMNS[i])) {
                    exist = true;
                    break;
                }
            }
            if (!exist) {
                log.warn("行：{},列：{}({})不在系统内置表字段中，将忽略。", "" + row.getRowNum(), "" + col, colName);
                continue;
            }
            String value = ExcelUtil.getValueFromCell(cell);
            //            try {
            //-->step5:处理每一行各个字段（不可为空）
            processColumn(value, colName, sn);
            //            } catch (Exception ex) {
            //                log.error(String.format("处理第%s行时出现系统错误: " + ex.getMessage(), row.getRowNum()), ex);
            //                break;
            //           }
        }
        return sn;
    }

    /**
     * step5:处理每一行各个字段
     * <p>
     * "序号", "SN号", "去向", "发货日期", "内控编号", "批号"
     *
     * @param value
     * @param colName
     * @return
     */
    private void processColumn(String value, String colName, SnToRecordEntity sn) {
        if (StringUtils.isBlank(value)) {
            throw new BackendException(colName + "不允许传空");
        }
        if (colName.equals(SN_COLUMNS[0])) {
            sn.setSequence(Long.valueOf(value));
        } else if (colName.equals(SN_COLUMNS[1])) {
            sn.setSn(value);
        } else if (colName.equals(SN_COLUMNS[2])) {
            sn.setDirection(value);
        } else if (colName.equals(SN_COLUMNS[3])) {
            sn.setDeliverDate(value);
        } else if (colName.equals(SN_COLUMNS[4])) {
            sn.setControlId(value);
        } else if (colName.equals(SN_COLUMNS[5])) {
            sn.setBatchId(value);
        } else {
        }

    }


}
package com.mira.backend.controller;

import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.backend.pojo.dto.mfr.MfrNetworkDTO;
import com.mira.core.annotation.Anonymous;
import com.mira.redis.cache.RedisComponent;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-11-10
 **/
@Api(tags = "14.mfr network connection")
@RestController
@RequestMapping("mfr")
@Slf4j
public class MfrNetworkController {
    @Resource
    private RedisComponent redisComponent;

    @GetMapping("/network")
    @Anonymous
    public MfrNetworkDTO mfrNetwork() {
        String cacheKey = RedisCacheKeyConst.MFR_NETWORK;
        MfrNetworkDTO mfrNetworkDTO;
        try {
            mfrNetworkDTO = redisComponent.get(cacheKey, MfrNetworkDTO.class);
        } catch (Exception e) {
            log.error("get mfr network cache error", e);
            mfrNetworkDTO = null;
        }
        return mfrNetworkDTO;
    }

    @PostMapping("/network")
    @Anonymous
    public void mfrNetwork(@RequestBody MfrNetworkDTO mfrNetworkDTO) {
        String cacheKey = RedisCacheKeyConst.MFR_NETWORK;
        //        MfrNetworkDTO existMfrNetworkDTO = redisComponent.get(cacheKey, MfrNetworkDTO.class);
        redisComponent.set(cacheKey, mfrNetworkDTO);
    }
}

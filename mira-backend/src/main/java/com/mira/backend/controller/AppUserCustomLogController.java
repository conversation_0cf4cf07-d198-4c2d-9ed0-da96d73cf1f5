package com.mira.backend.controller;


import com.mira.api.user.dto.backend.BackendCustomLogDTO;
import com.mira.api.user.dto.backend.BackendCustomLogResponseDTO;
import com.mira.api.user.dto.backend.UserIdPageDTO;
import com.mira.api.user.provider.IUserToBackendProvider;
import com.mira.backend.service.IAdminLoginService;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-05-15
 **/
@Api(tags = "06.app用户custom log数据管理")
@RestController
@RequestMapping("app/user-custom-log")
@Slf4j
public class AppUserCustomLogController {
    @Resource
    private IAdminLoginService adminLoginService;
    @Resource
    private IUserToBackendProvider userToBackendProvider;

    @ApiOperation("1. 用户custom log数据分页列表")
    @PostMapping("/page")
    public PageResult<BackendCustomLogDTO> customLogPage(@RequestBody UserIdPageDTO pageParam) {
        Long userId = pageParam.getUserId();
        //adminLoginService.checkUserInfoPermission(userId);
        BackendCustomLogResponseDTO customLogResponseDTO = userToBackendProvider.customLogPage(pageParam).getData();
        return new PageResult<>(customLogResponseDTO.getBackendCustomLogDTOS(), customLogResponseDTO.getTotal(), pageParam.getSize(),
                pageParam.getCurrent());
    }
}

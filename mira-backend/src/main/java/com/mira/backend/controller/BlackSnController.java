package com.mira.backend.controller;

import com.mira.api.user.dto.backend.BackendBlackSnResponseDTO;
import com.mira.api.user.dto.backend.BlackSnDTO;
import com.mira.api.user.dto.backend.BlackSnPageDTO;
import com.mira.api.user.dto.backend.CreateBlackSnDTO;
import com.mira.api.user.provider.IUserToBackendProvider;
import com.mira.backend.config.logs.AdminLog;
import com.mira.backend.pojo.param.user.EnableBlackSnParam;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-18
 **/
@Api(tags = "07.sn黑名单管理")
@RestController
@RequestMapping("sn/black")
@Slf4j
public class BlackSnController {
    @Resource
    private IUserToBackendProvider userToBackendProvider;

    @ApiOperation("1. 黑名单分页列表")
    @PostMapping("/page")
    public PageResult<BlackSnDTO> blackSnDataPage(@RequestBody BlackSnPageDTO pageParam) {
        BackendBlackSnResponseDTO backendBlackSnResponseDTO = userToBackendProvider.blackSnDataPage(pageParam).getData();
        return new PageResult<>(backendBlackSnResponseDTO.getBlackSnDTOS(), backendBlackSnResponseDTO.getTotal(),
                pageParam.getSize(),
                pageParam.getCurrent());
    }

    @ApiOperation("2. 创建黑名单")
    @PostMapping("/create")
    @AdminLog("创建黑名单")
    public Long create(@RequestBody CreateBlackSnDTO createBlackSnDTO) {
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        createBlackSnDTO.setAdminId(adminId);
        return userToBackendProvider.createBlackSn(createBlackSnDTO).getData();
    }

    @ApiOperation("3. 将黑名单激活或者置为未激活")
    @PostMapping("/enable")
    @AdminLog("将黑名单激活或者置为未激活")
    public void enable(@Valid @RequestBody EnableBlackSnParam enableBlackSnParam) {
        Long adminId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String sn = enableBlackSnParam.getSn();
        Integer enable = enableBlackSnParam.getEnable();
        userToBackendProvider.enableBlackSn(sn, enable, adminId);
    }

    @ApiOperation("4. 移除黑名单")
    @PostMapping("/delete")
    @AdminLog("移除黑名单")
    public void delete(@RequestParam String sn) {
        userToBackendProvider.deleteBlackSn(sn);
    }
}

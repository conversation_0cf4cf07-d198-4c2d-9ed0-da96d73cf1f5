package com.mira.backend.controller;

import com.mira.api.user.dto.backend.HomeBannerDTO;
import com.mira.backend.service.impl.HomeBannerServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * banner 管理
 *
 * <AUTHOR>
 */
@Api(tags = "08.banner 管理")
@RestController
@RequestMapping("banner")
public class HomeBannerController {
    @Resource(name = "deskHomeBannerService")
    private HomeBannerServiceImpl homeBannerServiceImpl;

    @ApiOperation(value = "后台系统banner列表")
    @GetMapping("/list")
    public List<HomeBannerDTO> all() {
        return homeBannerServiceImpl.listRecord();
    }

    @ApiOperation(value = "添加banner")
    @PostMapping("/add")
    public void add(@RequestBody HomeBannerDTO homeBannerDTO) {
        homeBannerServiceImpl.addRecord(homeBannerDTO);
    }

    @ApiOperation(value = "修改banner")
    @PostMapping("/edit")
    public void edit(@RequestBody HomeBannerDTO homeBannerDTO) {
        homeBannerServiceImpl.editRecord(homeBannerDTO);
    }

    @ApiOperation(value = "删除banner")
    @PostMapping("/delete")
    public void delete(@RequestBody List<Integer> ids) {
        homeBannerServiceImpl.deleteRecord(ids);
    }

    @ApiOperation(value = "修改排序")
    @PostMapping("/edit/orders")
    public void editOrders(@RequestBody List<String> groupUidList) {
        homeBannerServiceImpl.editOrders(groupUidList);
    }

    @ApiOperation(value = "激活")
    @PostMapping("/active/{groupUid}")
    public void active(@PathVariable String groupUid) {
        homeBannerServiceImpl.active(groupUid);
    }

    @ApiOperation(value = "取消激活")
    @PostMapping("/inactive/{groupUid}")
    public void inactive(@PathVariable String groupUid) {
        homeBannerServiceImpl.inactive(groupUid);
    }
}

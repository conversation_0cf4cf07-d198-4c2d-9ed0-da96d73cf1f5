package com.mira.backend.exception;

import com.mira.core.annotation.IgnoreLog;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import feign.codec.DecodeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.core.Ordered;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户服务异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@ResponseBody
@RestControllerAdvice
public class BackendExceptionHandler implements Ordered {
    @ExceptionHandler(BackendException.class)
    public CommonResult<String> userExHandler(HttpServletRequest request, HandlerMethod handlerMethod, BackendException ex) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        String userId = ObjectUtils.isNotEmpty(loginInfo) ? loginInfo.getId().toString() : "Anonymous";
        String url = request.getRequestURI();
        if (handlerMethod.hasMethodAnnotation(IgnoreLog.class)) {
            return CommonResult.FAILED(ex.getCode(), ex.getMsg());
        }
        log.info("user:{}, url:{}, code:{}, msg:{}", userId, url, ex.getCode(), ex.getMsg());
        return CommonResult.FAILED(ex.getCode(), ex.getMsg());
    }

    @ExceptionHandler(DecodeException.class)
    public CommonResult<String> decodeExHandler(HttpServletRequest request, DecodeException ex) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        String userId = ObjectUtils.isNotEmpty(loginInfo) ? loginInfo.getId().toString() : "Anonymous";
        String url = request.getRequestURI();
        log.info("user:{}, url:{}, msg:{}", userId, url, ex.getMessage());
        return CommonResult.FAILED(500, ex.getMessage());
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE + 8;
    }
}

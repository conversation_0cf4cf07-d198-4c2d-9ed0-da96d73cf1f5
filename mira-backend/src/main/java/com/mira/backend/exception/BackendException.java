package com.mira.backend.exception;

import com.mira.core.response.enums.BaseCodeEnum;
import com.mira.core.response.enums.ICodeEnum;
import lombok.Getter;

/**
 * 用户服务相关异常
 *
 * <AUTHOR>
 */
@Getter
public class BackendException extends RuntimeException {
    private final Integer code;
    private final String msg;

    public BackendException(ICodeEnum iCodeEnum) {
        super(iCodeEnum.getMsg());
        this.code = iCodeEnum.getCode();
        this.msg = iCodeEnum.getMsg();
    }

    public BackendException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public BackendException(String msg) {
        super(msg);
        this.code = BaseCodeEnum.INTERNAL_SERVER_ERROR.getCode();
        this.msg = msg;
    }
}

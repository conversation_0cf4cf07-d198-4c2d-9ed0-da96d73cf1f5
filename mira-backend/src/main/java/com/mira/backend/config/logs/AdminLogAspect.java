package com.mira.backend.config.logs;


import com.mira.backend.dal.dao.admin.AdminOperateLogDAO;
import com.mira.backend.dal.entity.admin.AdminOperateLogEntity;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.IpUtils;
import com.mira.core.util.JsonUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Aspect
@Component
public class AdminLogAspect {
    @Resource
    private AdminOperateLogDAO adminOperateLogDAO;

    @Pointcut("@annotation(com.mira.backend.config.logs.AdminLog)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;

        //保存日志
        saveAdminOperateLog(point, time);

        return result;
    }

    private Map<String, Object> getFieldsNameValueMap(JoinPoint joinPoint) throws Exception {
        //参数值
        Object[] args = joinPoint.getArgs();
        // 参数名
        String[] argNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        Map<String, Object> map = new HashMap<String, Object>();
        for (int i = 0; i < argNames.length; i++) {
            if (!"request".equals(argNames[i])) {
                map.put(argNames[i], args[i]);
            }
        }
        return map;
    }

    private void saveAdminOperateLog(ProceedingJoinPoint joinPoint, long time) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        AdminOperateLogEntity adminLogEntity = new AdminOperateLogEntity();
        AdminLog adminLog = method.getAnnotation(AdminLog.class);
        if (adminLog != null) {
            //注解上的描述
            adminLogEntity.setOperation(adminLog.value());
        }

        //请求的方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();
        adminLogEntity.setMethod(className + "." + methodName + "()");

        //请求的参数
        try {
            Map<String, Object> fieldsNameValueMap = getFieldsNameValueMap(joinPoint);
            String params = JsonUtil.toJson(fieldsNameValueMap);
            if (params.length() > 10000) {
                params = params.substring(0, 10000);
            }
            adminLogEntity.setParams(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //获取request
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        //设置IP地址
        adminLogEntity.setIp(IpUtils.getIp(request));
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        adminLogEntity.setAdminId(loginInfo == null ? null : loginInfo.getId());
        adminLogEntity.setName(loginInfo == null ? null : loginInfo.getUsername());
        adminLogEntity.setTime(time);
        adminLogEntity.setCreator(loginInfo == null ? null : loginInfo.getId());
        adminLogEntity.setModifier(loginInfo == null ? null : loginInfo.getId());
        adminLogEntity.setCreateTime(new Date());
        adminLogEntity.setModifyTime(new Date());
        //保存系统日志
        adminOperateLogDAO.save(adminLogEntity);
    }
}

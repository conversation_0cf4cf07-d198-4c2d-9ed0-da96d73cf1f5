package com.mira.backend.consts.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description 通知开关
 */
@Getter
public enum NotificationSwitchEnum {
    CYCLE_PHASE_SWITCH("cyclePhaseFlag", "周期信息推送开关"),
    PURCHASE_SWITCH("purchaseFlag", "购买建议推送开关"),
    HIDE_CONTENT_SWITCH("hideContentFlag", "通知栏内容隐藏开关"),
    FERTILITY_PLUS_WANDS_SWITCH("fertilityPlusWands", "Fertility Plus 试剂测试提醒开关"),
    FERTILITY_CONFIRM_WANDS_SWITCH("fertilityConfirmWands", "Fertility Confirm 试剂测试提醒开关"),
    MAX_WANDS_SWITCH("maxWands", "Max 试剂测试提醒开关"),
    OVUM_WANDS_SWITCH("ovumWands", "Ovum 试剂测试提醒开关");

    private final String value;
    private final String desc;

    NotificationSwitchEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static NotificationSwitchEnum get(String switchName) {
        for (NotificationSwitchEnum notificationSwitchEnum : NotificationSwitchEnum.values()) {
            if (notificationSwitchEnum.getValue().equals(switchName)) {
                return notificationSwitchEnum;
            }
        }
        return null;
    }
}

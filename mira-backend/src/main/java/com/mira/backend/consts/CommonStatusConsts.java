package com.mira.backend.consts;

/**
 * <AUTHOR>
 * @description 通用状态
 */
public class CommonStatusConsts {
    /**
     * 激活
     */
    public final static Integer ACTIVE = 1;
    /**
     * 未激活
     */
    public final static Integer IN_ACTIVE = 0;

    /**
     * 已过期
     */
    public final static Integer EXPIRE = 1;
    /**
     * 未过期
     */
    public final static Integer NO_EXPIRE = 0;

    /**
     * 正常
     */
    public final static Integer NORMAL = 0;
    /**
     * 已删除
     */
    public final static Integer DELETED = 1;

    /**
     * 打开
     */
    public final static Integer OPEN = 1;
    /**
     * 关闭
     */
    public final static Integer CLOSE = 0;

    /**
     * 显示
     */
    public final static Integer SHOW = 1;
    /**
     * 隐藏
     */
    public final static Integer HIDDEN = 0;

    /**
     * testing schedule display case
     */
    public final static Integer SCHEDULE_NOT_DISPLAY = -1;
    public final static Integer SCHEDULE_OFF = 0;
    public final static Integer SCHEDULE_ON = 1;
    public final static Integer SCHEDULE_ALWAYS_ON = 2;
}

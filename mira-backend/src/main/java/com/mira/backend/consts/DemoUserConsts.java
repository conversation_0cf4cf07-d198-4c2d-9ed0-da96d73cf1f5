package com.mira.backend.consts;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-12-08
 **/
public class DemoUserConsts {
    /**
     * <EMAIL> ~ <EMAIL>
     */
    public static List<Long> HEALTHY_USERIDS = Arrays.asList(523723L, 523725L, 523727L, 523729L);
    /**
     * <EMAIL> ~ <EMAIL>
     */
    public static List<Long> PCOS_USERIDS = Arrays.asList(523718L, 523720L, 523721L, 523722L);

    /**
     * QC account
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     * <EMAIL>
     */
    public static List<Long> QC_USERIDS = Arrays.asList(489316L,549473L,549474L,549475L,549476L,549477L,549478L,549479L,549480L,466437L,556977L,556978L,556979L,556980L,556981L,556982L,
            556983L,556984L,558425L,558426L,466438L,558427L,558428L,558429L,466440L,589744L,589745L,589746L,589747L,488227L,488228L,489312L,489313L,489314L,489315L);
}

package com.mira.backend.pojo.vo.user;

import com.mira.backend.pojo.dto.ClinicTrialUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-05-12
 **/
@Data
@ApiModel
public class MoodAndWellBeingTrialUserVO extends ClinicTrialUserDTO {
    @ApiModelProperty(value = "Number of Mood & Well Being logged")
    private Long countOfMoodAndWellBeing = 0L;

    @ApiModelProperty(value = "Number of Symptoms logged")
    private Long countOfSymptoms = 0L;
}

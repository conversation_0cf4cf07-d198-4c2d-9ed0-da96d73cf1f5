package com.mira.backend.pojo.param.user;

import com.mira.core.request.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-25
 **/
@Data
public class ClinicTrialPageParam extends PageDTO {
    @ApiModelProperty("类型:0:hcg临床；1:moods&well being临床")
    private Integer type;
    @ApiModelProperty("临床批次")
    private Integer batch;

    @ApiModelProperty("搜索关键词")
    private String keyword;
}

package com.mira.backend.pojo.param.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-03-31
 **/
@Data
@ApiModel(description = "手动添加测试数据发送通知请求参数")
public class DataManualNotificationParam {
    private Long id;
    @ApiModelProperty(value = "29:Your data was added;30:Your data was synchronized;31:Your data was not added to your profile", required = true)
    private Long notificationDefineId;
}

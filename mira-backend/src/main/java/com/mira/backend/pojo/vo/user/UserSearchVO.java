package com.mira.backend.pojo.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: xizhao.dai
 * @since: 2021-08-24
 * <p>
 * status 状态  0：禁用   1：正常	2:未激活
 * <p>
 * source 来源：0:v2数据同步；1:v3新数据;2:<PERSON><PERSON>医生创建的用户;3:<PERSON><PERSON>医生;4:v4新数据
 * <p>
 * transferFlag  0:v2用户未同步或者v3用户未同步，1:v2用户已同步到v3，2:已同步到v4
 * <p>
 * step1: status=2 --> 激活 （不跳转）
 * step2: source=0 && transferFlag=0  --> v2用户未同步 （不跳转）
 * step3: (source=0 && transferFlag=1) || (source=【1、2、3】 && transferFlag=0)  --> 跳转到v3界面
 * step4: transferFlag=2  --> 跳转到v4界面
 * <p>
 * step5: 未完成注册的用户
 */
@Data
public class UserSearchVO {
    private Long id;
    private String email;
    private String mobile;
    private Integer status;
    /**
     * 绑定设备
     */
    private String sn;

    /**
     * 0，1，2，3 都不能跳转到用户详情界面
     */
    @ApiModelProperty(value = "-1:已删除；0：未激活；1：v2用户未迁移；3：跳转到v3界面；4跳转到v4界面；5未完成注册流程的用户")
    private Integer turnFlag;
}

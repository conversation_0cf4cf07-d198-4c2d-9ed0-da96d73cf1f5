package com.mira.backend.pojo.vo.admin;


import com.mira.backend.util.tree.TreeNode;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AdminRoleV2VO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 角色code
     */
    private String code;

    /**
     * 角色名称
     */
    private String name;
    private String nameEn;

    /**
     * 角色描述
     */
    private String description;

    private List<TreeNode<AdminPermissionVO>> permissions = new ArrayList<>();


}

package com.mira.backend.pojo.param.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 激活仪器黑名单
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2021-10-15
 **/
@Data
public class EnableBlackSnParam {
    @ApiModelProperty(value = "sn", required = true)
    @NotBlank(message = "sn不能为空")
    private String sn;

    @ApiModelProperty(value = "是否激活：0未激活（默认）；1激活", required = true)
    private Integer enable;
}

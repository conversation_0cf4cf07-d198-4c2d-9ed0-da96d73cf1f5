package com.mira.backend.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-26
 **/
@Data
public class ClinicTrialBatchDTO {
    @ApiModelProperty(value = "code")
    private String code;
    @ApiModelProperty(value = "name")
    private String name;
    @ApiModelProperty(value = "icon")
    private String icon;
    @ApiModelProperty(value = "batch")
    private Integer batch;
    @ApiModelProperty(value = "batchName")
    private String batchName;
    @ApiModelProperty(value = "batch")
    private Integer type;
    @ApiModelProperty(value = "创建时间")
    private String createTimeStr;
}


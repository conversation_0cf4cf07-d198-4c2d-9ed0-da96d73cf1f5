package com.mira.backend.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-25
 **/
@Data
public class ClinicTrialUserDTO {
    @ApiModelProperty(value = "用户id")
    private Long id;
    @ApiModelProperty(value = "email")
    private String email;
    @ApiModelProperty(value = "用户名")
    private String nickname;
    @ApiModelProperty(value = "出生年份")
    private Integer birthYear;

    @ApiModelProperty(value = "出生月份")
    private Integer birthMonth;

    @ApiModelProperty(value = "出生日期")
    private Integer birthOfDay;

    @ApiModelProperty(value = "timeZone")
    private String timeZone;

}

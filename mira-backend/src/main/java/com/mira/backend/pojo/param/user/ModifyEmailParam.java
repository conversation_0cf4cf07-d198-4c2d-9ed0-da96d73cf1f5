package com.mira.backend.pojo.param.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ModifyEmailParam {
    @ApiModelProperty(value = "用户id", required = true)
    @NotNull(message = "用户id不能为空")
    private Long userId;
    @ApiModelProperty(value = "当前email", required = true)
    @NotBlank(message = "当前email不能为空")
    private String oldEmail;
    @ApiModelProperty(value = "新email", required = true)
    @NotBlank(message = "新email不能为空")
    private String newEmail;

}

package com.mira.backend.pojo.vo.user;

import com.mira.backend.pojo.dto.ClinicTrialUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-25
 **/
@Data
@ApiModel
public class HcgClinicTrialUserVO extends ClinicTrialUserDTO {


    @ApiModelProperty(value = "用户有效的测试数据数量")
    private Integer resultsCount = 0;

    @ApiModelProperty(value = "用户有效的hcg测试数据数量")
    private Integer countOfHcg = 0;
    @ApiModelProperty(value = "用户有效的阳性的hcg测试数据数量")
    private Integer countOfPositiveHcg = 0;

    @ApiModelProperty(value = "用户年龄")
    private Integer age;
    @ApiModelProperty(value = "出生日期")
    private String birthday;
    @ApiModelProperty(value = "周期长度")
    private Integer avgLenCycle;
    @ApiModelProperty(value = "经期长度")
    private Integer avgLenPeriod;
    @ApiModelProperty(value = "算法计算周期长度")
    private Integer analysisLenCycle;
    @ApiModelProperty(value = "算法计算经期长度")
    private Integer analysisLenPeriod;

}

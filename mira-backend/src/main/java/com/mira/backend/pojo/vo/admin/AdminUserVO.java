package com.mira.backend.pojo.vo.admin;


import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AdminUserVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 管理员用户名
     */
    private String name;

    /**
     * 真实姓名
     */
    private String realName;

    private List<AdminRoleVO> roles = new ArrayList<>();


}

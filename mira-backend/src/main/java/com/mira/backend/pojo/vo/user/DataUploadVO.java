package com.mira.backend.pojo.vo.user;


import com.mira.api.bluetooth.dto.backend.AppDataUploadDTO;
import com.mira.api.bluetooth.dto.backend.ScanDataResultParameterDTO;
import lombok.Data;

import java.util.List;

/**
 * 测量记录
 */
@Data
public class DataUploadVO extends AppDataUploadDTO {
    /**
     * 半基线宽
     */
    private Double uHalfBaseWidth1;
    private Double uHalfBaseWidth2;
    private Double uHalfBaseWidth3;

    /**
     * 平均基线
     */
    private Double avgBaseLine;
    /**
     * t1c斜率
     */
    private Double t1cBaseLinexielv;
    /**
     * t2c斜率
     */
    private Double t2cBaseLinexielv;
    /**
     * 跑版校验结果
     */
    private Boolean checkDataResult;

    /**
     * sn批号
     */
    private String snBatchId;

    /**
     * 扫描数据解析结果
     */
    private List<Float> scanDataArray;

    /**
     * 扫描数据解析额外参数，当前仅针对结果版本为4和试剂类型为18的
     */
    private ScanDataResultParameterDTO scanDataResultParameter;
    /**
     *
     */
    private String max2WarningFlag;
}

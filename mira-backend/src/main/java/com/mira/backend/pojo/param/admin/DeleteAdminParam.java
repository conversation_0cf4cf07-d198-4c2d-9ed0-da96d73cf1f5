package com.mira.backend.pojo.param.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-06-01
 **/
@Getter
@Setter
public class DeleteAdminParam {
    @ApiModelProperty(value = "管理员id", required = true)
    @NotNull(message = "管理员id不能为空")
    private Long adminId;
}

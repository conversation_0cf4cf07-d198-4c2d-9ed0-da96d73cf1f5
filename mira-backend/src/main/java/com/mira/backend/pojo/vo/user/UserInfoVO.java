package com.mira.backend.pojo.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class UserInfoVO {
    @ApiModelProperty(value = "用户id")
    private Long id;
    @ApiModelProperty(value = "email")
    private String email;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    @ApiModelProperty(value = "用户创建时的时区")
    private String timeZone;
    @ApiModelProperty(value = "用户名")
    private String nickname;
    @ApiModelProperty(value = "用户年龄")
    private Integer age;
    @ApiModelProperty(value = "出生日期")
    private String birthday;
    @ApiModelProperty(value = "人生状态")
    private String goal;
    @ApiModelProperty(value = "sn")
    private String sn;

    @ApiModelProperty(value = "去向")
    private String direction;
    /**
     * 批号
     */
    @ApiModelProperty(value = "批号")
    private String batchId;


    @ApiModelProperty(value = "周期长度")
    private String avgLenCycle;
    @ApiModelProperty(value = "经期长度")
    private String avgLenPeriod;
    @ApiModelProperty(value = "上次使用设备")
    private String lastDivice;
    /**
     * 用户来源
     */
    private String source;

    @ApiModelProperty(value = "用户当前本地时间")
    private String localCurrentTime;
    @ApiModelProperty(value = "与国内时差")
    private String diffTime;

    @ApiModelProperty(value = "绑定firmware版本")
    private String bindVersion;

    @ApiModelProperty(value = "作为病人的信息")
    @Deprecated
    private Patient patient;
    @ApiModelProperty(value = "作为病人的信息")
    private List<Patient> patients = new ArrayList<>();
    @ApiModelProperty(value = "是否在MAX和Plus测试推荐的列表中")
    private Boolean inTestingScheduleExchange;

    @ApiModelProperty(value = "是否被定义为不规则周期;null未判断;0否;1是")
    private Integer definedIrregularCycle;

    @ApiModelProperty(value = "绑定标示：0没有绑定过；1绑定过;2:wait shipping")
    private Integer bindFlag;

    @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
    private Integer trackingMenopause;

    @Data
    @ApiModel("作为病人的信息")
    public static class Patient {
        /**
         * 租户code
         */
        private String tenantCode;
        /**
         * 病人账号状态:1:邀请中;2:正常激活状态;0:关闭
         */
        private Integer status;
    }

    @ApiModelProperty(value = "partner")
    private Partner partner;

    @Data
    @ApiModel("Partner")
    public static class Partner {
        @ApiModelProperty(value = "partner邮箱")
        private String partnerEmail;

        @ApiModelProperty(value = "partner状态:0:未激活1:已完成激活但未注册2:已完成注册")
        private String partnerStatus;
    }

    private Integer bbtBindStatus;
    private String bbtBindCreateTime;
}

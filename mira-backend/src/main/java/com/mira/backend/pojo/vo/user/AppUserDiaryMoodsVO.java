package com.mira.backend.pojo.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 用户 custom log 新增加的目录选项“Mood & Well Being”
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-03-11
 **/
@Data
@ApiModel("Moods")
public class AppUserDiaryMoodsVO {
    @ApiModelProperty(value = "时间，e.g: 2021-05-20")
    private String diaryDayStr;

    /**
     */
    @ApiModelProperty(value = "心情")
    private String mood;

    /**
     */
    @ApiModelProperty(value = "心情干扰")
    private String moodInterfering;

    /**
     */
    @ApiModelProperty(value = "Do you feel any of the following?")
    private String feeling;


    /**
     */
    @ApiModelProperty(value = "性欲")
    private String sexDrive;

    /**
     */
    @ApiModelProperty(value = "生产力")
    private String productivity;

    /**
     */
    @ApiModelProperty(value = "Cravings?")
    private String cravings;

    /**
     */
    @ApiModelProperty(value = "锻炼")
    private String exercise;

    /**
     */
    @ApiModelProperty(value = "skin")
    private String skin;

    /**
     */
    @ApiModelProperty(value = "外表")
    private String looks;
}

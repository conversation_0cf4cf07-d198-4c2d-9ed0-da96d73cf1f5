package com.mira.backend.pojo.param.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class EditRolePermissionParam {
    @ApiModelProperty(value = "角色id", required = true)
    @NotNull(message = "角色id不能为空")
    private Long roleId;
    @ApiModelProperty(value = "权限id集合", required = true)
//    @NotBlank(message = "权限id集合不能为空")
    private List<Long> permissionIds;
}

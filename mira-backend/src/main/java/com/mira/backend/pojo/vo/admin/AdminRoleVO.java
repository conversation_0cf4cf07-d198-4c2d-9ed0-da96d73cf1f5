package com.mira.backend.pojo.vo.admin;


import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AdminRoleVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 角色code
     */
    private String code;

    /**
     * 角色名称
     */
    private String name;
    private String nameEn;

    /**
     * 角色描述
     */
    private String description;

    private List<AdminPermissionVO> permissions = new ArrayList<>();


}

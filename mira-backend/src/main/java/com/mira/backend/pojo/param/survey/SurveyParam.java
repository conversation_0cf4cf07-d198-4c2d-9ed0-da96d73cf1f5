package com.mira.backend.pojo.param.survey;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-03-24
 **/
@Getter
@Setter
@ApiModel("创建App Survey的参数")
public class SurveyParam {
    @ApiModelProperty(value = "surveyId,为空表示创建", required = false)
    private String id;
    
    @ApiModelProperty(value = "survey name", required = false)
    private String name;

    @ApiModelProperty(value = "横幅", required = false)
    private Object banner;

    @ApiModelProperty(value = "survey json数据", required = true)
    private Object surveyContent;

    @ApiModelProperty(value = "发出时间", required = false)
    private String startTime;

    @ApiModelProperty(value = "过期时间", required = false)
    private String endTime;

    @ApiModelProperty(value = "额外信息", required = false)
    private Object extraJson;

    @ApiModelProperty(value = "条件 同notification", required = false)
    private Object conditions;
}

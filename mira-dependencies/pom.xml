<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mira</groupId>
    <artifactId>mira-dependencies</artifactId>
    <version>1.0</version>
    <name>mira-dependencies</name>
    <packaging>pom</packaging>
    <description>包版本依赖控制</description>

    <properties>
        <!-- maven -->
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>

        <!-- mira common -->
        <common.core.version>1.0</common.core.version>
        <common.swagger.version>1.0</common.swagger.version>
        <common.web.version>1.0</common.web.version>
        <common.rpc.version>1.0</common.rpc.version>
        <common.redis.version>1.0</common.redis.version>
        <common.mybatis.version>1.0</common.mybatis.version>
        <common.kafka.version>1.0</common.kafka.version>
        <common.mongo.version>1.0</common.mongo.version>

        <!-- spring boot -->
        <spring.boot.version>2.7.0</spring.boot.version>
        <!-- spring cloud -->
        <spring.cloud.version>2021.0.3</spring.cloud.version>
        <!-- mysql -->
        <mysql.version>8.0.29</mysql.version>
        <!-- druid -->
        <druid.version>1.2.9</druid.version>
        <!-- mybatis plus -->
        <mybatis.plus.version>3.5.1</mybatis.plus.version>
        <!-- nacos -->
        <nacos.version>2021.0.5.0</nacos.version>
        <!-- hutool -->
        <hutool.version>5.8.0</hutool.version>
        <!-- apache common -->
        <common.io.version>2.13.0</common.io.version>
        <!-- jackson -->
        <jackson.version>2.14.0</jackson.version>
        <!-- guava -->
        <guava.version>31.1-jre</guava.version>
        <!-- lombok -->
        <lombok.version>1.18.24</lombok.version>
        <!-- retrofit -->
        <retrofit.version>2.3.13</retrofit.version>
        <!-- okhttp -->
        <okhttp.version>11.8</okhttp.version>
        <!-- kafka -->
        <kafka.version>2.8.6</kafka.version>
        <!-- aws sdk -->
        <aws.sdk.version>1.12.217</aws.sdk.version>
        <!-- jjwt -->
        <jjwt.version>0.11.5</jjwt.version>
        <!-- boostrap -->
        <bootstrap.version>3.1.3</bootstrap.version>
        <!-- swagger -->
        <knife4j.version>3.0.3</knife4j.version>
        <!-- oauth2 -->
        <oauth2.version>2.2.5.RELEASE</oauth2.version>
        <!-- cglib -->
        <cglib.version>3.3.0</cglib.version>
        <!-- shiro -->
        <shiro.version>1.10.1</shiro.version>
        <!-- ognl -->
        <ognl.version>3.1.15</ognl.version>
        <!-- amplitude -->
        <amplitude.version>1.12.2</amplitude.version>
        <org.json.version>20231013</org.json.version>
        <!-- mapstruct -->
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <!-- firebase -->
        <firebase.version>9.3.0</firebase.version>
        <!-- sleuth -->
        <sleuth.version>3.1.8</sleuth.version>
        <!-- zipkin -->
        <zipkin.version>3.1.8</zipkin.version>
        <!-- brave mysql8 -->
        <brave.mysql8.version>6.0.3</brave.mysql8.version>
        <!-- logback-gelf -->
        <logback.gelf.version>4.0.2</logback.gelf.version>
        <!-- micrometer -->
        <prometheus.version>1.12.8</prometheus.version>
        <!-- ehcache -->
        <ehcache.version>3.10.8</ehcache.version>
        <!-- resilience4j -->
        <resilience4j.version>1.7.1</resilience4j.version>
    </properties>

    <!-- 统一管理依赖和版本号 -->
    <dependencyManagement>
        <dependencies>
            <!-- =============== mira common =============== -->

            <dependency>
                <groupId>com.mira</groupId>
                <artifactId>common-core</artifactId>
                <version>${common.core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mira</groupId>
                <artifactId>common-swagger-starter</artifactId>
                <version>${common.swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mira</groupId>
                <artifactId>common-web-starter</artifactId>
                <version>${common.web.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mira</groupId>
                <artifactId>common-rpc-starter</artifactId>
                <version>${common.rpc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mira</groupId>
                <artifactId>common-redis-starter</artifactId>
                <version>${common.redis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mira</groupId>
                <artifactId>common-mybatis-starter</artifactId>
                <version>${common.mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mira</groupId>
                <artifactId>common-kafka-starter</artifactId>
                <version>${common.kafka.version}</version>
            </dependency>

            <!-- =============== other jar =============== -->

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${nacos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${nacos.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${common.io.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.lianjiatech</groupId>
                <artifactId>retrofit-spring-boot-starter</artifactId>
                <version>${retrofit.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${kafka.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-core</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-kms</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-ses</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-gson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
                <version>${bootstrap.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-core</artifactId>
                <version>${shiro.version}</version>
            </dependency>

            <dependency>
                <groupId>ognl</groupId>
                <artifactId>ognl</artifactId>
                <version>${ognl.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amplitude</groupId>
                <artifactId>java-sdk</artifactId>
                <version>${amplitude.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${org.json.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>${firebase.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-sleuth</artifactId>
                <version>${sleuth.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-sleuth-zipkin</artifactId>
                <version>${zipkin.version}</version>
            </dependency>

            <dependency>
                <groupId>io.zipkin.brave</groupId>
                <artifactId>brave-instrumentation-mysql8</artifactId>
                <version>${brave.mysql8.version}</version>
            </dependency>

            <dependency>
                <groupId>de.siegmar</groupId>
                <artifactId>logback-gelf</artifactId>
                <version>${logback.gelf.version}</version>
            </dependency>

            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${prometheus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache.version}</version>
            </dependency>

            <!-- resilience4j -->
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring-boot2</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-circuitbreaker</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-ratelimiter</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-feign</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <!-- =============== 下面依赖始终保持在最下面 =============== -->

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-oauth2</artifactId>
                <version>${oauth2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>

package com.mira.sso.producer.emailfactory.partner;

import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.RsaUtil;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.producer.emailfactory.EmailTemplateFactory;
import com.mira.web.properties.RsaProperties;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Partner修改邮箱邮件模版工厂
 *
 * <AUTHOR>
 */
public class PartnerChangeEmailTempFactory implements EmailTemplateFactory<EmailTypeEnum> {
    private final RsaProperties rsaProperties;
    private final String email;
    private final String nickName;
    private final AppUserPartnerEntity partnerEntity;

    public PartnerChangeEmailTempFactory(RsaProperties rsaProperties, String email, String nickName, AppUserPartnerEntity partnerEntity) {
        this.rsaProperties = rsaProperties;
        this.email = email;
        this.nickName = nickName;
        this.partnerEntity = partnerEntity;
    }

    @Override
    public Map<String, String> createTemplate(EmailTypeEnum emailTypeEnum) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("partnerId", partnerEntity.getId());
        encryptMap.put("changeEmailTimestamp", System.currentTimeMillis());
        encryptMap.put("templateCode", emailTypeEnum.getCode());
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        Map<String, String> variableMap = new HashMap<>();
        variableMap.put("userHash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("oldPartnerFirstName", partnerEntity.getFirstName());
        variableMap.put("partnerNewEmail", email);
        variableMap.put("userNickName", nickName);
        variableMap.put("email", email);
        return variableMap;
    }


    public static Map<String, String> createTemplate(EmailTypeEnum emailTypeEnum, AppUserPartnerEntity partnerEntity,
                                                     String email, String nickName, RsaProperties rsaProperties) {
        return new PartnerChangeEmailTempFactory(rsaProperties, email, nickName, partnerEntity).createTemplate(emailTypeEnum);
    }
}

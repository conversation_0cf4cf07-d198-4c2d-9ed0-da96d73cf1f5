package com.mira.sso.producer.emailfactory.partner;

import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.RsaUtil;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.producer.emailfactory.EmailTemplateFactory;
import com.mira.web.properties.RsaProperties;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Partner忘记密码邮件模版工厂
 *
 * <AUTHOR>
 */
public class PartnerResetPwEmailTempFactory implements EmailTemplateFactory<AppUserPartnerEntity> {
    private final RsaProperties rsaProperties;

    public PartnerResetPwEmailTempFactory(RsaProperties rsaProperties) {
        this.rsaProperties = rsaProperties;
    }

    @Override
    public Map<String, String> createTemplate(AppUserPartnerEntity partnerEntity) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("partnerId", partnerEntity.getId());
        encryptMap.put("resetPwTimestamp", System.currentTimeMillis());
        encryptMap.put("templateCode", EmailTypeEnum.PARTNER_RESET_PW.getCode());
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        HashMap<String, String> variableMap = new HashMap<>();
        variableMap.put("userHash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("firstName", partnerEntity.getFirstName());
        variableMap.put("partnerEmail", partnerEntity.getEmail());
        variableMap.put("email", partnerEntity.getEmail());
        return variableMap;
    }

    public static Map<String, String> createTemplate(AppUserPartnerEntity partnerEntity, RsaProperties rsaProperties) {
        return new PartnerResetPwEmailTempFactory(rsaProperties).createTemplate(partnerEntity);
    }
}

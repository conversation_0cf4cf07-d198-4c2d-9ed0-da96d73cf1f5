package com.mira.sso.producer;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.AppPartnerDTO;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.api.message.dto.AppUserEmailDTO;
import com.mira.api.message.dto.PartnerEmailDTO;
import com.mira.api.message.dto.SendEmailDTO;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.util.StringUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.sso.dal.entity.AppUserInfoEntity;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.producer.emailfactory.ChangeEmailTempFactory;
import com.mira.sso.producer.emailfactory.RegisterEmailTempFactory;
import com.mira.sso.producer.emailfactory.ResetPasswordEmailTempFactory;
import com.mira.sso.producer.emailfactory.partner.*;
import com.mira.sso.service.dto.UserRegisterDTO;
import com.mira.web.properties.RsaProperties;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 邮件发送生产者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class EmailProducer {
    @Resource
    private RsaProperties rsaProperties;
    @Resource
    private SysDictProperties sysDictProperties;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private IMessageProvider messageProvider;

    /**
     * 发送用户注册邮件
     *
     * @param appUser         用户信息
     * @param userRegisterDTO 注册信息
     */
    public Map<String, String> userRegister(AppUserEntity appUser, UserRegisterDTO userRegisterDTO) {
        Map<String, String> emailVariable = RegisterEmailTempFactory.createTemplate(appUser, userRegisterDTO, rsaProperties);
        AppUserEmailDTO appUserEmailDTO = new AppUserEmailDTO();
        appUserEmailDTO.setAppUserDTO(BeanUtil.toBean(appUser, AppUserDTO.class));
        appUserEmailDTO.setEmailTypeEnum(EmailTypeEnum.USER_REGISTER);
        appUserEmailDTO.setEmailVariable(emailVariable);

        SendEmailDTO<AppUserEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.APP_USER.getType());
        sendEmailDTO.setEmailDTO(appUserEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 发送用户重置密码邮件
     *
     * @param appUser     用户信息
     * @param appUserInfo 用户详情
     */
    public Map<String, String> userResetPassword(AppUserEntity appUser, AppUserInfoEntity appUserInfo) {
        Map<String, String> emailVariable = ResetPasswordEmailTempFactory.createTemplate(appUser, appUserInfo, rsaProperties);
        AppUserEmailDTO appUserEmailDTO = new AppUserEmailDTO();
        appUserEmailDTO.setAppUserDTO(BeanUtil.toBean(appUser, AppUserDTO.class));
        appUserEmailDTO.setEmailTypeEnum(EmailTypeEnum.USER_RESET_PW);
        appUserEmailDTO.setEmailVariable(emailVariable);

        SendEmailDTO<AppUserEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.APP_USER.getType());
        sendEmailDTO.setEmailDTO(appUserEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 发送用户修改邮箱的邮件，发送到新邮箱
     *
     * @param newEmail    新邮箱
     * @param appUser     用户信息
     * @param appUserInfo 用户详情
     */
    public Map<String, String> userChangeEmailSendNew(String newEmail, AppUserInfoEntity appUserInfo) {
        Map<String, String> newEmailVariableMap = ChangeEmailTempFactory
                .createTemplate(EmailTypeEnum.USER_CHANGE_EMAIL_TO_NEW_EMAIL, newEmail, appUserInfo.getNickname(), rsaProperties);

        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setId(appUserInfo.getUserId());
        appUserDTO.setEmail(newEmail);
        appUserDTO.setTimeZone(appUserInfo.getTimeZone());

        AppUserEmailDTO appUserEmailDTO = new AppUserEmailDTO();
        appUserEmailDTO.setAppUserDTO(appUserDTO);
        appUserEmailDTO.setEmailTypeEnum(EmailTypeEnum.USER_CHANGE_EMAIL_TO_NEW_EMAIL);
        appUserEmailDTO.setEmailVariable(newEmailVariableMap);

        SendEmailDTO<AppUserEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.APP_USER.getType());
        sendEmailDTO.setEmailDTO(appUserEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return newEmailVariableMap;
    }

    /**
     * 发送用户修改邮箱的邮件，发送到旧邮箱
     *
     * @param oldEmail    旧邮箱
     * @param newEmail    新邮箱
     * @param appUserInfo 用户详情
     */
    public Map<String, String> userChangeEmailSendOld(String oldEmail, String newEmail, AppUserInfoEntity appUserInfo) {
        Map<String, String> oldEmailVariableMap = ChangeEmailTempFactory
                .createTemplate(EmailTypeEnum.USER_CHANGE_EMAIL_TO_OLD_EMAIL, newEmail, appUserInfo.getNickname(), rsaProperties);

        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setId(appUserInfo.getUserId());
        appUserDTO.setEmail(oldEmail);
        appUserDTO.setTimeZone(appUserInfo.getTimeZone());

        AppUserEmailDTO appUserEmailDTO = new AppUserEmailDTO();
        appUserEmailDTO.setAppUserDTO(appUserDTO);
        appUserEmailDTO.setEmailTypeEnum(EmailTypeEnum.USER_CHANGE_EMAIL_TO_OLD_EMAIL);
        appUserEmailDTO.setEmailVariable(oldEmailVariableMap);

        SendEmailDTO<AppUserEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.APP_USER.getType());
        sendEmailDTO.setEmailDTO(appUserEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return oldEmailVariableMap;
    }

    /**
     * 邀请Partner
     *
     * @param partner     Partner
     * @param appUserInfo 用户详情
     */
    public Map<String, String> partnerInvite(AppUserPartnerEntity partner, AppUserInfoEntity appUserInfo) {
        Map<String, String> emailVariable = PartnerInviteEmailTempFactory.createTemplate(partner, appUserInfo, rsaProperties);

        PartnerEmailDTO partnerEmailDTO = new PartnerEmailDTO();
        partnerEmailDTO.setAppPartnerDTO(BeanUtil.toBean(partner, AppPartnerDTO.class));
        partnerEmailDTO.setEmailTypeEnum(EmailTypeEnum.USER_INVITE_PARTNER);
        partnerEmailDTO.setEmailVariable(emailVariable);

        SendEmailDTO<PartnerEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.PARTNER_USER.getType());
        sendEmailDTO.setEmailDTO(partnerEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 告知邀请Partner，发送给User
     *
     * @param appUser  AppUser
     * @param nickName Partner昵称
     * @return
     */
    public Map<String, String> invitePartnerInformUser(AppUserEntity appUser, String nickName) {
        Map<String, String> emailVariable = InformUserEmailTempFactory.createTemplate(appUser, nickName, rsaProperties);

        AppUserEmailDTO appUserEmailDTO = new AppUserEmailDTO();
        appUserEmailDTO.setAppUserDTO(BeanUtil.toBean(appUser, AppUserDTO.class));
        appUserEmailDTO.setEmailTypeEnum(EmailTypeEnum.USER_INFORM_PARTNER_EXIST_USER);
        appUserEmailDTO.setEmailVariable(emailVariable);

        SendEmailDTO<AppUserEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.APP_USER.getType());
        sendEmailDTO.setEmailDTO(appUserEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 告知邀请Partner，发送给Partner
     *
     * @param partner  Partner
     * @param nickName Partner昵称
     * @return
     */
    public Map<String, String> invitePartnerInformPartner(AppUserPartnerEntity partner, String nickName) {
        Map<String, String> emailVariable = InformPartnerEmailTempFactory.createTemplate(partner, nickName, rsaProperties);

        PartnerEmailDTO partnerEmailDTO = new PartnerEmailDTO();
        partnerEmailDTO.setAppPartnerDTO(BeanUtil.toBean(partner, AppPartnerDTO.class));
        partnerEmailDTO.setEmailTypeEnum(EmailTypeEnum.USER_INFORM_PARTNER_EXIST_PARTNER);
        partnerEmailDTO.setEmailVariable(emailVariable);

        SendEmailDTO<PartnerEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.PARTNER_USER.getType());
        sendEmailDTO.setEmailDTO(partnerEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 发送Partner重置密码邮件
     *
     * @param partner Partner
     */
    public Map<String, String> partnerResetPassword(AppUserPartnerEntity partner) {
        Map<String, String> emailVariable = PartnerResetPwEmailTempFactory.createTemplate(partner, rsaProperties);
        PartnerEmailDTO partnerEmailDTO = new PartnerEmailDTO();
        partnerEmailDTO.setAppPartnerDTO(BeanUtil.toBean(partner, AppPartnerDTO.class));
        partnerEmailDTO.setEmailTypeEnum(EmailTypeEnum.PARTNER_RESET_PW);
        partnerEmailDTO.setEmailVariable(emailVariable);

        SendEmailDTO<PartnerEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.PARTNER_USER.getType());
        sendEmailDTO.setEmailDTO(partnerEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 发送Partner修改邮箱的邮件，发送到新邮箱
     *
     * @param newEmail    新邮箱
     * @param partner     Partner
     * @param appUserInfo 用户详情
     */
    public Map<String, String> partnerChangeEmailSendNew(String newEmail, AppUserPartnerEntity partner, AppUserInfoEntity appUserInfo) {
        Map<String, String> newEmailVariableMap = PartnerChangeEmailTempFactory
                .createTemplate(EmailTypeEnum.PARTNER_CHANGE_EMAIL_TO_NEW_EMAIL, partner, newEmail, appUserInfo.getNickname(), rsaProperties);

        AppPartnerDTO appPartnerDTO = BeanUtil.toBean(partner, AppPartnerDTO.class);
        appPartnerDTO.setId(partner.getId());
        appPartnerDTO.setEmail(newEmail);
        appPartnerDTO.setTimeZone(appUserInfo.getTimeZone());

        PartnerEmailDTO partnerEmailDTO = new PartnerEmailDTO();
        partnerEmailDTO.setAppPartnerDTO(appPartnerDTO);
        partnerEmailDTO.setEmailTypeEnum(EmailTypeEnum.PARTNER_CHANGE_EMAIL_TO_NEW_EMAIL);
        partnerEmailDTO.setEmailVariable(newEmailVariableMap);

        SendEmailDTO<PartnerEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.PARTNER_USER.getType());
        sendEmailDTO.setEmailDTO(partnerEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return newEmailVariableMap;
    }

    /**
     * 发送Partner修改邮箱的邮件，发送到旧邮箱
     *
     * @param oldEmail    旧邮箱
     * @param partner     Partner
     * @param appUserInfo 用户详情
     */
    public Map<String, String> partnerChangeEmailSendOld(String oldEmail, AppUserPartnerEntity partner, AppUserInfoEntity appUserInfo) {
        Map<String, String> oldEmailVariableMap = PartnerChangeEmailTempFactory
                .createTemplate(EmailTypeEnum.PARTNER_CHANGE_EMAIL_TO_OLD_EMAIL, partner, oldEmail, appUserInfo.getNickname(), rsaProperties);

        AppPartnerDTO appPartnerDTO = BeanUtil.toBean(partner, AppPartnerDTO.class);
        appPartnerDTO.setId(partner.getId());
        appPartnerDTO.setEmail(oldEmail);
        appPartnerDTO.setTimeZone(appUserInfo.getTimeZone());

        PartnerEmailDTO partnerEmailDTO = new PartnerEmailDTO();
        partnerEmailDTO.setAppPartnerDTO(appPartnerDTO);
        partnerEmailDTO.setEmailTypeEnum(EmailTypeEnum.PARTNER_CHANGE_EMAIL_TO_OLD_EMAIL);
        partnerEmailDTO.setEmailVariable(oldEmailVariableMap);

        SendEmailDTO<PartnerEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.PARTNER_USER.getType());
        sendEmailDTO.setEmailDTO(partnerEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return oldEmailVariableMap;
    }

    /**
     * 设备登录时发送校验码
     *
     * @param appUser     用户
     * @param appUserInfo 用户详情
     */
    public String sendDeviceVerifyCode(AppUserEntity appUser, AppUserInfoEntity appUserInfo) {
        String verifyCode;
        String cacheKey = RedisCacheKeyConst.VERIFY_CODE_REPEAT + appUser.getId();
        String verifyCodeCache = redisComponent.get(cacheKey);
        if (StringUtils.isNotBlank(verifyCodeCache)) {
            verifyCode = verifyCodeCache;
        } else {
            verifyCode = StringUtil.randomNumber(6);
            redisComponent.setEx(cacheKey, verifyCode, sysDictProperties.getVerifyCodeRepeat(), TimeUnit.MINUTES);
        }

        Map<String, String> emailVariableMap = new HashMap<>();
        emailVariableMap.put("userNickName", appUserInfo.getNickname());
        emailVariableMap.put("verifyCode", verifyCode);

        AppUserEmailDTO appUserEmailDTO = new AppUserEmailDTO();
        appUserEmailDTO.setAppUserDTO(BeanUtil.toBean(appUser, AppUserDTO.class));
        appUserEmailDTO.setEmailTypeEnum(EmailTypeEnum.VERIFY_LOGIN_DEVICE);
        appUserEmailDTO.setEmailVariable(emailVariableMap);

        SendEmailDTO<AppUserEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.APP_USER.getType());
        sendEmailDTO.setEmailDTO(appUserEmailDTO);
        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());

        log.info("send user:{} verify code:{}", appUser.getId(), verifyCode);
        return verifyCode;
    }
}

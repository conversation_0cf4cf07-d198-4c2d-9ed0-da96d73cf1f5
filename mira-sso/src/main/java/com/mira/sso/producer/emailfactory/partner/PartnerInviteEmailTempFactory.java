package com.mira.sso.producer.emailfactory.partner;

import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.RsaUtil;
import com.mira.sso.dal.entity.AppUserInfoEntity;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.producer.emailfactory.EmailTemplateFactory;
import com.mira.web.properties.RsaProperties;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 邀请Partner邮件模版工厂
 *
 * <AUTHOR>
 */
public class PartnerInviteEmailTempFactory implements EmailTemplateFactory<AppUserPartnerEntity> {
    private final RsaProperties rsaProperties;
    private final AppUserInfoEntity appUserInfo;

    public PartnerInviteEmailTempFactory(RsaProperties rsaProperties, AppUserInfoEntity appUserInfo) {
        this.rsaProperties = rsaProperties;
        this.appUserInfo = appUserInfo;
    }

    @Override
    public Map<String, String> createTemplate(AppUserPartnerEntity partnerEntity) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("partnerId", partnerEntity.getId());
        encryptMap.put("partnerRegisterTimestamp", System.currentTimeMillis());
        encryptMap.put("templateCode", EmailTypeEnum.USER_INVITE_PARTNER.getCode());
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        Map<String, String> variableMap = new HashMap<>();
        variableMap.put("userHash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("firstName", partnerEntity.getFirstName());
        variableMap.put("userNickName", appUserInfo.getNickname());
        variableMap.put("email", partnerEntity.getEmail());
        return variableMap;
    }

    public static Map<String, String> createTemplate(AppUserPartnerEntity partnerEntity, AppUserInfoEntity appUserInfo, RsaProperties rsaProperties) {
        return new PartnerInviteEmailTempFactory(rsaProperties, appUserInfo).createTemplate(partnerEntity);
    }
}

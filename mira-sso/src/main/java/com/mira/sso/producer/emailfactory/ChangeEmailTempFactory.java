package com.mira.sso.producer.emailfactory;

import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.RsaUtil;
import com.mira.web.properties.RsaProperties;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 修改邮箱邮件模版工厂
 *
 * <AUTHOR>
 */
public class ChangeEmailTempFactory implements EmailTemplateFactory<EmailTypeEnum> {
    private final RsaProperties rsaProperties;
    private final String email;
    private final String nickName;

    public ChangeEmailTempFactory(RsaProperties rsaProperties, String email, String nickName) {
        this.rsaProperties = rsaProperties;
        this.email = email;
        this.nickName = nickName;
    }

    @Override
    public Map<String, String> createTemplate(EmailTypeEnum emailTypeEnum) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("userId", ContextHolder.<BaseLoginInfo>getLoginInfo().getId());
        encryptMap.put("changeEmailTimestamp", System.currentTimeMillis());
        encryptMap.put("templateCode", emailTypeEnum.getCode());
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        Map<String, String> variableMap = new HashMap<>();
        variableMap.put("userHash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("firstName", nickName);
        variableMap.put("newEmail", email);
        return variableMap;
    }

    public static Map<String, String> createTemplate(EmailTypeEnum emailTypeEnum, String email, String nickName, RsaProperties rsaProperties) {
        return new ChangeEmailTempFactory(rsaProperties, email, nickName).createTemplate(emailTypeEnum);
    }
}

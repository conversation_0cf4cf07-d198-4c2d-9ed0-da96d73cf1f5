package com.mira.sso.producer.emailfactory;

import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.sso.service.dto.UserRegisterDTO;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.RsaUtil;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.web.properties.RsaProperties;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 注册邮件模版工厂
 *
 * <AUTHOR>
 */
public class RegisterEmailTempFactory implements EmailTemplateFactory<AppUserEntity> {
    private final RsaProperties rsaProperties;
    private final UserRegisterDTO userRegisterDTO;

    public RegisterEmailTempFactory(RsaProperties rsaProperties, UserRegisterDTO userRegisterDTO) {
        this.rsaProperties = rsaProperties;
        this.userRegisterDTO = userRegisterDTO;
    }

    @Override
    public Map<String, String> createTemplate(AppUserEntity appUser) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("userId", appUser.getId());
        encryptMap.put("registerTimestamp", System.currentTimeMillis());
        encryptMap.put("templateCode", EmailTypeEnum.USER_REGISTER.getCode());
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        Map<String, String> variableMap = new HashMap<>();
        variableMap.put("userHash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("firstName", userRegisterDTO.getFirstName());
        variableMap.put("email", userRegisterDTO.getEmail());
        return variableMap;
    }

    public static Map<String, String> createTemplate(AppUserEntity appUser,
                                                     UserRegisterDTO userRegisterDTO,
                                                     RsaProperties rsaProperties) {
        return new RegisterEmailTempFactory(rsaProperties, userRegisterDTO).createTemplate(appUser);
    }
}

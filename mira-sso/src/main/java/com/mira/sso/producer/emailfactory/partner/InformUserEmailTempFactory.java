package com.mira.sso.producer.emailfactory.partner;

import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.RsaUtil;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.sso.producer.emailfactory.EmailTemplateFactory;
import com.mira.web.properties.RsaProperties;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 告知User邮件模版工厂
 *
 * <AUTHOR>
 */
public class InformUserEmailTempFactory implements EmailTemplateFactory<AppUserEntity> {
    private final RsaProperties rsaProperties;
    private final String nickName;

    public InformUserEmailTempFactory(RsaProperties rsaProperties, String nickName) {
        this.rsaProperties = rsaProperties;
        this.nickName = nickName;
    }

    @Override
    public Map<String, String> createTemplate(AppUserEntity appUser) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("userId", appUser.getId());
        encryptMap.put("templateCode", EmailTypeEnum.USER_INFORM_PARTNER_EXIST_USER.getCode());
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        Map<String, String> variableMap = new HashMap<>();
        variableMap.put("userHash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("userNickName", nickName);
        variableMap.put("email", appUser.getEmail());
        return variableMap;
    }

    public static Map<String, String> createTemplate(AppUserEntity appUser, String nickName, RsaProperties rsaProperties) {
        return new InformUserEmailTempFactory(rsaProperties, nickName).createTemplate(appUser);
    }
}

package com.mira.sso.producer.emailfactory.partner;

import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.RsaUtil;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.producer.emailfactory.EmailTemplateFactory;
import com.mira.web.properties.RsaProperties;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 告知Partner邮件模版工厂
 *
 * <AUTHOR>
 */
public class InformPartnerEmailTempFactory implements EmailTemplateFactory<AppUserPartnerEntity> {
    private final RsaProperties rsaProperties;
    private final String nickName;

    public InformPartnerEmailTempFactory(RsaProperties rsaProperties, String nickName) {
        this.rsaProperties = rsaProperties;
        this.nickName = nickName;
    }

    @Override
    public Map<String, String> createTemplate(AppUserPartnerEntity partnerEntity) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("userId", partnerEntity.getId());
        encryptMap.put("templateCode", EmailTypeEnum.USER_INFORM_PARTNER_EXIST_PARTNER.getCode());
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        Map<String, String> variableMap = new HashMap<>();
        variableMap.put("userHash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("userNickName", nickName);
        variableMap.put("email", partnerEntity.getEmail());
        return variableMap;
    }

    public static Map<String, String> createTemplate(AppUserPartnerEntity partnerEntity, String nickName, RsaProperties rsaProperties) {
        return new InformPartnerEmailTempFactory(rsaProperties, nickName).createTemplate(partnerEntity);
    }
}

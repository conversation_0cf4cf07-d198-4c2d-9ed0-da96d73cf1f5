package com.mira.sso.producer.emailfactory;

import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.RsaUtil;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.sso.dal.entity.AppUserInfoEntity;
import com.mira.web.properties.RsaProperties;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 忘记密码邮件模版工厂
 *
 * <AUTHOR>
 */
public class ResetPasswordEmailTempFactory implements EmailTemplateFactory<AppUserEntity> {
    private final RsaProperties rsaProperties;
    private final AppUserInfoEntity appUserInfo;

    public ResetPasswordEmailTempFactory(RsaProperties rsaProperties, AppUserInfoEntity appUserInfo) {
        this.rsaProperties = rsaProperties;
        this.appUserInfo = appUserInfo;
    }

    @Override
    public Map<String, String> createTemplate(AppUserEntity appUser) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("userId", appUser.getId());
        encryptMap.put("resetPasswordTimestamp", System.currentTimeMillis());
        encryptMap.put("templateCode", EmailTypeEnum.USER_RESET_PW.getCode());
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        Map<String, String> variableMap = new HashMap<>();
        variableMap.put("userHash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("firstName", appUserInfo.getFirstName());
        variableMap.put("email", appUser.getEmail());
        return variableMap;
    }

    public static Map<String, String> createTemplate(AppUserEntity appUser, AppUserInfoEntity appUserInfo, RsaProperties rsaProperties) {
        return new ResetPasswordEmailTempFactory(rsaProperties, appUserInfo).createTemplate(appUser);
    }
}

package com.mira.sso.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * cache expire properties
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "cache.expire")
public class CacheExpireProperties {
    /**
     * 注册信息
     */
    private long register;

    /**
     * 修改邮箱
     */
    private long changeEmail;

    /**
     * 重置密码
     */
    private long resetPassword;

    /**
     * 登录锁定
     */
    private long loginLock;

    /**
     * 登录失败
     */
    private long loginError;

    /**
     * Partner邀请
     */
    private long partnerInvite;

    /**
     * 登录设备校验码
     */
    private long deviceVerifyCode;

    /**
     * firebase push token
     */
    private long pushToken;
}

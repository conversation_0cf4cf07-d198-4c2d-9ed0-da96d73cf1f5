package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.UserProductTrialEntity;
import com.mira.sso.dal.mapper.UserProductTrialMapper;
import org.springframework.stereotype.Repository;

/**
 * user_product_trial DAO
 *
 * <AUTHOR>
 */
@Repository
public class UserProductTrialDAO extends ServiceImpl<UserProductTrialMapper, UserProductTrialEntity> {
    public UserProductTrialEntity getByEmail(String email) {
        return getOne(Wrappers.<UserProductTrialEntity>lambdaQuery()
                .eq(UserProductTrialEntity::getEmail, email)
                .last("limit 1"));
    }

    public UserProductTrialEntity getByUserId(Long userId) {
        return getOne(Wrappers.<UserProductTrialEntity>lambdaQuery()
                .eq(UserProductTrialEntity::getUserId, userId)
                .last("limit 1"));
    }

    public void removeByUserId(Long userId) {
        remove(Wrappers.<UserProductTrialEntity>lambdaUpdate()
                .eq(UserProductTrialEntity::getUserId, userId));
    }
}

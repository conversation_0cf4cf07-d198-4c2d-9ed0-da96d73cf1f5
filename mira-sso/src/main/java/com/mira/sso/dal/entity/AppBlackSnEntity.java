package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 仪器黑名单
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_black_sn")
public class AppBlackSnEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * sn
     */
    private String sn;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * email
     */
    private String email;

    /**
     * 是否激活：0未激活（默认）；1激活
     */
    private Integer enable;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否退回：1退回，2不退回，默认null
     */
    private Integer needReturn;
}

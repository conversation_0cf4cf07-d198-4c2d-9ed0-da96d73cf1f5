package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户关键操作记录
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_operate_log")
public class AppOperateLogEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * email
     */
    private String email;

    /**
     * 操作行为
     */
    private Integer operate;

    /**
     * 用户当前ip地址
     */
    private String currentIp;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建时间
     */
    private String createTimeStr;

    /**
     * 操作记录
     */
    private String sysNote;
}

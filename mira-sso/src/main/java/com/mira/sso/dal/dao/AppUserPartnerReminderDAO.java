package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.AppUserPartnerReminderEntity;
import com.mira.sso.dal.mapper.AppUserPartnerReminderMapper;
import org.springframework.stereotype.Repository;

/**
 * app_user_partner_reminder DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserPartnerReminderDAO extends ServiceImpl<AppUserPartnerReminderMapper, AppUserPartnerReminderEntity> {
    public AppUserPartnerReminderEntity getByPartnerId(Long partnerId) {
        return getOne(Wrappers.<AppUserPartnerReminderEntity>lambdaQuery()
                .eq(AppUserPartnerReminderEntity::getPartnerId, partnerId));
    }
}

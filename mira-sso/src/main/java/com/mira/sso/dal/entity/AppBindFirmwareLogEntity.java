package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 固件版本绑定日志
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_bind_firmware_log")
public class AppBindFirmwareLogEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 设备ID
     */
    private String bindDevice;

    /**
     * 设备SN
     */
    private String sn;

    /**
     * 绑定的固件版本
     */
    private String bindVersion;

    /**
     * 旧的固件版本
     */
    private String oldVersion;
}

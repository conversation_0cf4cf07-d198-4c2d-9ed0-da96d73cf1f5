package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户仪器绑定表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_bind")
public class AppUserBindEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 绑定设备
     */
    private String bindDevice;

    /**
     * 绑定时间
     */
    private String bindTime;

    /**
     * 设备版本
     */
    private String bindVersion;

    /**
     * sn
     */
    private String sn;
}

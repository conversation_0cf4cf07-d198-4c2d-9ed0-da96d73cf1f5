package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.UserDeviceEntity;
import com.mira.sso.dal.mapper.UserDeviceMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserDeviceDAO extends ServiceImpl<UserDeviceMapper, UserDeviceEntity> {
    public UserDeviceEntity getByUserIdAndChannel(Long userId, Integer channel) {
        return getOne(Wrappers.<UserDeviceEntity>lambdaQuery()
                .eq(UserDeviceEntity::getUserId, userId)
                .eq(UserDeviceEntity::getChannel, channel));
    }

    public List<UserDeviceEntity> listByUserIdAndChannel(Long userId, Integer channel) {
        return list(Wrappers.<UserDeviceEntity>lambdaQuery()
                .eq(UserDeviceEntity::getUserId, userId)
                .eq(UserDeviceEntity::getChannel, channel)
                .orderByDesc(UserDeviceEntity::getCreateTime));
    }
}

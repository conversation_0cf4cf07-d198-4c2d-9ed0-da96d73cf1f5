package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.UserReminderComplaintEntity;
import com.mira.sso.dal.mapper.UserReminderComplaintMapper;
import org.springframework.stereotype.Repository;

/**
 * user_reminder_complaint DAO
 *
 * <AUTHOR>
 */
@Repository
public class UserReminderComplaintDAO extends ServiceImpl<UserReminderComplaintMapper, UserReminderComplaintEntity> {
    public UserReminderComplaintEntity getByUserId(Long userId) {
        return getOne(Wrappers.<UserReminderComplaintEntity>lambdaQuery()
                              .eq(UserReminderComplaintEntity::getUserId, userId));
    }

    public long getCountByUserId(Long userId) {
        return count(Wrappers.<UserReminderComplaintEntity>lambdaQuery()
                             .eq(UserReminderComplaintEntity::getUserId, userId));
    }

    /**
     * 校验用户是否在testing schedule exchange 列表中
     *
     * @param userId
     * @return true or false
     */
    public Boolean inReminderComplaint(Long userId) {
        // 用户是否在 user_reminder_compaint 表中
        Long count =
                baseMapper.selectCount(Wrappers.<UserReminderComplaintEntity>lambdaQuery().eq(UserReminderComplaintEntity::getUserId, userId));
        Boolean inReminderComplaint = Boolean.FALSE;
        if (count > 0) {
            inReminderComplaint = Boolean.TRUE;
        }
        return inReminderComplaint;
    }

    /**
     * 将用户添加到testing schedule exchange 列表中
     *
     * @param userId
     */
    public void addTestingScheduleExchange(Long userId) {
        UserReminderComplaintEntity userReminderComplaintEntity =
                baseMapper.selectOne(Wrappers.<UserReminderComplaintEntity>lambdaQuery().eq(UserReminderComplaintEntity::getUserId,
                        userId));
        if (userReminderComplaintEntity == null) {
            userReminderComplaintEntity = new UserReminderComplaintEntity();
            userReminderComplaintEntity.setUserId(userId);
            baseMapper.insert(userReminderComplaintEntity);
        }
    }
}

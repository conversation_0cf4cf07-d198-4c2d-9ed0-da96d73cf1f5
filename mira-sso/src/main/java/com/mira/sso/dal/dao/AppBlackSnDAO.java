package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.AppBlackSnEntity;
import com.mira.sso.dal.mapper.AppBlackSnMapper;
import org.springframework.stereotype.Repository;

/**
 * app_black_sn DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppBlackSnDAO extends ServiceImpl<AppBlackSnMapper, AppBlackSnEntity> {
    public AppBlackSnEntity getEnableOne(String sn) {
        return getOne(Wrappers.<AppBlackSnEntity>lambdaQuery()
                              .eq(AppBlackSnEntity::getSn, sn)
                              .eq(AppBlackSnEntity::getEnable, 1).last("limit 1"));
    }
}

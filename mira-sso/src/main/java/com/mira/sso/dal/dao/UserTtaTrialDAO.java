package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.UserTtaTrialEntity;
import com.mira.sso.dal.mapper.UserTtaTrialMapper;
import org.springframework.stereotype.Repository;

/**
 * user_tta_trial DAO
 *
 * <AUTHOR>
 */
@Repository
public class UserTtaTrialDAO extends ServiceImpl<UserTtaTrialMapper, UserTtaTrialEntity> {
    public UserTtaTrialEntity getByEmail(String email) {
        return getOne(Wrappers.<UserTtaTrialEntity>lambdaQuery()
                .eq(UserTtaTrialEntity::getEmail, email));
    }
}

package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * tta临床用户表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("user_tta_trial")
public class UserTtaTrialEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 类型:0临床；1bata
     */
    private Integer type;
}

package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户通知提醒（投诉用户专用）
 *
 * <AUTHOR>
 */
@Data
@TableName("user_reminder_complaint")
public class UserReminderComplaintEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 状态 0 正常状态 1 删除状态
     */
    @TableLogic
    private Integer deleted;
}

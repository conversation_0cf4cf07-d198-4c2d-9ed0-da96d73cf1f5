package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.AppUserBindLogEntity;
import com.mira.sso.dal.mapper.AppUserBindLogMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_user_bind_log DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserBindLogDAO extends ServiceImpl<AppUserBindLogMapper, AppUserBindLogEntity> {
    public AppUserBindLogEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppUserBindLogEntity>lambdaQuery()
                .eq(AppUserBindLogEntity::getUserId, userId));
    }

    public AppUserBindLogEntity getFirstBindTimeByUserId(Long userId) {
        return lambdaQuery().select(AppUserBindLogEntity::getBindTime)
                .eq(AppUserBindLogEntity::getUserId, userId)
                .orderByAsc(AppUserBindLogEntity::getId)
                .last("limit 1")
                .one();
    }

    public List<AppUserBindLogEntity> list10BindLog(Long userId) {
        return list(Wrappers.<AppUserBindLogEntity>lambdaQuery()
                .eq(AppUserBindLogEntity::getUserId, userId)
                .orderByDesc(AppUserBindLogEntity::getBindTime)
                .last("limit 10"));
    }

    public long countByUserId(Long userId) {
        return count(Wrappers.<AppUserBindLogEntity>lambdaQuery()
                .eq(AppUserBindLogEntity::getUserId, userId));
    }

    public long countBySnAndType(String sn, Integer type) {
        return count(Wrappers.<AppUserBindLogEntity>lambdaQuery()
                .eq(AppUserBindLogEntity::getSn, sn)
                .eq(AppUserBindLogEntity::getType, type));
    }
}

package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.sso.dal.mapper.AppUserMapper;
import org.springframework.stereotype.Repository;

/**
 * app_user DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserDAO extends ServiceImpl<AppUserMapper, AppUserEntity> {
    public AppUserEntity getByEmail(String email) {
        return getOne(Wrappers.<AppUserEntity>lambdaQuery()
                              .eq(AppUserEntity::getEmail, email));
    }
}

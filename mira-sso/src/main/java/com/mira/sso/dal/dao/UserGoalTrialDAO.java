package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.UserGoalTrialEntity;
import com.mira.sso.dal.mapper.UserGoalTrialMapper;
import org.springframework.stereotype.Repository;

/**
 * user_goal_trial DAO
 *
 * <AUTHOR>
 */
@Repository
public class UserGoalTrialDAO extends ServiceImpl<UserGoalTrialMapper, UserGoalTrialEntity> {
    public UserGoalTrialEntity getByEmail(String email) {
        return getOne(Wrappers.<UserGoalTrialEntity>lambdaQuery()
                .eq(UserGoalTrialEntity::getEmail, email)
                .last("limit 1"));
    }

    public UserGoalTrialEntity getByUserId(Long userId) {
        return getOne(Wrappers.<UserGoalTrialEntity>lambdaQuery()
                .eq(UserGoalTrialEntity::getUserId, userId)
                .last("limit 1"));
    }

    public void removeByUserId(Long userId) {
        remove(Wrappers.<UserGoalTrialEntity>lambdaUpdate()
                .eq(UserGoalTrialEntity::getUserId, userId));
    }
}

package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.ReportGoodUsersEntity;
import com.mira.sso.dal.mapper.ReportGoodUsersMapper;
import org.springframework.stereotype.Repository;

/**
 * report_good_users DAO
 *
 * <AUTHOR>
 */
@Repository
public class ReportGoodUsersDAO extends ServiceImpl<ReportGoodUsersMapper, ReportGoodUsersEntity> {
    public ReportGoodUsersEntity getByUserId(Long userId) {
        return getOne(Wrappers.<ReportGoodUsersEntity>lambdaQuery()
                .eq(ReportGoodUsersEntity::getUserId, userId));
    }

    public boolean isAmazonUser(Long userId) {
        long amazonUserCount = count(Wrappers.<ReportGoodUsersEntity>lambdaQuery()
                .eq(ReportGoodUsersEntity::getUserId, userId)
                .eq(ReportGoodUsersEntity::getIsAmazon, 1));
        return amazonUserCount > 0;
    }
}

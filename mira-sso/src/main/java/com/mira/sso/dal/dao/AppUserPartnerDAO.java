package com.mira.sso.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.dal.mapper.AppUserPartnerMapper;
import org.springframework.stereotype.Repository;

/**
 * app_user_partner DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserPartnerDAO extends ServiceImpl<AppUserPartnerMapper, AppUserPartnerEntity> {
    public AppUserPartnerEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppUserPartnerEntity>lambdaQuery()
                .eq(AppUserPartnerEntity::getUserId, userId));
    }

    public AppUserPartnerEntity getByEmail(String email) {
        return getOne(Wrappers.<AppUserPartnerEntity>lambdaQuery()
                .eq(AppUserPartnerEntity::getEmail, email));
    }
}

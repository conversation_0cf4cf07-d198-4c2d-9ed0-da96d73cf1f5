package com.mira.sso.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.api.user.dto.user.TrialUserDTO;
import com.mira.sso.dal.entity.UserProductTrialEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * user_product_trial
 *
 * <AUTHOR>
 */
@Mapper
public interface UserProductTrialMapper extends BaseMapper<UserProductTrialEntity> {
    /**
     * 获取所有临床的用户
     *
     * @return list
     */
    List<TrialUserDTO> listTrialUsers();
}

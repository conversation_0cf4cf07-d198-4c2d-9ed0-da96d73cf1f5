package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("user_device")
public class UserDeviceEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 创建时间时间戳
     */
    private Long createTime;

    /**
     * 创建时间字符串
     */
    private String createTimeStr;

    /**
     * 时区
     */
    private String timeZone;
}

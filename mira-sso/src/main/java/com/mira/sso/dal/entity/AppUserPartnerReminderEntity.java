package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * partner partner通知提醒开关
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_partner_reminder")
public class AppUserPartnerReminderEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * partner ID
     */
    private Long partnerId;

    /**
     * 通知标示:0 no remind;1remind
     */
    private Integer remindFlag;

    /**
     * 通知时间
     */
    private Long remindTime;

    /**
     * 通知时间
     */
    private String remindTimeStr;

    /**
     * 推送Token
     */
    private String pushToken;

    /**
     * 平台: iOS:3;android:4
     */
    private Integer platform;
}

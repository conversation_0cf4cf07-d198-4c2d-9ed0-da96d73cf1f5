package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户登录失败历史表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_login_failure_history")
public class LoginFailureHistoryEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * ip
     */
    private String ip;

    /**
     * iOS/android
     */
    private String os;

    /**
     * 手机系统版本
     */
    private String version;

    /**
     * 手机机型
     */
    private String device;

    /**
     * app版本
     */
    private String appVersion;

    /**
     * 标记
     */
    @Deprecated
    private Integer flag;

    /**
     * 渠道
     */
    private Integer channel;
}

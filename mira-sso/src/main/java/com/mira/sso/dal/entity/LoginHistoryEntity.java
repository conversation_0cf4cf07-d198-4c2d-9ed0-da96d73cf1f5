package com.mira.sso.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 仪器黑名单
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_login_history")
public class LoginHistoryEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * ip
     */
    private String ip;

    /**
     * iOS/android
     */
    private String os;

    /**
     * 手机系统版本
     */
    private String version;

    /**
     * 手机机型
     */
    private String device;

    /**
     * app版本
     */
    private String appVersion;

    /**
     * 渠道
     */
    private Integer channel;
}

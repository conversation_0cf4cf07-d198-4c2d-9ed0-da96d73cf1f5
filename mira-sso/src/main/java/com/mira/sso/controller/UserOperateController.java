package com.mira.sso.controller;

import com.mira.core.annotation.Anonymous;
import com.mira.core.annotation.IgnoreLog;
import com.mira.core.consts.RequestSendFlag;
import com.mira.sso.controller.vo.UserForgetPasswordVO;
import com.mira.sso.service.IUserOperateService;
import com.mira.sso.service.dto.ChangeEmailDTO;
import com.mira.sso.service.dto.ResetPasswordDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 用户操作控制器
 * <p>app/v4前缀的url是兼容老版本
 *
 * <AUTHOR>
 */
@Api(tags = "04.用户操作")
@RestController
public class UserOperateController {
    @Resource
    private IUserOperateService userOperateService;

    @Anonymous
    @ApiOperation("忘记密码")
    @PostMapping("/sso/user/mail/reset-pw")
    public UserForgetPasswordVO resetPassword(@RequestParam String email) {
        return userOperateService.resetPassword(email, RequestSendFlag.FIRST_SEND);
    }

    @Anonymous
    @ApiOperation("忘记密码再次发送")
    @PostMapping("/sso/user/mail/reset-pw/resend")
    public UserForgetPasswordVO resetPasswordResend(@RequestParam String email) {
        return userOperateService.resetPassword(email, RequestSendFlag.RESEND);
    }

    @Anonymous
    @ApiOperation("确认忘记密码")
    @PostMapping("/sso/user/mail/reset-pw/confirm")
    public String resetPasswordConfim(@Valid @RequestBody ResetPasswordDTO resetPasswordDTO) {
        return userOperateService.resetPasswordConfirm(resetPasswordDTO);
    }

    @Anonymous
    @IgnoreLog
    @ApiOperation("获取忘记密码的状态")
    @PostMapping("/sso/user/mail/reset-pw-status")
    public String resetPasswordStatus(@RequestParam String email) {
        return userOperateService.resetPasswordStatus(email);
    }

    @ApiOperation("修改用户的邮箱")
    @PostMapping("/sso/user/mail/change-email")
    public String changeEmail(@Valid @RequestBody ChangeEmailDTO changeEmailDTO) {
        return userOperateService.changeEmail(changeEmailDTO, RequestSendFlag.FIRST_SEND);
    }

    @ApiOperation("修改用户的邮箱再次发送")
    @PostMapping("/sso/user/mail/change-email/resend")
    public String changeEmailResend(@Valid @RequestBody ChangeEmailDTO changeEmailDTO) {
        return userOperateService.changeEmail(changeEmailDTO, RequestSendFlag.RESEND);
    }

    @Anonymous
    @ApiOperation("确认修改用户的邮箱")
    @PostMapping("/sso/user/mail/change/email/confirm")
    public String changeEmailConfirm(@RequestParam String hash) {
        return userOperateService.changeEmailConfirm(hash);
    }

    @IgnoreLog
    @ApiOperation("获取修改用户的邮箱的状态")
    @PostMapping("/sso/user/mail/change-email-status")
    public String changeEmailStatus(@Valid @RequestBody ChangeEmailDTO changeEmailDTO) {
        return userOperateService.changeEmailStatus(changeEmailDTO);
    }

    // ---------------------------------------------

    @Anonymous
    @ApiOperation("忘记密码（兼容）")
    @PostMapping("/app/v4/mail/reset-pw")
    public UserForgetPasswordVO  CompatibleResetPassword(@RequestParam String email) {
        return userOperateService.resetPassword(email, RequestSendFlag.FIRST_SEND);
    }

    @Anonymous
    @ApiOperation("忘记密码再次发送（兼容）")
    @PostMapping("/app/v4/mail/reset-pw/resend")
    public UserForgetPasswordVO  CompatibleResetPasswordResend(@RequestParam String email) {
        return userOperateService.resetPassword(email, RequestSendFlag.RESEND);
    }

    @Anonymous
    @ApiOperation("确认忘记密码（兼容）")
    @PostMapping("/app/v4/mail/reset-pw/confirm")
    public String  CompatibleResetPasswordConfim(@Valid @RequestBody ResetPasswordDTO resetPasswordDTO) {
        return userOperateService.resetPasswordConfirm(resetPasswordDTO);
    }

    @Anonymous
    @IgnoreLog
    @ApiOperation("获取忘记密码的状态（兼容）")
    @PostMapping("/app/v4/mail/reset-pw-status")
    public String  CompatibleResetPasswordStatus(@RequestParam String email) {
        return userOperateService.resetPasswordStatus(email);
    }

    @ApiOperation("修改用户的邮箱（兼容）")
    @PostMapping("/app/v4/mail/change-email")
    public String  CompatibleChangeEmail(@Valid @RequestBody ChangeEmailDTO changeEmailDTO) {
        return userOperateService.changeEmail(changeEmailDTO, RequestSendFlag.FIRST_SEND);
    }

    @ApiOperation("修改用户的邮箱再次发送（兼容）")
    @PostMapping("/app/v4/mail/change-email/resend")
    public String  CompatibleChangeEmailResend(@Valid @RequestBody ChangeEmailDTO changeEmailDTO) {
        return userOperateService.changeEmail(changeEmailDTO, RequestSendFlag.RESEND);
    }

    @Anonymous
    @ApiOperation("确认修改用户的邮箱（兼容）")
    @PostMapping("/app/v4/mail/change/email/confirm")
    public String  CompatibleChangeEmailConfirm(@RequestParam String hash) {
        return userOperateService.changeEmailConfirm(hash);
    }

    @IgnoreLog
    @ApiOperation("获取修改用户的邮箱的状态（兼容）")
    @PostMapping("/app/v4/mail/change-email-status")
    public String  CompatibleChangeEmailStatus(@Valid @RequestBody ChangeEmailDTO changeEmailDTO) {
        return userOperateService.changeEmailStatus(changeEmailDTO);
    }
}

package com.mira.sso.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@ApiModel("用户信息")
public class WebsiteUserInfoVO {
    @ApiModelProperty(value = "昵称")
    private String nickname;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "用户目标")
    private Integer goalStatus;

    @ApiModelProperty(value = "默认地址")
    private Map<String, Object> defaultAddress;
}

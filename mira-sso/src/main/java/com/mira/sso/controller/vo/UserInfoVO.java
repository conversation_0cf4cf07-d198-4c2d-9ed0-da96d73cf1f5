package com.mira.sso.controller.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.mira.api.user.enums.UserGoalEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ApiModel("用户信息")
public class UserInfoVO {
    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("英文名")
    private String firstName;

    @ApiModelProperty("英文姓")
    private String lastName;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("出生年份")
    private Integer birthYear;

    @ApiModelProperty("出生月份")
    private Integer birthMonth;

    @ApiModelProperty("出生日期")
    private Integer birthDay;

    @ApiModelProperty("用户目标，参考 UserGoalEnum")
    private Integer goalStatus;

    @ApiModelProperty("用户目标选项")
    private List<Integer> goalStatusOptions = Lists.newArrayList(
            UserGoalEnum.CYCLE_TRACKING.getValue(),
            UserGoalEnum.TTC.getValue());

    @ApiModelProperty("用户 Condition，参考 UserConditionEnum")
    private List<Integer> conditions = new ArrayList<>();

    @ApiModelProperty("节育方式")
    private Integer hormonalBirthControl;

    @ApiModelProperty("通知标示:0 no remind，1remind")
    private Integer remindFlag;

    @ApiModelProperty("通知时间")
    private Long remindTime;

    @ApiModelProperty("平均经期长度")
    private Integer avgLenPeriod;

    @ApiModelProperty("平均周期长度")
    private Integer avgLenCycle;

    @Deprecated
    @ApiModelProperty("是否新用户：0新用户，1老用户")
    private Integer fresh;

    @ApiModelProperty("Onboarding状态，0-未完成，1-到连接仪器，2-到达首页")
    private Integer onboardingStatus;

    @ApiModelProperty("Onboarding Page")
    private String onboardingPage;

    @ApiModelProperty("No Period 标记，0-否，1-是")
    private Integer noPeriod;

    @ApiModelProperty("是否Irregular，0-否，1-是")
    private Integer irregularCycle;

    @ApiModelProperty("临床试验参与者的临床状态")
    private Integer trialStatus;

    @ApiModelProperty("绑定的固件版本")
    private String bindVersion;

    @ApiModelProperty("绑定的设备")
    private String bindDevice;

    @ApiModelProperty("Firebase 推送 Token")
    private String pushToken;

    @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
    private Integer trackingMenopause;

    @ApiModelProperty("最近一次经期")
    private RecentPeriod recentPeriod;

    @Getter
    @Setter
    @ApiModel("最近一次经期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RecentPeriod {
        @ApiModelProperty("周期开始日")
        private Long datePeriodStart;

        @ApiModelProperty("经期结束⽇（经期不包含这天）")
        private Long datePeriodEnd;
    }

    @ApiModelProperty("是否是好用户：0-否，1-是")
    private Integer goodUser;

    @ApiModelProperty("是否 Amazon 用户：0-否，1-是")
    private Integer amazonUser;

    @ApiModelProperty("新旧版本数据同步状态")
    private Integer transferFlag;

    @ApiModelProperty("partner")
    private Partner partner;

    @Getter
    @Setter
    @ApiModel("Partner")
    public static class Partner {
        @ApiModelProperty("partner邮箱")
        private String partnerEmail;

        @ApiModelProperty("partner状态：0-未激活，1-已完成激活但未注册，2-已完成注册")
        private String partnerStatus;
    }

    @ApiModelProperty("是否是仪器黑名单：true是黑名单；false不是（仅针对bindDevice不为空的情况）")
    private Boolean blackSn;

    @ApiModelProperty("clinic")
    private Clinic clinic;

    @ApiModelProperty("clinics")
    private List<Clinic> clinics = new ArrayList<>();

    @Getter
    @Setter
    @ApiModel("Clinic")
    public static class Clinic {
        @ApiModelProperty("租户code")
        private String code;

        @ApiModelProperty("租户名称")
        private String name;

        @ApiModelProperty("租户图标")
        private String icon;

        @ApiModelProperty("病人账号状态:1:邀请中;2:正常激活状态;")
        private Integer status;

        @ApiModelProperty("绑定时间")
        private String bindTimeStr;
    }

    @ApiModelProperty("国家编号")
    private String countryCode;

    @ApiModelProperty("洲编号")
    private String continentCode;

    @ApiModelProperty("用户注册时间")
    private String registerTime;

    @ApiModelProperty("首次绑定仪器时间")
    private String firstBindDeviceTime;

    @ApiModelProperty("首次绑定BBT时间")
    private String firstBindBbtTime;

    @ApiModelProperty("是否有hasMax2有效测试数据")
    private Integer hasMax2;

    @ApiModelProperty("特殊账号类型，1-pfizer")
    private Integer specialAccountType;

    @ApiModelProperty("扩展字段")
    private String extraInfo;
}

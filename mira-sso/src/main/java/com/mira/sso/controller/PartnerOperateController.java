package com.mira.sso.controller;

import com.mira.core.annotation.Anonymous;
import com.mira.core.annotation.IgnoreLog;
import com.mira.core.consts.RequestSendFlag;
import com.mira.sso.controller.vo.UserForgetPasswordVO;
import com.mira.sso.service.IPartnerOperateService;
import com.mira.sso.service.dto.ChangeEmailDTO;
import com.mira.sso.service.dto.PartnerInviteDTO;
import com.mira.sso.service.dto.ResetPasswordDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Partner操作控制器
 *
 * <AUTHOR>
 */
@Api(tags = "31.Partner操作")
@RestController
@RequestMapping("/sso/partner")
public class PartnerOperateController {
    @Resource
    private IPartnerOperateService partnerOperateService;

    @ApiOperation("邀请partner")
    @PostMapping("/mail/invite")
    public String invite(@Valid @RequestBody PartnerInviteDTO partnerInviteDTO) {
        return partnerOperateService.invite(partnerInviteDTO, RequestSendFlag.FIRST_SEND);
    }

    @ApiOperation("邀请partner再次发送")
    @PostMapping("/mail/resend-invite")
    public String inviteResend(@Valid @RequestBody PartnerInviteDTO partnerInviteDTO) {
        return partnerOperateService.invite(partnerInviteDTO, RequestSendFlag.RESEND);
    }

    @ApiOperation("告知邀请partner")
    @PostMapping("/mail/inform")
    public String inviteInform(@Valid @RequestBody PartnerInviteDTO partnerInviteDTO) {
        return partnerOperateService.inviteInform(partnerInviteDTO);
    }

    @Anonymous
    @ApiOperation("确认邀请")
    @PostMapping("/mail/activation")
    public String activation(@RequestParam String hash) {
        return partnerOperateService.activation(hash);
    }

    @Anonymous
    @ApiOperation("忘记密码")
    @PostMapping("/mail/reset-pw")
    public UserForgetPasswordVO resetPassword(@RequestParam String email) {
        return partnerOperateService.resetPassword(email, RequestSendFlag.FIRST_SEND);
    }

    @Anonymous
    @ApiOperation("忘记密码再次发送")
    @PostMapping("/mail/reset-pw/resend")
    public UserForgetPasswordVO resetPasswordResend(@RequestParam String email) {
        return partnerOperateService.resetPassword(email, RequestSendFlag.RESEND);
    }

    @Anonymous
    @ApiOperation("确认忘记密码")
    @PostMapping("/mail/reset-pw/confirm")
    public String resetPasswordConfirm(@Valid @RequestBody ResetPasswordDTO resetPasswordDTO) {
        return partnerOperateService.resetPasswordConfirm(resetPasswordDTO);
    }

    @Anonymous
    @IgnoreLog
    @ApiOperation("获取忘记密码的状态")
    @PostMapping("/mail/reset-pw-status")
    public String resetPasswordStatus(@RequestParam String email) {
        return partnerOperateService.resetPasswordStatus(email);
    }

    @ApiOperation("修改partner的Email")
    @PostMapping("/mail/change-email")
    public String changeEmail(@Valid @RequestBody ChangeEmailDTO changeEmailDTO) {
        return partnerOperateService.changeEmail(changeEmailDTO, RequestSendFlag.FIRST_SEND);
    }

    @ApiOperation("修改partner的Email再次发送")
    @PostMapping("/mail/change-email/resend")
    public String changeEmailResend(@Valid @RequestBody ChangeEmailDTO changeEmailDTO) {
        return partnerOperateService.changeEmail(changeEmailDTO, RequestSendFlag.RESEND);
    }

    @Anonymous
    @ApiOperation("确认修改partner的Email")
    @PostMapping("/mail/change/email/confirm")
    public String changeEmailConfirm(@RequestParam String hash) {
        return partnerOperateService.changeEmailConfirm(hash);
    }
}

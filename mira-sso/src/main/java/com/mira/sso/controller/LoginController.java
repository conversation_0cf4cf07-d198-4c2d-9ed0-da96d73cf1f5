package com.mira.sso.controller;

import com.mira.api.user.consts.OperateLogConst;
import com.mira.api.sso.dto.LoginInfoDTO;
import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.core.annotation.Anonymous;
import com.mira.core.annotation.OperateLog;
import com.mira.sso.controller.vo.UserInfoVO;
import com.mira.sso.service.IUserLoginService;
import com.mira.sso.service.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 登录控制器
 *
 * <AUTHOR>
 */
@Api(tags = "01.用户登录相关")
@RestController
@RequestMapping("/sso/user/sign")
public class LoginController {
    @Resource
    private IUserLoginService userLoginService;

    @Anonymous
    @ApiOperation("邮箱密码登录")
    @PostMapping("/login")
    public String login(@Valid @RequestBody LoginInfoDTO loginInfoDTO) throws Exception {
        return userLoginService.login(loginInfoDTO);
    }

    @Anonymous
    @ApiOperation("验证码登录")
    @PostMapping("/verify-login")
    public String verifyLogin(@Valid @RequestBody VerifyLoginDTO verifyLoginDTO) throws Exception {
        return userLoginService.appCodeLogin(verifyLoginDTO);
    }

    @ApiOperation("获取登录用户详情")
    @GetMapping("/info")
    public UserInfoVO info() {
        return userLoginService.info();
    }

    @Anonymous
    @ApiOperation("重置初始密码")
    @PostMapping("/reset/init-pw")
    public void resetInitPassword(@Valid @RequestBody ResetInitPasswordDTO resetInitPasswordDTO) {
        userLoginService.resetInitPassword(resetInitPasswordDTO);
    }

    @ApiOperation("修改用户的密码")
    @PostMapping("/edit/pw")
    public void editPassword(@Valid @RequestBody EditPasswordDTO editPasswordDTO) {
        userLoginService.editPassword(editPasswordDTO);
    }

    @Anonymous
    @ApiOperation("退出登录")
    @PostMapping("/logout")
    public void logout() {
        userLoginService.logout();
    }

    @OperateLog(operate = OperateLogConst.DELETE_ACCOUNT)
    @ApiOperation("删除用户")
    @PostMapping("/delete")
    public Long delete(@Valid @RequestBody UserDeleteDTO userDeleteDTO) {
        return userLoginService.delete(userDeleteDTO);
    }
}

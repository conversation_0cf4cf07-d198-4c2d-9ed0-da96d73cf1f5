package com.mira.sso.controller;

import com.mira.api.user.consts.OperateLogConst;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.core.annotation.Anonymous;
import com.mira.core.annotation.Idempotent;
import com.mira.core.annotation.OperateLog;
import com.mira.sso.controller.vo.PartnerInfoVO;
import com.mira.sso.service.IPartnerService;
import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.api.sso.dto.LoginInfoDTO;
import com.mira.sso.service.dto.UserRegisterDTO;
import com.mira.sso.service.dto.UserRemindTimeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Partner注册登录控制器
 *
 * <AUTHOR>
 */
@Api(tags = "30.Partner注册登录")
@RestController
@RequestMapping("/sso/partner")
public class PartnerController {
    @Resource
    private IPartnerService partnerService;

    @Anonymous
    @ApiOperation("注册")
    @PostMapping("/sign/register")
    public String register(@Valid @RequestBody UserRegisterDTO userRegisterDTO) throws Exception {
        return partnerService.register(userRegisterDTO);
    }

    @Anonymous
    @ApiOperation("邮箱密码登录")
    @PostMapping("/sign/login")
    public String login(@Valid @RequestBody LoginInfoDTO loginInfoDTO) throws Exception {
        return partnerService.login(loginInfoDTO);
    }

    @Anonymous
    @ApiOperation("退出登录")
    @PostMapping("/sign/logout")
    public void logout() {
        partnerService.logout();
    }

    @ApiOperation("Partner详情")
    @GetMapping("/sign/info")
    public PartnerInfoVO info() {
        return partnerService.info();
    }

    @ApiOperation("修改partner的密码")
    @PostMapping("/info/edit/pw")
    public void editPassword(@Valid @RequestBody EditPasswordDTO editPasswordDTO) {
        partnerService.editPassword(editPasswordDTO);
    }

    @OperateLog(operate = OperateLogConst.USER_REMOVE_PARTNER)
    @ApiOperation("用户移除partner")
    @PostMapping("/info/unbind/partner")
    public void userUnbindPartner() {
        partnerService.userUnbindPartner();
    }

    @OperateLog(operate = OperateLogConst.PARTNER_REMOVE_USER)
    @ApiOperation("partner移除用户")
    @PostMapping("/info/unbind/user")
    public void partnerUnbindUser() {
        partnerService.partnerUnbindUser();
    }

    @ApiOperation("修改partner的remind-time")
    @PostMapping("/info/edit/remind-time")
    public void editRemindTime(@RequestBody UserRemindTimeDTO userRemindTimeDTO) {
        partnerService.editRemindTime(userRemindTimeDTO);
    }

    @Idempotent
    @ApiOperation("修改partner的pushToken")
    @PostMapping("/info/edit/push-token")
    public void editPushToken(@RequestBody PushTokenDTO pushTokenDTO) {
        partnerService.editPushToken(pushTokenDTO);
    }
}

package com.mira.sso.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("Partner信息")
public class PartnerInfoVO {
    @ApiModelProperty("用户ID")
    private Long partnerId;

    @ApiModelProperty("英文名")
    private String firstName;

    @ApiModelProperty("英文姓")
    private String lastName;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("绑定的用户ID")
    private Long userId;

    @ApiModelProperty("绑定的用户邮箱")
    private String userEmail;

    @ApiModelProperty("First name")
    private String userFirstName;

    @ApiModelProperty("Last name")
    private String userLastName;

    @ApiModelProperty("状态，参考PartnerStatusConst")
    private Integer status;

    @ApiModelProperty("当前ip")
    private String currentIp;

    @ApiModelProperty("推送Token")
    private String pushToken;

    @ApiModelProperty("平台，3-iOS；4-android")
    private Integer platform;

    @ApiModelProperty("通知标示，0 no remind；1remind")
    private Integer remindFlag;

    @ApiModelProperty("通知时间")
    private Long remindTime;

    @ApiModelProperty("用户目标，参考UserGoalEnum")
    private Integer goalStatus;

    @ApiModelProperty("tta开关，默认0关闭；1临床打开；2全部打开")
    private Integer ttaSwitch;
}

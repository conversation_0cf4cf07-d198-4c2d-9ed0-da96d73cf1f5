package com.mira.sso.controller;

import com.mira.api.sso.dto.LoginInfoDTO;
import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.core.annotation.Anonymous;
import com.mira.core.consts.RequestSendFlag;
import com.mira.sso.consts.UserChannelConst;
import com.mira.sso.controller.vo.RegisterResultVO;
import com.mira.sso.controller.vo.UserForgetPasswordVO;
import com.mira.sso.controller.vo.WebsiteLoginVO;
import com.mira.sso.controller.vo.WebsiteUserInfoVO;
import com.mira.sso.service.IRegisterService;
import com.mira.sso.service.IUserLoginService;
import com.mira.sso.service.IUserOperateService;
import com.mira.sso.service.IWebsiteService;
import com.mira.sso.service.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 网站用户控制器
 *
 * <AUTHOR>
 */
@Api(tags = "25.网站用户")
@RestController
@RequestMapping("/sso/website")
public class WebsiteController {
    @Resource
    private IRegisterService registerService;
    @Resource
    private IUserLoginService userLoginService;
    @Resource
    private IUserOperateService userOperateService;
    @Resource
    private IWebsiteService websiteService;

    @Anonymous
    @ApiOperation("注册")
    @PostMapping("/register")
    public RegisterResultVO register(@Valid @RequestBody UserRegisterDTO userRegisterDTO) {
        return registerService.register(userRegisterDTO, RequestSendFlag.FIRST_SEND, UserChannelConst.WEBSITE);
    }

    @Anonymous
    @ApiOperation("登录")
    @PostMapping("/login")
    public WebsiteLoginVO login(@Valid @RequestBody LoginInfoDTO loginInfoDTO) throws Exception {
        return userLoginService.websiteLogin(loginInfoDTO);
    }

    @Anonymous
    @ApiOperation("验证码登录")
    @PostMapping("/verify-login")
    public WebsiteLoginVO verifyLogin(@Valid @RequestBody VerifyLoginDTO verifyLoginDTO) throws Exception {
        return userLoginService.websiteCodeLogin(verifyLoginDTO);
    }

    @Anonymous
    @ApiOperation("忘记密码")
    @PostMapping("/reset-pw")
    public UserForgetPasswordVO resetPassword(@RequestParam String email) {
        return userOperateService.resetPassword(email, RequestSendFlag.FIRST_SEND);
    }

    @ApiOperation("退出登录")
    @PostMapping("/logout")
    public void logout() {
        userLoginService.logout();
    }

    @ApiOperation("获取用户信息")
    @GetMapping("/user-info")
    public WebsiteUserInfoVO userInfo() {
        return websiteService.userInfo();
    }

    @ApiOperation("修改用户邮箱")
    @PostMapping("/change-email")
    public void changeEmail(@Valid @RequestBody ChangeEmailDTO changeEmailDTO) {
        userOperateService.changeEmail(changeEmailDTO, RequestSendFlag.FIRST_SEND);
    }

    @ApiOperation("修改用户的密码")
    @PostMapping("/edit-pw")
    public void editPassword(@Valid @RequestBody EditPasswordDTO editPasswordDTO) {
        userLoginService.editPassword(editPasswordDTO);
    }
}

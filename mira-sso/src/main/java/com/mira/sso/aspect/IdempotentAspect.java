package com.mira.sso.aspect;

import cn.hutool.crypto.digest.DigestUtil;
import com.mira.core.annotation.Idempotent;
import com.mira.core.util.IpUtils;
import com.mira.core.util.JsonUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.web.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

/**
 * 幂等性切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class IdempotentAspect {
    @Resource
    private RedisComponent redisComponent;

    @Pointcut("@annotation(com.mira.core.annotation.Idempotent)")
    public void pointcut() {}

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Idempotent idempotent = method.getAnnotation(Idempotent.class);
        String key = String.format("idempotent:%s", generateKey(RequestUtil.getRequest(), method, joinPoint.getArgs()));

        if (redisComponent.setIfAbent(key, "1", idempotent.expire(), TimeUnit.SECONDS)) {
            return joinPoint.proceed();
        }

        log.warn("repeat request, key: {}", key);
        if (StringUtils.isBlank(idempotent.returnValue())) {
            return "success";
        }

        if ("Integer".equals(idempotent.returnValue())) {
            return -1;
        }

        if ("List".equals(idempotent.returnValue())) {
            return new ArrayList<>();
        }

        String classNamePrefix = "com.mira.sso.controller.vo.";
        Class<?> clazz = Class.forName(classNamePrefix.concat(idempotent.returnValue()));
        return clazz.getDeclaredConstructor().newInstance();
    }

    /**
     * 根据请求路径、方法名和请求参数生成md5
     */
    private String generateKey(HttpServletRequest request, Method method, Object[] args) {
        StringBuilder methodString = new StringBuilder(method.toString());
        methodString.append(IpUtils.getIp(request));
        methodString.append(request.getRequestURI());

        for (Object arg : args) {
            methodString.append(JsonUtil.toJson(arg));
        }

        return DigestUtil.md5Hex(methodString.toString());
    }
}

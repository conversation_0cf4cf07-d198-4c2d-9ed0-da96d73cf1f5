package com.mira.sso.aspect;

import com.mira.api.user.consts.OperateLogConst;
import com.mira.core.annotation.OperateLog;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.sso.dal.dao.AppOperateLogDAO;
import com.mira.sso.dal.entity.AppOperateLogEntity;
import com.mira.web.properties.SysDictProperties;
import com.mira.web.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;

/**
 * 记录操作切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class OperateLogAspect {
    @Resource
    private AppOperateLogDAO appOperateLogDAO;
    @Resource
    private SysDictProperties sysDictProperties;

    @Pointcut("@annotation(operateLog)")
    public void pointcut(OperateLog operateLog) {
    }

    @Before(value = "pointcut(operateLog)", argNames = "operateLog")
    public void before(OperateLog operateLog) {
        try {
            String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
            String ip = ContextHolder.get(HeaderConst.IP);
            BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
            String email;

            if (loginInfo == null) {
                String parameters = RequestUtil.getParameters(RequestUtil.getRequest());
                HashMap paramMap = JsonUtil.toObject(parameters, HashMap.class);
                Object obj = paramMap.get("email");
                // 没有email参数，不记录操作日志
                if (ObjectUtils.isEmpty(obj)) {
                    return;
                }
                email = (String) obj;
            } else {
                email = loginInfo.getUsername();
            }

            // save
            saveOperateLog(email, timeZone, ip, operateLog);
        } catch (Exception e) {
            log.error("save operate log error", e);
        }
    }

    private void saveOperateLog(String email, String timeZone, String ip, OperateLog operateLog) {
        CompletableFuture.runAsync(() -> {
            int operate = operateLog.operate();
            long now = System.currentTimeMillis();
            String createTimeStr = ZoneDateUtil.format(timeZone, now, DatePatternConst.DATE_TIME_PATTERN);

            AppOperateLogEntity appOperateLogEntity = new AppOperateLogEntity();
            appOperateLogEntity.setEmail(email);
            appOperateLogEntity.setOperate(operate);
            appOperateLogEntity.setTimeZone(timeZone);
            appOperateLogEntity.setCurrentIp(ip);
            appOperateLogEntity.setCreateTime(now);
            appOperateLogEntity.setCreateTimeStr(createTimeStr);
            if (OperateLogConst.PRIVACY_AGREEMENT_UPDATE == operate) {
                appOperateLogEntity.setSysNote("Terms of Use, Privacy policy:".concat(sysDictProperties.getPrivacyVersion()));
            }

            appOperateLogDAO.save(appOperateLogEntity);
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("save operate log error, user:{}", email, ex);
            return null;
        });
    }
}

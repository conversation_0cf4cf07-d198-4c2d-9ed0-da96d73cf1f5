package com.mira.sso.service;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.thirdparty.provider.IShopifyProvider;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import com.mira.sso.controller.vo.WebsiteUserInfoVO;
import com.mira.sso.service.manager.UserInfoManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 网站用户接口实现
 *
 * <AUTHOR>
 */
@Service
public class WebsiteServiceImpl implements IWebsiteService {
    @Resource
    private UserInfoManager userInfoManager;

    @Resource
    private IShopifyProvider shopifyProvider;

    @Override
    public WebsiteUserInfoVO userInfo() {
        LoginUserInfoDTO loginUserInfoDTO = userInfoManager.loginUserInfo();
        WebsiteUserInfoVO websiteUserInfoVO = new WebsiteUserInfoVO();
        BeanUtil.copyProperties(loginUserInfoDTO, websiteUserInfoVO);

        String currency = ContextHolder.get(HeaderConst.CURRENCY);
        Map<String, Object> defaultAddress = shopifyProvider.defaultAddress(loginUserInfoDTO.getEmail(), currency).getData();
        websiteUserInfoVO.setDefaultAddress(defaultAddress);

        return websiteUserInfoVO;
    }
}

package com.mira.sso.service;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.thirdparty.dto.blog.Ip2CountryDTO;
import com.mira.api.thirdparty.dto.shopify.CustomerRegisterDTO;
import com.mira.api.thirdparty.provider.IBlogProvider;
import com.mira.api.thirdparty.provider.IShopifyProvider;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.consts.UserSourceConst;
import com.mira.api.user.consts.UserStatusConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.RequestSendFlag;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.response.enums.BaseCodeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.PasswordUtil;
import com.mira.core.util.RsaUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.sso.async.KlaviyoProducer;
import com.mira.sso.consts.UserChannelConst;
import com.mira.sso.consts.UserConst;
import com.mira.sso.controller.vo.RegisterResultVO;
import com.mira.sso.dal.dao.AppUserDAO;
import com.mira.sso.dal.dao.AppUserInfoDAO;
import com.mira.sso.dal.dao.AppUserPartnerDAO;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.sso.dal.entity.AppUserInfoEntity;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.exception.SsoException;
import com.mira.sso.producer.EmailProducer;
import com.mira.sso.service.dto.UserRegisterDTO;
import com.mira.sso.service.manager.CacheManager;
import com.mira.web.properties.RsaProperties;
import com.mira.web.util.HashDecodeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 注册相关服务接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RegisterServiceImpl implements IRegisterService {
    private final AppUserDAO appUserDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final AppUserPartnerDAO appUserPartnerDAO;
    private final KlaviyoProducer klaviyoProducer;
    private final EmailProducer emailProducer;
    private final RsaProperties rsaProperties;
    private final CacheManager cacheManager;
    private final IBlogProvider blogProvider;
    private final IShopifyProvider shopifyProvider;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RegisterResultVO register(UserRegisterDTO userRegisterDTO, int flag, int channel) {
        AppUserEntity appUser = appUserDAO.getByEmail(userRegisterDTO.getEmail());

        if (RequestSendFlag.FIRST_SEND == flag) {
            if (Objects.nonNull(appUser)) {
                if (UserStatusConst.NORMAL == appUser.getStatus()) {
                    throw new SsoException(BizCodeEnum.USER_ALREADY_ENABLED);
                }
                // update user
                registerUpdateUser(userRegisterDTO, appUser);
            } else {
                AppUserPartnerEntity partner = appUserPartnerDAO.getByEmail(userRegisterDTO.getEmail());
                if (null != partner) {
                    throw new SsoException(BizCodeEnum.USER_ALREADY_ENABLED.getCode(),
                            "It looks like you're a partner user. Please switch to the partner login page.");
                }
                // insert user
                appUser = registerInsertUser(userRegisterDTO);
                // Klaviyo，User Email，Data format（Text）
                klaviyoProducer.accountCreated(appUser);
            }

        } else if (RequestSendFlag.RESEND == flag) {
            if (Objects.isNull(appUser)) {
                throw new SsoException("A system error occurred. Please register again.");
            }
            if (UserStatusConst.NORMAL == appUser.getStatus()) {
                throw new SsoException(BizCodeEnum.USER_ALREADY_ENABLED);
            }
            // 需要判断密码是否一致来判定是否是同一用户
            if (!appUser.getPassword().equals(PasswordUtil.encryptPassword(userRegisterDTO.getPassword(), appUser.getSalt()))) {
                // 再次发送的密码与注册时候不一致，安全起见，跳到重新注册，并提示用户已存在
                throw new SsoException(BizCodeEnum.CONTACT_WITH_US);
            }
        }

        // get first name
        if (RequestSendFlag.RESEND == flag) {
            AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(appUser.getId());
            userRegisterDTO.setFirstName(appUserInfo.getFirstName());
            userRegisterDTO.setLastName(appUserInfo.getLastName());
        }

        // 发送邮件
        Map<String, String> emailVariable = emailProducer.userRegister(appUser, userRegisterDTO);
        String hash = emailVariable.get("userHash");

        // save cache
        cacheManager.cacheRegister(appUser, userRegisterDTO, hash, flag, channel);

        RegisterResultVO registerResultVO = new RegisterResultVO();
        registerResultVO.setHash(hash);
        registerResultVO.setEmailFlag(true);
        return registerResultVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void registerUpdateUser(UserRegisterDTO userRegisterDTO, AppUserEntity appUser) {
        // 需要判断密码是否一致来判定是否是同一用户
        if (!appUser.getPassword().equals(PasswordUtil.encryptPassword(userRegisterDTO.getPassword(), appUser.getSalt()))) {
            // 再次发送的密码与注册时候不一致，安全起见，跳到重新注册，并提示用户已存在
            throw new SsoException(BizCodeEnum.CONTACT_WITH_US.getCode(),
                    "Oops! Looks like something went wrong.  Contact support to resolve this issue!");
        } else {
            // 密码一致，认为是同一用户
            log.info("user:{} register already but try again.", userRegisterDTO.getEmail());
            UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), appUser);
            appUserDAO.updateById(appUser);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public AppUserEntity registerInsertUser(UserRegisterDTO userRegisterDTO) {
        AppUserEntity appUser = new AppUserEntity();
        appUser.setName(userRegisterDTO.getEmail());
        appUser.setSalt(PasswordUtil.generateSalt(20));
        appUser.setPassword(PasswordUtil.encryptPassword(userRegisterDTO.getPassword(), appUser.getSalt()));
        appUser.setEmail(userRegisterDTO.getEmail());
        appUser.setStatus(UserStatusConst.INACTIVE);
        appUser.setSource(UserSourceConst.FOUR);

        // ip
        try {
            String ip = ContextHolder.get(HeaderConst.IP);
            CommonResult<Ip2CountryDTO> commonResult = blogProvider.getCountryByIp(ip, -1L);
            Ip2CountryDTO countryCodeDTO = commonResult.getData();
            appUser.setCurrentIp(ip);
            appUser.setCountryCode(countryCodeDTO.getCountryCode());
            appUser.setContinentCode(countryCodeDTO.getContinentCode());
        } catch (Exception e) {
            log.error("update ip error, email:{}", appUser.getEmail());
        }

        // time
        UpdateEntityTimeUtil.setBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), appUser);

        // insert app_user
        if (!appUserDAO.save(appUser)) {
            throw new SsoException(BizCodeEnum.CREATE_ACCOUNT_FAIL);
        }

        // insert app_user_info
        String firstName = userRegisterDTO.getFirstName();
        String lastName = userRegisterDTO.getLastName();
        AppUserInfoEntity appUserInfoEntity = new AppUserInfoEntity();
        appUserInfoEntity.setUserId(appUser.getId());
        appUserInfoEntity.setFirstName(firstName);
        appUserInfoEntity.setLastName(lastName);
        String nickName = (StringUtils.isBlank(firstName) ? "" : firstName) + " " + (StringUtils.isBlank(lastName) ? "" : lastName);
        appUserInfoEntity.setNickname(nickName);
        UpdateEntityTimeUtil.setBaseEntityTime(appUser.getTimeZone(), appUserInfoEntity);
        if (!appUserInfoDAO.save(appUserInfoEntity)) {
            throw new SsoException(BizCodeEnum.CREATE_ACCOUNT_FAIL);
        }

        return appUser;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String confirmRegister(String hash) {
        hash = HashDecodeUtil.decodeHash(hash);
        String decode = RsaUtil.decodeRsa(hash, rsaProperties.getPrivateKey());
        Map<String, Object> infoMap = JsonUtil.toObject(decode, HashMap.class);
        Long registerTimestamp = Long.valueOf(infoMap.get("registerTimestamp").toString());
        Long userId = Long.valueOf(infoMap.get("userId").toString());

        // check
        Map<String, Object> cacheRegisterMap = cacheManager.getCacheRegister(userId);
        if (MapUtils.isEmpty(cacheRegisterMap)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED);
        }
        String currentHash = cacheRegisterMap.get("hash").toString();
        if (StringUtils.isNotBlank(currentHash) && !currentHash.equals(hash)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED);
        }

        AppUserEntity appUser = appUserDAO.getById(userId);
        if (Objects.isNull(appUser)) {
            throw new SsoException("The account doesn't exist or has been deleted.");
        }
        appUser.setStatus(UserStatusConst.NORMAL);
        appUser.setPasswordGrade(UserConst.PASSWORD_GRADE_HIGHEST);
        appUserDAO.updateById(appUser);

        int channel = Integer.parseInt(cacheRegisterMap.get("channel").toString());
        if (UserChannelConst.WEBSITE == channel) {
            // 网站注册
            String currency = ContextHolder.get(HeaderConst.CURRENCY);
            Boolean exist = shopifyProvider.checkAccountExist(appUser.getEmail(), currency).getData();
            if (!exist) {
                UserRegisterDTO userRegisterDTO = new UserRegisterDTO();
                userRegisterDTO.setEmail(appUser.getEmail());
                userRegisterDTO.setFirstName(cacheRegisterMap.get("firstName").toString());
                userRegisterDTO.setLastName(cacheRegisterMap.get("lastName").toString());
                userRegisterDTO.setPassword(cacheRegisterMap.get("password").toString());

                CustomerRegisterDTO customerRegisterDTO = BeanUtil.toBean(userRegisterDTO, CustomerRegisterDTO.class);
                customerRegisterDTO.setCurrency(currency);
                shopifyProvider.createAccount(customerRegisterDTO);
            }
        }

        // klaviyo
        klaviyoProducer.accountConfirmed(appUser, registerTimestamp);

        log.info("user:{} confirm register", userId);
        return BaseCodeEnum.OK.getMsg();
    }

    @Override
    public String queryEnableStatus(String email) {
        AppUserEntity appUser = appUserDAO.getByEmail(email);
        if (Objects.isNull(appUser)) {
            throw new SsoException("We couldn't find this account. Let's start fresh and register!");
        }
        if (!cacheManager.cacheExist(appUser.getId(), RedisCacheKeyConst.EMAIL_USER_REGISTER)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED.getCode(),
                    "Looks like time got away from us. Please request a new link.");
        }

        if (UserStatusConst.NORMAL != appUser.getStatus()) {
            throw new SsoException(BizCodeEnum.USER_NOT_ENABLED.getCode(),
                    "Your account isn't ready yet. Please check your email for activation.");
        }

        return "Activate account success.";
    }
}

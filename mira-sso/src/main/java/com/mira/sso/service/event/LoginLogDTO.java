package com.mira.sso.service.event;

import com.mira.api.sso.dto.LoginInfoDTO;
import com.mira.sso.dal.entity.AppUserEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 登录日志 DTO
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class LoginLogDTO {
    /**
     * 登录参数
     */
    private LoginInfoDTO loginInfoDTO;

    /**
     * 用户信息
     */
    private AppUserEntity appUser;

    /**
     * 登录标记，0-失败，1-成功
     */
    private int success;

    /**
     * 渠道
     */
    private int channel;
}

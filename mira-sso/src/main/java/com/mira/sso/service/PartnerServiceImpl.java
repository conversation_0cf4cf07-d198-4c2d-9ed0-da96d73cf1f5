package com.mira.sso.service;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.iam.dto.AuthTokenDTO;
import com.mira.api.iam.provider.IAuthProvider;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.sso.dto.LoginInfoDTO;
import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.util.PasswordUtil;
import com.mira.core.util.StringUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.sso.consts.PartnerStatusConst;
import com.mira.sso.controller.vo.PartnerInfoVO;
import com.mira.sso.dal.dao.AppUserInfoDAO;
import com.mira.sso.dal.dao.AppUserPartnerDAO;
import com.mira.sso.dal.dao.AppUserPartnerReminderDAO;
import com.mira.sso.dal.dao.UserTtaTrialDAO;
import com.mira.sso.dal.entity.AppUserInfoEntity;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.dal.entity.AppUserPartnerReminderEntity;
import com.mira.sso.dal.entity.UserTtaTrialEntity;
import com.mira.sso.exception.SsoException;
import com.mira.sso.service.dto.UserRegisterDTO;
import com.mira.sso.service.dto.UserRemindTimeDTO;
import com.mira.sso.service.manager.CacheManager;
import com.mira.web.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * Partner服务接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PartnerServiceImpl implements IPartnerService {
    private final AppUserPartnerDAO appUserPartnerDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final UserTtaTrialDAO userTtaTrialDAO;
    private final AppUserPartnerReminderDAO appUserPartnerReminderDAO;
    private final CacheManager cacheManager;
    private final IAuthProvider authProvider;

    @Override
    public String register(UserRegisterDTO userRegisterDTO) throws Exception {
        // query app_user_partner
        AppUserPartnerEntity partner = appUserPartnerDAO.getByEmail(userRegisterDTO.getEmail());
        if (partner == null) {
            throw new SsoException("Need to be invited.");
        }
        if (PartnerStatusConst.INACTIVE == partner.getStatus()) {
            throw new SsoException("Activation required.");
        }
        if (PartnerStatusConst.FINISHED_REGISTER == partner.getStatus()) {
            throw new SsoException("Partner's account has been active, please return to login.");
        }
        // update partner
        partner.setStatus(PartnerStatusConst.FINISHED_REGISTER);
        partner.setSalt(PasswordUtil.generateSalt(20));
        partner.setPassword(PasswordUtil.encryptPassword(userRegisterDTO.getPassword(), partner.getSalt()));
        partner.setCurrentIp(ContextHolder.get(HeaderConst.IP));
        UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), partner);
        appUserPartnerDAO.updateById(partner);
        cacheManager.deleteUserDetailCache(partner.getUserId());

        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.PARTNER_USER.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(userRegisterDTO.getEmail(), userRegisterDTO.getPassword());

        log.info("register partner:{}, email:{}", partner.getId(), partner.getEmail());

        return "partner:".concat(partner.getId().toString()).concat(":")
                .concat(partner.getUserId().toString()).concat(":")
                .concat(tokenResult.getData().getAccess_token());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String login(LoginInfoDTO loginInfoDTO) throws Exception {
        // query app_user_partner
        AppUserPartnerEntity partner = appUserPartnerDAO.getByEmail(loginInfoDTO.getEmail());
        checkStatus(partner);

        // check password
        if (!PasswordUtil.match(loginInfoDTO.getPassword(), partner.getPassword(), partner.getSalt())) {
            throw new SsoException("Email address or password don't match.");
        }

        // update ip
        partner.setCurrentIp(ContextHolder.get(HeaderConst.IP));
        appUserPartnerDAO.updateById(partner);

        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.PARTNER_USER.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(loginInfoDTO.getEmail(), loginInfoDTO.getPassword());

        return "partner:".concat(partner.getId().toString()).concat(":")
                .concat(partner.getUserId().toString()).concat(":")
                .concat(tokenResult.getData().getAccess_token());
    }

    private void checkStatus(AppUserPartnerEntity partner) {
        if (partner == null) {
            throw new SsoException("The account doesn't exist.");
        }
        if (PartnerStatusConst.INACTIVE == partner.getStatus()) {
            throw new SsoException("Please check your email to activate your Mira account.");
        }
        if (PartnerStatusConst.ACTIVE_BUT_NOT_REGISTER == partner.getStatus()) {
            throw new SsoException(BizCodeEnum.PARTNER_ACTIVE_NOT_SIGNUP);
        }
    }

    @Override
    public void logout() {
        List<String> tokenHeaderList = UserTypeEnum.PARTNER_USER.getTokenHeaderList();
        for (String tokenHeader : tokenHeaderList) {
            String authorization = RequestUtil.getRequest().getHeader(tokenHeader);
            if (StringUtils.isNotEmpty(authorization)) {
                authProvider.deleteToken(authorization, UserTypeEnum.PARTNER_USER.getType());
            }
        }
    }

    @Override
    public PartnerInfoVO info() {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long partnerId = loginInfo.getPartnerId();
        String email = loginInfo.getUsername();

        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getById(partnerId);
        if (Objects.isNull(partnerEntity)) {
            throw new SsoException("partner does not exist or has been deleted");
        }

        AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(partnerEntity.getUserId());
        if (Objects.isNull(appUserInfoEntity)) {
            throw new SsoException("user associated with partner do not exist or have been deleted");
        }

        AppUserPartnerReminderEntity partnerReminderEntity = appUserPartnerReminderDAO.getByPartnerId(partnerId);
        UserTtaTrialEntity userTtaTrialEntity = userTtaTrialDAO.getByEmail(email);

        PartnerInfoVO partnerInfoVO = new PartnerInfoVO();
        BeanUtil.copyProperties(partnerEntity, partnerInfoVO);
        if (partnerReminderEntity != null) {
            BeanUtil.copyProperties(partnerReminderEntity, partnerInfoVO);
        }
        partnerInfoVO.setPartnerId(partnerId);
        partnerInfoVO.setUserFirstName(appUserInfoEntity.getFirstName());
        partnerInfoVO.setUserLastName(appUserInfoEntity.getLastName());
        partnerInfoVO.setGoalStatus(appUserInfoEntity.getGoalStatus());
        partnerInfoVO.setTtaSwitch(Objects.isNull(userTtaTrialEntity) ? 0 : 1);

        return partnerInfoVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editPassword(EditPasswordDTO editPasswordDTO) {
        String oldPassword = editPasswordDTO.getOldPwd();
        String newPassword = editPasswordDTO.getNewPwd();

        Long partnerId = ContextHolder.<BaseLoginInfo>getLoginInfo().getPartnerId();
        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getById(partnerId);
        if (!PasswordUtil.match(oldPassword, partnerEntity.getPassword(), partnerEntity.getSalt())) {
            throw new SsoException("Old password incorrect.");
        }

        partnerEntity.setSalt(PasswordUtil.generateSalt(20));
        partnerEntity.setPassword(PasswordUtil.encryptPassword(newPassword, partnerEntity.getSalt()));
        UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), partnerEntity);
        appUserPartnerDAO.updateById(partnerEntity);

        log.info("partner:{} edit password", partnerId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void userUnbindPartner() {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getByUserId(userId);
        if (Objects.isNull(partnerEntity)) {
            return;
        }

        // 删除Partner令牌
        String partnerToken = cacheManager.getPartnerMarkToken(partnerEntity.getId());
        if (StringUtils.isNotBlank(partnerToken)) {
            authProvider.deleteToken(partnerToken, UserTypeEnum.PARTNER_USER.getType());
        }

        // 邮箱无效化
        String invalidPartnerEmail = partnerEntity.getEmail()
                + ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN)
                + StringUtil.random(8);
        partnerEntity.setEmail(invalidPartnerEmail);
        String invalidUserEmail = partnerEntity.getUserEmail()
                + ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN)
                + StringUtil.random(8);
        partnerEntity.setUserEmail(invalidUserEmail);
        // 删除Partner
        partnerEntity.setUserId(-userId);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, partnerEntity);
        appUserPartnerDAO.updateById(partnerEntity);
        appUserPartnerDAO.removeById(partnerEntity);
        cacheManager.deleteUserDetailCache(userId);

        log.info("user:{} unbind partner:{}", userId, partnerEntity.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void partnerUnbindUser() {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        Long partnerId = ContextHolder.<BaseLoginInfo>getLoginInfo().getPartnerId();

        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getById(partnerId);
        if (Objects.isNull(partnerEntity)) {
            return;
        }

        // 删除Partner令牌
        String partnerToken = cacheManager.getPartnerMarkToken(partnerEntity.getId());
        if (StringUtils.isNotBlank(partnerToken)) {
            authProvider.deleteToken(partnerToken, UserTypeEnum.PARTNER_USER.getType());
        }

        // 邮箱无效化
        String invalidPartnerEmail = partnerEntity.getEmail()
                + ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN)
                + StringUtil.random(8);
        partnerEntity.setEmail(invalidPartnerEmail);
        String invalidUserEmail = partnerEntity.getUserEmail()
                + ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN)
                + StringUtil.random(8);
        partnerEntity.setUserEmail(invalidUserEmail);
        // 删除Partner
        partnerEntity.setUserId(-partnerEntity.getUserId());
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, partnerEntity);
        appUserPartnerDAO.updateById(partnerEntity);
        appUserPartnerDAO.removeById(partnerEntity.getId());
        cacheManager.deleteUserDetailCache(partnerEntity.getUserId());

        log.info("partner:{} unbind user:{}", partnerId, partnerEntity.getUserId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editRemindTime(UserRemindTimeDTO userRemindTimeDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        Long partnerId = ContextHolder.<BaseLoginInfo>getLoginInfo().getPartnerId();

        Long remindTime = userRemindTimeDTO.getRemindTime();
        Integer remindFlag = userRemindTimeDTO.getRemindFlag();

        AppUserPartnerReminderEntity partnerReminderEntity = appUserPartnerReminderDAO.getByPartnerId(partnerId);
        if (partnerReminderEntity == null) {
            partnerReminderEntity = new AppUserPartnerReminderEntity();
            partnerReminderEntity.setPartnerId(partnerId);
            partnerReminderEntity.setRemindFlag(remindFlag);
            if (remindTime != null) {
                String remindTimeStr = ZoneDateUtil.format(timeZone, remindTime, DatePatternConst.DATE_TIME_PATTERN);
                partnerReminderEntity.setRemindTime(remindTime);
                partnerReminderEntity.setRemindTimeStr(remindTimeStr);
            }
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, partnerReminderEntity);
            appUserPartnerReminderDAO.save(partnerReminderEntity);

        } else {
            partnerReminderEntity.setRemindFlag(remindFlag);
            Long dbRemindTime = partnerReminderEntity.getRemindTime();
            if (remindTime != null) {
                if (!remindTime.equals(dbRemindTime)) {
                    UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, partnerReminderEntity);
                }
                partnerReminderEntity.setRemindTime(remindTime);
                partnerReminderEntity.setRemindTimeStr(ZoneDateUtil.format(timeZone, remindTime, DatePatternConst.DATE_TIME_PATTERN));
            }
            appUserPartnerReminderDAO.updateById(partnerReminderEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editPushToken(PushTokenDTO pushTokenDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        Long partnerId = ContextHolder.<BaseLoginInfo>getLoginInfo().getPartnerId();

        if (pushTokenDTO.getPlatform() == null || StringUtils.isBlank(pushTokenDTO.getPushToken())) {
            log.info("partner:{} input param edit push-token is null", partnerId);
        }

        AppUserPartnerReminderEntity partnerReminderEntity = appUserPartnerReminderDAO.getByPartnerId(partnerId);
        if (partnerReminderEntity == null) {
            partnerReminderEntity = new AppUserPartnerReminderEntity();
            partnerReminderEntity.setPartnerId(partnerId);
            partnerReminderEntity.setPushToken(pushTokenDTO.getPushToken());
            partnerReminderEntity.setPlatform(pushTokenDTO.getPlatform());
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, partnerReminderEntity);
            appUserPartnerReminderDAO.save(partnerReminderEntity);
        } else {
            partnerReminderEntity.setPushToken(pushTokenDTO.getPushToken());
            partnerReminderEntity.setPlatform(pushTokenDTO.getPlatform());
            appUserPartnerReminderDAO.updateById(partnerReminderEntity);
        }
    }
}

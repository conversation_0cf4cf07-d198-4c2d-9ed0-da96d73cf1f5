package com.mira.sso.service;

import com.mira.sso.controller.vo.UserForgetPasswordVO;
import com.mira.sso.service.dto.ChangeEmailDTO;
import com.mira.sso.service.dto.PartnerInviteDTO;
import com.mira.sso.service.dto.ResetPasswordDTO;

/**
 * Partner操作接口
 *
 * <AUTHOR>
 */
public interface IPartnerOperateService {
    /**
     * 邀请partner
     *
     * @param partnerInviteDTO 参数
     * @param flag             首次邀请-0，再次邀请-1
     * @return String
     */
    String invite(PartnerInviteDTO partnerInviteDTO, int flag);

    /**
     * 告知邀请partner
     *
     * @param partnerInviteDTO 参数
     * @return String
     */
    String inviteInform(PartnerInviteDTO partnerInviteDTO);

    /**
     * 确认邀请
     *
     * @param hash 加密的用户信息
     * @return String
     */
    String activation(String hash);

    /**
     * 忘记密码
     *
     * @param email 邮箱
     * @param flag  首次忘记密码-0，再次发送忘记密码-1
     * @return UserForgetPasswordVO
     */
    UserForgetPasswordVO resetPassword(String email, int flag);

    /**
     * 确认忘记密码
     *
     * @param resetPasswordDTO 参数
     * @return String
     */
    String resetPasswordConfirm(ResetPasswordDTO resetPasswordDTO);

    /**
     * 获取忘记密码的状态
     *
     * @param email 邮箱
     * @return String
     */
    String resetPasswordStatus(String email);

    /**
     * 修改partner的Email
     *
     * @param changeEmailDTO 参数
     * @param flag           首次修改邮箱-0，再次发送修改邮箱-1
     * @return String
     */
    String changeEmail(ChangeEmailDTO changeEmailDTO, int flag);

    /**
     * 确认修改partner的Email
     *
     * @param hash 加密的用户信息
     * @return String
     */
    String changeEmailConfirm(String hash);
}

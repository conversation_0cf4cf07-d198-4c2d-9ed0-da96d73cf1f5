package com.mira.sso.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Getter
@Setter
@ApiModel("Partner邀请DTO")
public class PartnerInviteDTO {
    @ApiModelProperty("邮箱")
    @Pattern(regexp = "[\\w!#$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?",
            message = "The email address is invalid.")
    @Size(max = 99, message = "Please enter a valid email address.")
    private String email;
    
    @ApiModelProperty("英文名")
    private String firstName;
    
    @ApiModelProperty("英文姓")
    private String lastName;
}

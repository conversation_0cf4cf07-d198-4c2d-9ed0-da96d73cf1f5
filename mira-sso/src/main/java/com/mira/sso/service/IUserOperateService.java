package com.mira.sso.service;

import com.mira.sso.controller.vo.UserForgetPasswordVO;
import com.mira.sso.service.dto.ChangeEmailDTO;
import com.mira.sso.service.dto.ResetPasswordDTO;

/**
 * 用户信息操作接口
 *
 * <AUTHOR>
 */
public interface IUserOperateService {
    /**
     * 忘记密码
     *
     * @param email 邮箱
     * @param flag  首次忘记密码-0，再次发送忘记密码-1
     * @return UserForgetPasswordVO
     */
    UserForgetPasswordVO resetPassword(String email, int flag);

    /**
     * 确认忘记密码
     *
     * @param resetPasswordDTO 参数
     * @return String
     */
    String resetPasswordConfirm(ResetPasswordDTO resetPasswordDTO);

    /**
     * 获取忘记密码的状态
     *
     * @param email 邮箱
     * @return String
     */
    String resetPasswordStatus(String email);

    /**
     * 修改邮箱
     *
     * @param changeEmailDTO 参数
     * @param flag           首次修改邮箱-0，再次发送修改邮箱-1
     * @return String
     */
    String changeEmail(ChangeEmailDTO changeEmailDTO, int flag);

    /**
     * 确认修改用户的邮箱
     *
     * @param hash 加密的用户信息
     * @return String
     */
    String changeEmailConfirm(String hash);

    /**
     * 修改用户邮箱
     *
     * @param userId   用户id
     * @param timeZone 时区
     * @param newEmail 新邮箱
     * @param source   来源
     */
    void changeUserEmail(Long userId, String timeZone, String newEmail, String source);

    /**
     * 获取修改邮箱的状态
     *
     * @param changeEmailDTO 参数
     * @return String
     */
    String changeEmailStatus(ChangeEmailDTO changeEmailDTO);
}

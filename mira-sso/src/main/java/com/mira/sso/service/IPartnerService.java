package com.mira.sso.service;

import com.mira.api.message.dto.PushTokenDTO;
import com.mira.sso.controller.vo.PartnerInfoVO;
import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.api.sso.dto.LoginInfoDTO;
import com.mira.sso.service.dto.UserRegisterDTO;
import com.mira.sso.service.dto.UserRemindTimeDTO;

/**
 * Partner服务接口
 *
 * <AUTHOR>
 */
public interface IPartnerService {
    /**
     * 注册
     *
     * @param userRegisterDTO 注册信息
     * @return 令牌
     */
    String register(UserRegisterDTO userRegisterDTO) throws Exception;

    /**
     * 用户名密码登录
     *
     * @param loginInfoDTO 登录信息
     * @return 令牌
     */
    String login(LoginInfoDTO loginInfoDTO) throws Exception;

    /**
     * 退出登录
     */
    void logout();

    /**
     * Partner详情
     *
     * @return PartnerInfoVO
     */
    PartnerInfoVO info();

    /**
     * 修改Partner的密码
     *
     * @param editPasswordDTO 参数
     */
    void editPassword(EditPasswordDTO editPasswordDTO);

    /**
     * 用户移除Partner
     */
    void userUnbindPartner();

    /**
     * Partner移除用户
     */
    void partnerUnbindUser();

    /**
     * 修改partner的remind-time
     *
     * @param userRemindTimeDTO remind time
     */
    void editRemindTime(UserRemindTimeDTO userRemindTimeDTO);

    /**
     * 修改partner的pushToken
     *
     * @param pushTokenDTO push token
     */
    void editPushToken(PushTokenDTO pushTokenDTO);
}

package com.mira.sso.service;

import com.mira.sso.controller.vo.RegisterResultVO;
import com.mira.sso.service.dto.UserRegisterDTO;

/**
 * 注册相关服务接口
 *
 * <AUTHOR>
 */
public interface IRegisterService {
    /**
     * 用户注册
     *
     * @param userRegisterDTO 注册参数
     * @param flag            首次注册标记-0，再次调用注册标记-1
     * @param channel         渠道
     * @return RegisterResultVO
     */
    RegisterResultVO register(UserRegisterDTO userRegisterDTO, int flag, int channel);

    /**
     * 确认注册
     *
     * @param hash 用户信息加密字符串
     * @return String
     */
    String confirmRegister(String hash);

    /**
     * 获取用户激活状态
     *
     * @param email 邮箱
     * @return String
     */
    String queryEnableStatus(String email);
}

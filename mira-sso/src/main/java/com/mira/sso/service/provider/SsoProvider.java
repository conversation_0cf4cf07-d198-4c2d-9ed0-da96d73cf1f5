package com.mira.sso.service.provider;

import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.core.response.CommonResult;
import com.mira.sso.service.IUserOperateService;
import com.mira.sso.service.manager.UserInfoManager;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户服务feign接口实现
 *
 * <AUTHOR>
 */
@RestController
public class SsoProvider implements ISsoProvider {
    @Resource
    private UserInfoManager userInfoManager;
    @Resource
    private IUserOperateService userOperateService;

    @Override
    public CommonResult<LoginUserInfoDTO> getUserLoginInfo(Long userId) {
        return CommonResult.OK(userInfoManager.loginUserInfo(userId));
    }

    @Override
    public CommonResult<Void> changeUserEmail(Long userId, String timeZone, String newEmail, String source) {
        userOperateService.changeUserEmail(userId, timeZone, newEmail, source);
        return CommonResult.OK();
    }
}

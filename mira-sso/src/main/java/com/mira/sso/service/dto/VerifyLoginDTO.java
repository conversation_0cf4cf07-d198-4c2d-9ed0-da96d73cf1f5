package com.mira.sso.service.dto;

import com.mira.api.sso.dto.LoginInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ApiModel(description = "验证码登录")
public class VerifyLoginDTO extends LoginInfoDTO {
    @ApiModelProperty("verifyCode")
    @NotBlank(message = "verify code can not be empty.")
    private String verifyCode;
}

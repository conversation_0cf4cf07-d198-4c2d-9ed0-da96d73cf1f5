package com.mira.sso.service;

import com.mira.api.sso.dto.LoginInfoDTO;
import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.sso.controller.vo.UserInfoVO;
import com.mira.sso.controller.vo.WebsiteLoginVO;
import com.mira.sso.service.dto.*;

/**
 * 用户登录接口
 *
 * <AUTHOR>
 */
public interface IUserLoginService {
    /**
     * 用户名密码登录
     *
     * @param loginInfoDTO 登录信息
     * @return 令牌
     */
    String login(LoginInfoDTO loginInfoDTO) throws Exception;

    /**
     * 验证码登录
     *
     * @param verifyLoginDTO 登录信息
     * @return 令牌
     */
    String appCodeLogin(VerifyLoginDTO verifyLoginDTO) throws Exception;

    /**
     * 登录用户详情
     *
     * @return UserInfoVO
     */
    UserInfoVO info();

    /**
     * 重置初始密码
     *
     * @param resetInitPasswordDTO 参数
     */
    void resetInitPassword(ResetInitPasswordDTO resetInitPasswordDTO);

    /**
     * 修改密码
     *
     * @param editPasswordDTO 参数
     */
    void editPassword(EditPasswordDTO editPasswordDTO);

    /**
     * 退出登录
     */
    void logout();

    /**
     * 删除用户
     *
     * @param userDeleteDTO 删除选项
     * @return 用户编号
     */
    Long delete(UserDeleteDTO userDeleteDTO);

    /**
     * 网站用户登录
     *
     * @param loginInfoDTO 登录信息
     * @return WebsiteLoginVO
     */
    WebsiteLoginVO websiteLogin(LoginInfoDTO loginInfoDTO) throws Exception;

    /**
     * 网站用户验证码登录
     *
     * @param verifyLoginDTO 验证码
     * @return WebsiteLoginVO
     */
    WebsiteLoginVO websiteCodeLogin(VerifyLoginDTO verifyLoginDTO) throws Exception;
}

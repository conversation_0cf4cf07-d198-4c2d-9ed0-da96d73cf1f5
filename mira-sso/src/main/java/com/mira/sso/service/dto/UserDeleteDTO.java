package com.mira.sso.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel("删除用户，类型选项")
public class UserDeleteDTO {
    @ApiModelProperty("type 1:keep data; 0 remove data")
    @NotNull(message = "type can not be empty.")
    private Integer type;
}

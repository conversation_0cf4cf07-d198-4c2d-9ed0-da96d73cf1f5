package com.mira.sso.service.manager;

import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.mira.api.iam.consts.TokenCacheConst;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.redis.cache.RedisComponent;
import com.mira.redis.cache.StringRedisComponent;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.properties.CacheExpireProperties;
import com.mira.sso.service.dto.ChangeEmailDTO;
import com.mira.sso.service.dto.UserRegisterDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Cache manager
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class CacheManager {
    private final RedisComponent redisComponent;
    private final StringRedisComponent stringRedisComponent;
    private final CacheExpireProperties cacheExpireProperties;

    /**
     * 缓存是否存在
     *
     * @param userId 用户编号
     * @param key    cache key 前缀
     * @return true/false
     */
    public boolean cacheExist(Long userId, String key) {
        return redisComponent.exists(key + userId);
    }

    /**
     * 删除缓存
     *
     * @param userId 用户编号
     * @param key    cache key 前缀
     */
    public void cacheDelete(Long userId, String key) {
        redisComponent.delete(key + userId);
    }

    /**
     * 获取剩余过期时间
     *
     * @param userId 用户编号
     * @param key    cache key 前缀
     * @return 过期时间，秒
     */
    public Long cacheRemainExpire(Long userId, String key) {
        return redisComponent.ttl(key + userId);
    }

    /**
     * Cache 注册信息
     *
     * @param appUser         用户信息
     * @param userRegisterDTO 注册信息
     * @param hash            hash
     * @param flag            判定是注册还是再次注册，0：注册；1：再次注册
     * @param channel         渠道
     */
    public void cacheRegister(AppUserEntity appUser, UserRegisterDTO userRegisterDTO,
                              String hash, int flag, int channel) {
        String cacheKey = RedisCacheKeyConst.EMAIL_USER_REGISTER + appUser.getId();
        Map<String, Object> cacheMap = Maps.newHashMap();
        cacheMap.put("email", userRegisterDTO.getEmail());
        cacheMap.put("password", userRegisterDTO.getPassword());
        cacheMap.put("firstName", userRegisterDTO.getFirstName());
        cacheMap.put("lastName", userRegisterDTO.getLastName());
        cacheMap.put("flag", flag);
        cacheMap.put("hash", URLDecoder.decode(hash, StandardCharsets.UTF_8));
        cacheMap.put("registerTimestamp", System.currentTimeMillis());
        cacheMap.put("channel", channel);
        redisComponent.setEx(cacheKey, cacheMap, cacheExpireProperties.getRegister(), TimeUnit.MINUTES);
    }

    /**
     * Cache 获取注册信息
     *
     * @param userId 用户编号
     * @return Map
     */
    public Map<String, Object> getCacheRegister(Long userId) {
        String cacheKey = RedisCacheKeyConst.EMAIL_USER_REGISTER + userId;
        Map<String, Object> result = redisComponent.get(cacheKey, Map.class);
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    /**
     * Cache 忘记密码信息
     *
     * @param appUser 用户信息
     * @param hash    hash
     * @param flag    判定是首次reset还是再次修改，0：首次reset；1：再次reset
     */
    public void cacheResetPassword(AppUserEntity appUser, String hash, int flag) {
        String cacheKey = RedisCacheKeyConst.EMAIL_RESET_PW + appUser.getId();
        Map<String, Object> cacheMap = Maps.newHashMap();
        cacheMap.put("email", appUser.getEmail());
        cacheMap.put("flag", flag);
        cacheMap.put("hash", URLDecoder.decode(hash, StandardCharsets.UTF_8));
        cacheMap.put("resetPwTimestamp", System.currentTimeMillis());
        cacheMap.put("oldPwEncrypt", appUser.getPassword());
        redisComponent.setEx(cacheKey, cacheMap, cacheExpireProperties.getResetPassword(), TimeUnit.MINUTES);
    }

    /**
     * Cache 获取忘记密码信息
     *
     * @param userId 用户编号
     * @return Map
     */
    public Map<String, Object> getCacheResetPassword(Long userId) {
        String cacheKey = RedisCacheKeyConst.EMAIL_RESET_PW + userId;
        Map<String, Object> result = redisComponent.get(cacheKey, Map.class);
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    /**
     * Cache 修改用户的邮箱信息
     *
     * @param userId 用户编号
     * @param hash   hash
     * @param flag   判定是首次修改还是再次修改，0：修改；1：再次修改
     */
    public void cacheChangeEmail(Long userId, ChangeEmailDTO changeEmailDTO, String hash, int flag) {
        String cacheKey = RedisCacheKeyConst.EMAIL_CHANGE_EMAIL + userId;
        Map<String, Object> cacheMap = Maps.newHashMap();
        cacheMap.put("oldEmail", changeEmailDTO.getOldEmail());
        cacheMap.put("newEmail", changeEmailDTO.getNewEmail());
        cacheMap.put("flag", flag);
        cacheMap.put("hash", URLDecoder.decode(hash, StandardCharsets.UTF_8));
        cacheMap.put("changeEmailTimestamp", System.currentTimeMillis());
        redisComponent.setEx(cacheKey, cacheMap, cacheExpireProperties.getChangeEmail(), TimeUnit.MINUTES);
    }

    /**
     * Cache 获取修改用户的邮箱信息
     *
     * @param userId 用户编号
     * @return Map
     */
    public Map<String, Object> getCacheChangeEmail(Long userId) {
        String cacheKey = RedisCacheKeyConst.EMAIL_CHANGE_EMAIL + userId;
        Map<String, Object> result = redisComponent.get(cacheKey, Map.class);
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    /**
     * Cache 设置用户登录锁定标记
     *
     * @param userId  用户编号
     * @param minutes 锁定时间，分钟
     */
    public void cacheLoginLock(Long userId, long minutes) {
        String cacheKey = RedisCacheKeyConst.USER_LOGIN_LOCK + userId;
        redisComponent.setEx(cacheKey, 1, minutes, TimeUnit.MINUTES);
    }

    /**
     * Cache 自增1后获取用户登录失败标记最新值
     *
     * @param userId 用户编号
     * @return 失败的登录次数
     */
    public Long getCacheLoginErrorIncr(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_LOGIN_ERROR + userId;
        Long incr = redisComponent.incr(cacheKey);
        if (incr == 1) {
            redisComponent.setEx(cacheKey, incr, cacheExpireProperties.getLoginError(), TimeUnit.MINUTES);
        }
        return incr;
    }

    /**
     * Cache Partner 邀请
     *
     * @param appUserPartner Partner
     * @param hash           hash
     * @param flag           判定是首次邀请还是再次邀请，0：首次邀请；1：再次邀请
     */
    public void cachePartnerInvite(AppUserPartnerEntity appUserPartner, String hash, int flag) {
        String cacheKey = RedisCacheKeyConst.PARTNER_INVITE_INFO + appUserPartner.getId();
        Map<String, Object> cacheMap = Maps.newHashMap();
        cacheMap.put("email", appUserPartner.getEmail());
        cacheMap.put("flag", flag);
        cacheMap.put("hash", URLDecoder.decode(hash, StandardCharsets.UTF_8));
        cacheMap.put("inviteTimestamp", System.currentTimeMillis());
        redisComponent.setEx(cacheKey, cacheMap, cacheExpireProperties.getPartnerInvite(), TimeUnit.MINUTES);
    }

    /**
     * 获取Partner邀请信息
     *
     * @param partnerId Partner id
     * @return Map
     */
    public Map<String, Object> getCachePartnerInvite(Long partnerId) {
        String cacheKey = RedisCacheKeyConst.PARTNER_INVITE_INFO + partnerId;
        Map<String, Object> result = redisComponent.get(cacheKey, Map.class);
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    /**
     * Cache Partner忘记密码信息
     *
     * @param partnerEntity 用户信息
     * @param hash          hash
     * @param flag          判定是首次reset还是再次修改，0：首次reset；1：再次reset
     */
    public void cachePartnerResetPassword(AppUserPartnerEntity partnerEntity, String hash, int flag) {
        String cacheKey = RedisCacheKeyConst.PARTNER_EMAIL_RESET_PW + partnerEntity.getId();
        Map<String, Object> cacheMap = Maps.newHashMap();
        cacheMap.put("email", partnerEntity.getEmail());
        cacheMap.put("flag", flag);
        cacheMap.put("hash", URLDecoder.decode(hash, StandardCharsets.UTF_8));
        cacheMap.put("resetPwTimestamp", System.currentTimeMillis());
        cacheMap.put("oldPwEncrypt", partnerEntity.getPassword());
        redisComponent.setEx(cacheKey, cacheMap, cacheExpireProperties.getResetPassword(), TimeUnit.MINUTES);
    }

    /**
     * Cache 获取Partner忘记密码信息
     *
     * @param partnerId Partner编号
     * @return Map
     */
    public Map<String, Object> getPartnerResetPassword(Long partnerId) {
        String cacheKey = RedisCacheKeyConst.PARTNER_EMAIL_RESET_PW + partnerId;
        Map<String, Object> result = redisComponent.get(cacheKey, Map.class);
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    /**
     * Cache 修改Partner的邮箱信息
     *
     * @param partnerId Partner编号
     * @param hash      hash
     * @param flag      判定是首次修改还是再次修改，0：修改；1：再次修改
     */
    public void cachePartnerChangeEmail(Long partnerId, ChangeEmailDTO changeEmailDTO, String hash, int flag) {
        String cacheKey = RedisCacheKeyConst.PARTNER_EMAIL_CHANGE_EMAIL + partnerId;
        Map<String, Object> cacheMap = Maps.newHashMap();
        cacheMap.put("oldEmail", changeEmailDTO.getOldEmail());
        cacheMap.put("newEmail", changeEmailDTO.getNewEmail());
        cacheMap.put("flag", flag);
        cacheMap.put("hash", URLDecoder.decode(hash, StandardCharsets.UTF_8));
        cacheMap.put("changeEmailTimestamp", System.currentTimeMillis());
        redisComponent.setEx(cacheKey, cacheMap, cacheExpireProperties.getChangeEmail(), TimeUnit.MINUTES);
    }

    /**
     * Cache 获取修改Partner的邮箱信息
     *
     * @param partnerId Partner编号
     * @return Map
     */
    public Map<String, Object> getPartnerChangeEmail(Long partnerId) {
        String cacheKey = RedisCacheKeyConst.PARTNER_EMAIL_CHANGE_EMAIL + partnerId;
        Map<String, Object> result = redisComponent.get(cacheKey, Map.class);
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    /**
     * 获取App User标记令牌
     *
     * @param userId 用户编号
     * @return 令牌
     */
    public String getAppMarkToken(Long userId) {
        String cacheKey = TokenCacheConst.APP_ID_TOKEN_MAPPING + userId;
        return redisComponent.get(cacheKey);
    }

    /**
     * 获取Partner标记令牌
     *
     * @param partnerId Partner编号
     * @return 令牌
     */
    public String getPartnerMarkToken(Long partnerId) {
        String cacheKey = TokenCacheConst.PARTNER_ID_TOKEN_MAPPING + partnerId;
        return redisComponent.get(cacheKey);
    }

    /**
     * 缓存登录设备校验码
     *
     * @param userId     用户id
     * @param verifyCode 校验码
     */
    public void cacheDeviceVerifyCode(Long userId, String verifyCode) {
        String cacheKey = RedisCacheKeyConst.USER_LOGIN_VERIFY_CODE + userId;
        redisComponent.setEx(cacheKey, verifyCode, cacheExpireProperties.getDeviceVerifyCode(), TimeUnit.MINUTES);
    }

    /**
     * 获取登录设备校验码
     *
     * @param userId 用户id
     * @return 校验码
     */
    public String getDeviceVerifyCode(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_LOGIN_VERIFY_CODE + userId;
        return redisComponent.get(cacheKey);
    }

    /**
     * 邮箱是否在设备白名单中
     *
     * @param email 邮箱
     * @return true/false
     */
    public Boolean isInWhiteDeviceList(String email) {
        return stringRedisComponent.sIsmember(RedisCacheKeyConst.DEVICE_ID_WHITE, email);
    }

    /**
     * 删除用户详情缓存
     *
     * @param userId 用户id
     */
    public void deleteUserDetailCache(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_LOGIN_INFO + userId;
        redisComponent.delete(cacheKey);
    }
}

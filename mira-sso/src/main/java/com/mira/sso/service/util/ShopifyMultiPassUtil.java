package com.mira.sso.service.util;

import com.mira.api.thirdparty.consts.ShopifyStoreConst;
import com.mira.core.consts.enums.CurrencyEnum;
import com.mira.core.util.JsonUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ArrayUtils;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.util.Arrays;
import java.util.Date;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-07-06
 **/
@Slf4j
@SuppressWarnings("all")
public class ShopifyMultiPassUtil {
    /**
     * 生成shopify multipass接口 的请求url
     * 本地生成，不会发生http请求
     *
     * @param email
     * @param currency
     * @return
     */
    public static String getUrl(String email, String currency) {
        ShopifyMultiPassParam multiPassParam = new ShopifyMultiPassParam();
        multiPassParam.setEmail(email);
        multiPassParam.setCreated_at(new Date().toGMTString());

        String multipassApi = "/account/login/multipass/";
        String multipassSecret = null;
        String shopifyUrl = null;
        switch (CurrencyEnum.get(currency)) {
            case USD:
                multipassSecret = ShopifyStoreConst.Us.MULTIPASS_SECRET;
                shopifyUrl = ShopifyStoreConst.Us.BASE_URL;
                break;
            case AUD:
                multipassSecret = ShopifyStoreConst.Au.MULTIPASS_SECRET;
                shopifyUrl = ShopifyStoreConst.Au.BASE_URL;
                break;
            case CAD:
                multipassSecret = ShopifyStoreConst.Ca.MULTIPASS_SECRET;
                shopifyUrl = ShopifyStoreConst.Ca.BASE_URL;
                break;
            case EUR:
                multipassSecret = ShopifyStoreConst.Eu.MULTIPASS_SECRET;
                shopifyUrl = ShopifyStoreConst.Eu.BASE_URL;
                break;
            case GBP:
                multipassSecret = ShopifyStoreConst.Uk.MULTIPASS_SECRET;
                shopifyUrl = ShopifyStoreConst.Uk.BASE_URL;
                break;
            default:
                multipassSecret = ShopifyStoreConst.Test.MULTIPASS_SECRET;
                shopifyUrl = ShopifyStoreConst.Test.BASE_URL;
                break;
        }

        String multipassToken = getMultipassToken(multiPassParam, multipassSecret);
        return shopifyUrl + multipassApi + multipassToken;
    }

    /**
     * shopify multipass接口的参数
     */
    @Getter
    @Setter
    public static class ShopifyMultiPassParam {
        String email;
        String created_at;
    }

    private static String getMultipassToken(ShopifyMultiPassParam multiPassParam, String multipassSecret) {
        String toJson = JsonUtil.toJson(multiPassParam);
        byte[] encrypted = new byte[0];
        Mac sha256HMAC = null;

        try {
            // Get the encryption and signature keys
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            // multipassSecret is defined at this point, the value comes from our store admin
            byte[] hash = digest.digest(multipassSecret.getBytes("UTF-8"));
            byte[] encryptionKey = Arrays.copyOfRange(hash, 0, 16);
            byte[] signatureKey = Arrays.copyOfRange(hash, 16, 32);
            // Get random IV
            SecureRandom random = new SecureRandom();
            byte iv[] = new byte[16];
            random.nextBytes(iv);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            // Encrypt user data
            SecretKeySpec skeySpec = new SecretKeySpec(encryptionKey, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, ivSpec);
            encrypted = ArrayUtils.addAll(iv, cipher.doFinal(toJson.getBytes()));
            // Sign encrypted user data
            sha256HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(signatureKey, "HmacSHA256");
            sha256HMAC.init(secretKeySpec);
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException | NoSuchPaddingException |
                 InvalidKeyException | InvalidAlgorithmParameterException | IllegalBlockSizeException |
                 BadPaddingException e) {
            log.error("get multipass url err:cause:{},message:{}", e.getCause(), e.getMessage());
        }
        byte[] signature = sha256HMAC.doFinal(encrypted);
        // Combine the encrypted data with its signature and base64-encode it
        String token = Base64.encodeBase64URLSafeString(ArrayUtils.addAll(encrypted, signature));
        token = token.replace('+', '-')  // Replace + with -
                .replace('/', '_'); // Replace / with _
        return token;
    }
}

package com.mira.sso.service;

import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.RequestSendFlag;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.response.enums.BaseCodeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.PasswordUtil;
import com.mira.core.util.RsaUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.sso.consts.PartnerStatusConst;
import com.mira.sso.controller.vo.UserForgetPasswordVO;
import com.mira.sso.dal.dao.AppUserDAO;
import com.mira.sso.dal.dao.AppUserInfoDAO;
import com.mira.sso.dal.dao.AppUserPartnerDAO;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.sso.dal.entity.AppUserInfoEntity;
import com.mira.sso.dal.entity.AppUserPartnerEntity;
import com.mira.sso.exception.SsoException;
import com.mira.sso.producer.EmailProducer;
import com.mira.sso.service.dto.ChangeEmailDTO;
import com.mira.sso.service.dto.PartnerInviteDTO;
import com.mira.sso.service.dto.ResetPasswordDTO;
import com.mira.sso.service.manager.CacheManager;
import com.mira.web.properties.RsaProperties;
import com.mira.web.util.HashDecodeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Partner操作接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PartnerOperateServiceImpl implements IPartnerOperateService {
    private final AppUserPartnerDAO appUserPartnerDAO;
    private final AppUserDAO appUserDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final RsaProperties rsaProperties;
    private final CacheManager cacheManager;
    private final EmailProducer emailProducer;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String invite(PartnerInviteDTO partnerInviteDTO, int flag) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        // 校验
        if (RequestSendFlag.FIRST_SEND == flag) {
            AppUserPartnerEntity existPartner = appUserPartnerDAO.getByUserId(loginInfo.getId());
            if (Objects.nonNull(existPartner)) {
                throw new SsoException("You have already invited a partner(" + existPartner.getEmail() + "), please do not repeat the operation!");
            }
        }

        AppUserEntity appUser = appUserDAO.getByEmail(partnerInviteDTO.getEmail());
        if (Objects.nonNull(appUser)) {
            throw new SsoException(BizCodeEnum.USER_EXIST);
        }

        AppUserPartnerEntity partner = appUserPartnerDAO.getByEmail(partnerInviteDTO.getEmail());
        if ((RequestSendFlag.FIRST_SEND == flag)) {
            if (Objects.nonNull(partner)) {
                throw new SsoException(BizCodeEnum.PARTNER_EXIST);
            }
        } else {
            if (Objects.isNull(partner)) {
                throw new SsoException("A system error occurred. Please retry invite.");
            }
            if (Objects.nonNull(partner) && !partner.getStatus().equals(0)) {
                throw new SsoException(BizCodeEnum.PARTNER_EXIST);
            }
        }

        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(loginInfo.getId());

        // insert or update
        if ((RequestSendFlag.FIRST_SEND == flag)) {
            partner = new AppUserPartnerEntity();
            partner.setEmail(partnerInviteDTO.getEmail());
            partner.setFirstName(partnerInviteDTO.getFirstName());
            partner.setLastName(partnerInviteDTO.getLastName());
            partner.setStatus(0);
            partner.setUserId(loginInfo.getId());
            partner.setUserEmail(loginInfo.getUsername());
            partner.setPassword("123");
            partner.setSalt("123");
            UpdateEntityTimeUtil.setBaseEntityTime(loginInfo.getTimeZone(), partner);
            appUserPartnerDAO.save(partner);
        } else {
            partner.setEmail(partnerInviteDTO.getEmail());
            partner.setUserId(loginInfo.getId());
            partner.setUserEmail(loginInfo.getUsername());
            partner.setPassword("123");
            partner.setSalt("123");
            UpdateEntityTimeUtil.setBaseEntityTime(loginInfo.getTimeZone(), partner);
            appUserPartnerDAO.updateById(partner);
        }

        // 发送邮件
        Map<String, String> emailVariable = emailProducer.partnerInvite(partner, appUserInfo);

        // cache
        String hash = emailVariable.get("userHash");
        cacheManager.cachePartnerInvite(partner, hash, flag);
        cacheManager.deleteUserDetailCache(partner.getUserId());

        log.info("user:{} will invite partner, target email:{}, flag:{}", loginInfo.getId(), partnerInviteDTO.getEmail(), flag);
        return emailVariable.get("userHash");
    }

    @Override
    public String inviteInform(PartnerInviteDTO partnerInviteDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(userId);
        String nickName = appUserInfo.getNickname();

        AppUserEntity appUser = appUserDAO.getByEmail(partnerInviteDTO.getEmail());
        if (Objects.nonNull(appUser)) {
            emailProducer.invitePartnerInformUser(appUser, nickName);
            return CommonResult.OK().getMsg();
        }

        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getByEmail(partnerInviteDTO.getEmail());
        if (Objects.nonNull(partnerEntity)) {
            emailProducer.invitePartnerInformPartner(partnerEntity, nickName);
        }

        return CommonResult.OK().getMsg();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String activation(String hash) {
        hash = HashDecodeUtil.decodeHash(hash);
        String decode = RsaUtil.decodeRsa(hash, rsaProperties.getPrivateKey());
        Map<String, Object> infoMap = JsonUtil.toObject(decode, HashMap.class);
        Long partnerId = Long.valueOf(infoMap.get("partnerId").toString());

        // check
        Map<String, Object> cacheChangeEmailInfo = cacheManager.getCachePartnerInvite(partnerId);
        if (MapUtils.isEmpty(cacheChangeEmailInfo)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED);
        }
        String currentHash = cacheChangeEmailInfo.get("hash").toString();
        if (StringUtils.isNotBlank(currentHash) && !currentHash.equals(hash)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED);
        }

        // 更新
        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getById(partnerId);
        if (Objects.isNull(partnerEntity)) {
            throw new SsoException("The account doesn't exist.");
        }
        partnerEntity.setStatus(PartnerStatusConst.ACTIVE_BUT_NOT_REGISTER);
        appUserPartnerDAO.updateById(partnerEntity);
        cacheManager.deleteUserDetailCache(partnerEntity.getUserId());

        log.info("partner:{} activation, email:{}", partnerId, cacheChangeEmailInfo.get("email"));
        return CommonResult.OK().getMsg();
    }

    @Override
    public UserForgetPasswordVO resetPassword(String email, int flag) {
        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getByEmail(email);
        if (Objects.isNull(partnerEntity)) {
            throw new SsoException("The account doesn't exist.");
        }

        // 发送邮件
        Map<String, String> emailVariable = emailProducer.partnerResetPassword(partnerEntity);

        // save cache
        String hash = emailVariable.get("userHash");
        cacheManager.cachePartnerResetPassword(partnerEntity, hash, flag);

        // result
        UserForgetPasswordVO userForgetPasswordVO = new UserForgetPasswordVO();
        userForgetPasswordVO.setEmailFlag(true);

        log.info("partner:{} reset password, flag:{}", partnerEntity.getId(), flag);
        return userForgetPasswordVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String resetPasswordConfirm(ResetPasswordDTO resetPasswordDTO) {
        String hash = HashDecodeUtil.decodeHash(resetPasswordDTO.getHash());
        String decode = RsaUtil.decodeRsa(hash, rsaProperties.getPrivateKey());
        Map<String, Object> infoMap = JsonUtil.toObject(decode, HashMap.class);
        Long partnerId = Long.valueOf(infoMap.get("partnerId").toString());

        // check
        Map<String, Object> cacheChangeEmailInfo = cacheManager.getPartnerResetPassword(partnerId);
        if (MapUtils.isEmpty(cacheChangeEmailInfo)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED);
        }
        String currentHash = cacheChangeEmailInfo.get("hash").toString();
        if (StringUtils.isNotBlank(currentHash) && !currentHash.equals(hash)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED);
        }
        if (!PasswordUtil.checkRules(resetPasswordDTO.getPw())) {
            throw new SsoException("Password must be 8-30 characters with both numbers and letters.");
        }

        // 更新密码
        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getById(partnerId);
        partnerEntity.setSalt(PasswordUtil.generateSalt(20));
        partnerEntity.setPassword(PasswordUtil.encryptPassword(resetPasswordDTO.getPw(), partnerEntity.getSalt()));
        UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), partnerEntity);
        appUserPartnerDAO.updateById(partnerEntity);

        log.info("partner:{} confirm reset password, password:{}", partnerId, resetPasswordDTO.getPw());
        return BaseCodeEnum.OK.getMsg();
    }

    @Override
    public String resetPasswordStatus(String email) {
        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getByEmail(email);
        if (Objects.isNull(partnerEntity)) {
            throw new SsoException("The account doesn't exist.");
        }
        Map<String, Object> cacheResetPasswordInfo = cacheManager.getPartnerResetPassword(partnerEntity.getId());
        if (MapUtils.isEmpty(cacheResetPasswordInfo)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED);
        }
        if (partnerEntity.getPassword().equals(cacheResetPasswordInfo.get("oldPwEncrypt"))) {
            throw new SsoException(BizCodeEnum.EMAIL_USER_WILL_CHANGE);
        }

        return "Reset password success.";
    }

    @Override
    public String changeEmail(ChangeEmailDTO changeEmailDTO, int flag) {
        String newEmail = changeEmailDTO.getNewEmail();
        String oldEmail = changeEmailDTO.getOldEmail();

        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        AppUserPartnerEntity currentUserPartner = appUserPartnerDAO.getByUserId(loginInfo.getId());
        if (Objects.isNull(currentUserPartner)) {
            throw new SsoException("The partner account doesn't exist.");
        }
        if (!currentUserPartner.getEmail().equals(oldEmail)) {
            throw new SsoException("Your old email isn't match.");
        }

        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getByEmail(oldEmail);
        if (Objects.isNull(partnerEntity)) {
            throw new SsoException("The partner account doesn't exist.");
        }

        if (RequestSendFlag.FIRST_SEND == flag) {
            AppUserEntity newEmailUser = appUserDAO.getByEmail(newEmail);
            if (Objects.nonNull(newEmailUser)) {
                throw new SsoException("Email already exists.");
            }
            AppUserPartnerEntity newEmailPartner = appUserPartnerDAO.getByEmail(newEmail);
            if (Objects.nonNull(newEmailPartner)) {
                throw new SsoException("Email already exists.");
            }
        }

        // 发送邮件
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(partnerEntity.getUserId());
        Map<String, String> emailVariable = emailProducer.partnerChangeEmailSendNew(newEmail, partnerEntity, appUserInfo);
        emailProducer.partnerChangeEmailSendOld(oldEmail, partnerEntity, appUserInfo);

        // save cache
        String hash = emailVariable.get("userHash");
        cacheManager.cachePartnerChangeEmail(partnerEntity.getId(), changeEmailDTO, hash, flag);

        log.info("partner:{} will edit email, oldEmail:{}, newEmail:{}, flag:{}", partnerEntity.getId(), oldEmail, newEmail, flag);
        return BaseCodeEnum.OK.getMsg();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String changeEmailConfirm(String hash) {
        hash = HashDecodeUtil.decodeHash(hash);
        String decode = RsaUtil.decodeRsa(hash, rsaProperties.getPrivateKey());
        Map<String, Object> infoMap = JsonUtil.toObject(decode, HashMap.class);
        Long partnerId = Long.valueOf(infoMap.get("partnerId").toString());

        // check
        Map<String, Object> cacheChangeEmailInfo = cacheManager.getPartnerChangeEmail(partnerId);
        if (MapUtils.isEmpty(cacheChangeEmailInfo)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED);
        }
        String currentHash = cacheChangeEmailInfo.get("hash").toString();
        if (StringUtils.isNotBlank(currentHash) && !currentHash.equals(hash)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED);
        }

        AppUserPartnerEntity partnerEntity = appUserPartnerDAO.getById(partnerId);
        partnerEntity.setEmail((String) cacheChangeEmailInfo.get("newEmail"));
        UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), partnerEntity);
        appUserPartnerDAO.updateById(partnerEntity);
        cacheManager.deleteUserDetailCache(partnerEntity.getUserId());

        log.info("partner:{} confirm change email, email:{}", partnerId, partnerEntity.getEmail());
        return BaseCodeEnum.OK.getMsg();
    }
}

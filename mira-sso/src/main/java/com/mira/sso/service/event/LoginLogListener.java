package com.mira.sso.service.event;

import com.mira.api.sso.dto.LoginInfoDTO;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.sso.dal.dao.LoginFailureHistoryDAO;
import com.mira.sso.dal.dao.LoginHistoryDAO;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.sso.dal.entity.LoginFailureHistoryEntity;
import com.mira.sso.dal.entity.LoginHistoryEntity;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * 登录日志记录监听器
 *
 * <AUTHOR>
 */
@Component
public class LoginLogListener {
    @Resource
    private LoginHistoryDAO loginHistoryDAO;
    @Resource
    private LoginFailureHistoryDAO loginFailureHistoryDAO;

    @EventListener(LoginLogEvent.class)
    public void loginFailureLogEvent(LoginLogEvent event) {
        LoginLogDTO loginLogDTO = (LoginLogDTO) event.getSource();
        LoginInfoDTO loginInfoDTO = loginLogDTO.getLoginInfoDTO();
        AppUserEntity appUser = loginLogDTO.getAppUser();
        int success = loginLogDTO.getSuccess();

        if (0 == success) {
            LoginFailureHistoryEntity loginFailureHistory = new LoginFailureHistoryEntity();
            loginFailureHistory.setIp(ContextHolder.get(HeaderConst.IP));
            loginFailureHistory.setOs(loginInfoDTO.getOs());
            loginFailureHistory.setVersion(loginInfoDTO.getVersion());
            loginFailureHistory.setDevice(loginInfoDTO.getDevice());
            loginFailureHistory.setAppVersion(loginInfoDTO.getAppVersion());
            loginFailureHistory.setUserId(appUser.getId());
            loginFailureHistory.setChannel(loginLogDTO.getChannel());
            UpdateEntityTimeUtil.setBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), loginFailureHistory);

            CompletableFuture.runAsync(() -> loginFailureHistoryDAO.save(loginFailureHistory), ThreadPoolUtil.getPool());
        }

        if (1 == success) {
            LoginHistoryEntity loginHistory = new LoginHistoryEntity();
            loginHistory.setIp(ContextHolder.get(HeaderConst.IP));
            loginHistory.setOs(loginInfoDTO.getOs());
            loginHistory.setVersion(loginInfoDTO.getVersion());
            loginHistory.setDevice(loginInfoDTO.getDevice());
            loginHistory.setAppVersion(loginInfoDTO.getAppVersion());
            loginHistory.setUserId(appUser.getId());
            loginHistory.setChannel(loginLogDTO.getChannel());
            UpdateEntityTimeUtil.setBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), loginHistory);

            CompletableFuture.runAsync(() -> loginHistoryDAO.save(loginHistory), ThreadPoolUtil.getPool());
        }
    }
}

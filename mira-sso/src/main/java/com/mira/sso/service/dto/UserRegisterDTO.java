package com.mira.sso.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Getter
@Setter
@ApiModel("注册用户请求参数")
public class UserRegisterDTO {
    @ApiModelProperty("英文名")
    private String firstName;

    @ApiModelProperty("英文姓")
    private String lastName;

    @ApiModelProperty("邮箱")
    @Pattern(regexp = "[\\w!#$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?",
            message = "Oops! That email doesn't look right. Please enter a valid email address.")
    @Size(max = 99, message = "Your email address is too lengthy. Please use a shorter email.")
    private String email;

    @ApiModelProperty("密码")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-zA-Z])[0-9A-Za-z~!@#$%^&*():,;'=?./-]{8,30}$",
            message = "Your password must be 8-30 characters with both numbers and letters. Spice it up!")
    private String password;
}

package com.mira.sso.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Getter
@Setter
@ApiModel("修改邮箱请求参数")
public class ChangeEmailDTO {
    @ApiModelProperty("原Email")
    @NotBlank(message = "old email can not be empty.")
    private String oldEmail;

    @ApiModelProperty("新Email")
    @Pattern(regexp = "[\\w!#$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?",
            message = "That email doesn't look right. Please enter a valid email.")
    @Size(max = 99, message = "Keep your email short and sweet, please!")
    private String newEmail;
}

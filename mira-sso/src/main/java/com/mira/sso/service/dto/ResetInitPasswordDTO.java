package com.mira.sso.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Getter
@Setter
@ApiModel("初始密码参数")
public class ResetInitPasswordDTO {
    @ApiModelProperty("email")
    @NotBlank(message = "email can not be empty.")
    private String email;

    @ApiModelProperty("新密码")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-zA-Z])[0-9A-Za-z~!@#$%^&*():,;'=?./-]{8,30}$",
            message = "8-30 characters with numbers and letters. Make it fun!")
    @NotBlank(message = "Don’t forget your new password!")
    private String pwd;
}

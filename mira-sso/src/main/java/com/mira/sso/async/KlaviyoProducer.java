package com.mira.sso.async;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.mira.api.thirdparty.consts.KlaviyoMetricConst;
import com.mira.api.thirdparty.consts.KlaviyoPropertyConst;
import com.mira.api.thirdparty.dto.klaviyo.KlaviyoMetricEventDTO;
import com.mira.api.thirdparty.dto.klaviyo.KlaviyoProfileDTO;
import com.mira.api.thirdparty.provider.IKlaviyoProvider;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.sso.dal.entity.AppUserEntity;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * klaviyo 消息生产者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class KlaviyoProducer {
    @Resource
    private SysDictProperties sysDictProperties;
    @Resource
    private IKlaviyoProvider klaviyoProvider;

    private final static String IP = "ip";
    private final static String COUNTRYCODE = "countryCode";
    private final static String CONTINENTCODE = "continentCode";
    private final static String TIME_FULL_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private final static String TARGET_TIME_PATTERN = "MM/dd/yy";

    /**
     * 新用户注册
     */
    public void accountCreated(AppUserEntity appUser) {
        if (!klaviyoSwitchOpen()) {
            return;
        }
        String ip = ContextHolder.get(HeaderConst.IP);

        CompletableFuture.supplyAsync(() -> {
            Map<String, Object> properties = Maps.newHashMap();
            buildCommonProperty(ip, appUser, properties);
            properties.put(KlaviyoPropertyConst.INTERNAL_USER_ID, String.valueOf(appUser.getId()));
            properties.put(KlaviyoPropertyConst.APP_SIGN_UP_DATE, LocalDateUtil.format(appUser.getModifyTimeStr(), TIME_FULL_PATTERN, TARGET_TIME_PATTERN));

            // 初始化带Y/N的Property
            properties.put(KlaviyoPropertyConst.EMAIL_CONFIRMED, "N");
            properties.put(KlaviyoPropertyConst.ONBOARDING_COMPLETE, "N");
            properties.put(KlaviyoPropertyConst.ANALYZER_PAIRED, "N");

            // 创建 profile
            createProfileEvent(appUser, properties);
            // 如果用户信息已经存在Klaviyo，则更新
            updateProfileEvent(appUser, properties);
            // 添加 profile 进 list
            addProfileToList(appUser);
            // 更新 event metric
            createMetricEvent(appUser, KlaviyoMetricConst.APP_SIGN_UP);

            return "";
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("func:[accountCreated] Error:", ex);
            return ex.getMessage();
        });
    }

    /**
     * 用户确认注册
     */
    public void accountConfirmed(AppUserEntity appUser, long registerTimestamp) {
        if (!klaviyoSwitchOpen()) {
            return;
        }
        String ip = ContextHolder.get(HeaderConst.IP);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        CompletableFuture.supplyAsync(() -> {
            String userLocalTime = ZoneDateUtil.format(timeZone, registerTimestamp, DatePatternConst.DATE_TIME_PATTERN);

            Map<String, Object> properties = Maps.newHashMap();
            buildCommonProperty(ip, appUser, properties);
            properties.put(KlaviyoPropertyConst.EMAIL_CONFIRMED, LocalDateUtil.format(userLocalTime, TIME_FULL_PATTERN, TARGET_TIME_PATTERN));

            updateProfileEvent(appUser, properties);
            createMetricEvent(appUser, KlaviyoMetricConst.ONBOARDING_START);

            return "";
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("func:[accountConfirmed] Error:", ex);
            return ex.getMessage();
        });
    }

    /**
     * 用户登录
     */
    public void userLogin(AppUserEntity appUser, String os) {
        if (!klaviyoSwitchOpen()) {
            return;
        }
        String ip = ContextHolder.get(HeaderConst.IP);

        CompletableFuture.supplyAsync(() -> {
            Map<String, Object> properties = Maps.newHashMap();
            buildCommonProperty(ip, appUser, properties);
            properties.put(KlaviyoPropertyConst.MOBILE_PLATFORM, os);

            updateProfileEvent(appUser, properties);

            return "";
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("func:[klaviyoLogin] Error:", ex);
            return ex.getMessage();
        });
    }

    private void buildCommonProperty(String ip, AppUserEntity appUser, Map<String, Object> properties) {
        properties.put(IP, ip);
        properties.put(COUNTRYCODE, appUser.getCountryCode());
        properties.put(CONTINENTCODE, appUser.getContinentCode());
    }

    /**
     * 创建 profile
     */
    private void createProfileEvent(AppUserEntity appUser, Map<String, Object> properties) {
        KlaviyoProfileDTO.Attributes attributes = new KlaviyoProfileDTO.Attributes();
        attributes.setEmail(appUser.getEmail());
        attributes.setProperties(properties);

        KlaviyoProfileDTO.Data data = new KlaviyoProfileDTO.Data();
        data.setAttributes(attributes);

        KlaviyoProfileDTO klaviyoProfileDTO = new KlaviyoProfileDTO();
        klaviyoProfileDTO.setData(data);

        Map<String, Object> sendMap = new HashMap<>();
        sendMap.put("flag", "createProfile");
        sendMap.put("user", JsonUtil.toJson(BeanUtil.toBean(appUser, AppUserDTO.class)));
        sendMap.put("params", new Gson().toJson(klaviyoProfileDTO));

        klaviyoProvider.appEvent(sendMap);
    }

    /**
     * 更新 profile
     */
    private void updateProfileEvent(AppUserEntity appUser, Map<String, Object> properties) {
        KlaviyoProfileDTO.Attributes attributes = new KlaviyoProfileDTO.Attributes();
        attributes.setProperties(properties);

        KlaviyoProfileDTO.Data data = new KlaviyoProfileDTO.Data();
        data.setAttributes(attributes);

        KlaviyoProfileDTO klaviyoProfileDTO = new KlaviyoProfileDTO();
        klaviyoProfileDTO.setData(data);

        Map<String, Object> sendMap = new HashMap<>();
        sendMap.put("flag", "updateProfile");
        sendMap.put("user", JsonUtil.toJson(BeanUtil.toBean(appUser, AppUserDTO.class)));
        sendMap.put("params", new Gson().toJson(klaviyoProfileDTO));

        klaviyoProvider.appEvent(sendMap);
    }

    /**
     * 添加 profile 进 list
     */
    private void addProfileToList(AppUserEntity appUser) {
        Map<String, Object> sendMap = new HashMap<>();
        sendMap.put("flag", "addProfileToList");
        sendMap.put("user", JsonUtil.toJson(BeanUtil.toBean(appUser, AppUserDTO.class)));
        sendMap.put("params", new Gson().toJson(new HashMap<>()));

        klaviyoProvider.appEvent(sendMap);
    }

    /**
     * 添加 Metric 事件
     */
    private void createMetricEvent(AppUserEntity appUser, String metricName) {
        // 使用 Gson，否则$开头的变量不会转化
        String value = new Gson().toJson(new KlaviyoMetricEventDTO(appUser.getEmail(), metricName, metricName));
        Map<String, Object> metricEventMap = new HashMap<>();
        metricEventMap.put("flag", "metricEvent");
        metricEventMap.put("user", JsonUtil.toJson(BeanUtil.toBean(appUser, AppUserDTO.class)));
        metricEventMap.put("params", value);

        klaviyoProvider.appEvent(metricEventMap);
    }

    /**
     * klaviyo 开关
     *
     * @return true/false
     */
    private boolean klaviyoSwitchOpen() {
        return "1".equals(sysDictProperties.getKlaviyoSwitch());
    }
}

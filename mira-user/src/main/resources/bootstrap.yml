spring:
  application:
    name: mira-user
  profiles:
    active: dev
  mvc:
    pathmatch:
      # spring boot 2.6 以上兼容 swagger3
      matching-strategy: ant_path_matcher
  cloud:
    # 负载均衡
    loadbalancer:
      nacos:
        enabled: true
  # 链路追踪
  sleuth:
    traceId128: true
    sampler:
      probability: 0.1
feign:
  client:
    config:
      default:
        # 连接超时时间
        connectTimeout: 5000
        # 读取超时时间
        readTimeout: 6000
      mira-bluetooth:
        connectTimeout: 30000
        # 连接超时时间
        readTimeout: 60000
      mira-third-party:
        # 连接超时时间
        connectTimeout: 3000
        # 读取超时时间
        readTimeout: 3000
  # 禁用httpclient
  httpclient:
    enabled: false
  # 启用okhttp
  okhttp:
    enabled: true
  # Feign Resilience4j 集成开关
  resilience4j:
    enabled: true

server:
  port: 8081
  shutdown: graceful
  tomcat:
    threads:
      # 最大工作线程数
      max: 200
      # 最小空闲线程数
      min-spare: 20
    # 最大连接数
    max-connections: 8192
    # 等待队列长度
    accept-count: 100
    # 连接超时时间(毫秒)
    connection-timeout: 30000
    # Keep-Alive超时
    keep-alive-timeout: 60000
    # 单连接最大Keep-Alive请求数
    max-keep-alive-requests: 200
    # 处理器缓存
    processor-cache: 200

# Resilience4j 配置
resilience4j:
  # 熔断器配置
  circuitbreaker:
    configs:
      default:
        # 失败率阈值百分比
        failureRateThreshold: 50
        # 熔断器在半开状态时允许通过的请求数
        permittedNumberOfCallsInHalfOpenState: 3
        # 熔断器从开启状态到半开状态需要等待的时间（秒）
        waitDurationInOpenState: 30s
        # 滑动窗口的大小
        slidingWindowSize: 10
        # 滑动窗口的类型（COUNT_BASED：基于请求数，TIME_BASED：基于时间）
        slidingWindowType: COUNT_BASED
        # 最小请求数，只有达到这个数量才会进行熔断判断
        minimumNumberOfCalls: 5
        # 自动从半开状态转换到开启状态
        automaticTransitionFromOpenToHalfOpenEnabled: true
    instances:
      # 针对 mira-third-party 服务的熔断配置
      mira-third-party:
        baseConfig: default
        # 可以覆盖默认配置
        failureRateThreshold: 60
        waitDurationInOpenState: 20s
        # 慢调用阈值（毫秒）
        slowCallDurationThreshold: 4000
        # 慢调用率阈值百分比
        slowCallRateThreshold: 50
        # 最小请求数，降低以便更容易触发
        minimumNumberOfCalls: 3

  # 限流器配置
  ratelimiter:
    configs:
      default:
        # 限流周期内允许的请求数
        limitForPeriod: 100
        # 限流周期的时长
        limitRefreshPeriod: 1s
        # 线程等待许可的最大时间
        timeoutDuration: 500ms
    instances:
      # 针对 mira-third-party 服务的限流配置
      mira-third-party:
        baseConfig: default
        # 可以覆盖默认配置，降低 QPS 限制
        limitForPeriod: 5
        limitRefreshPeriod: 1s
        # 等待许可的超时时间，设置为0表示不等待，直接拒绝
        timeoutDuration: 0ms
package com.mira.user.config;

import feign.Feign;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.feign.FeignDecorators;
import io.github.resilience4j.feign.Resilience4jFeign;
import io.github.resilience4j.ratelimiter.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;

/**
 * Feign Resilience4j 配置
 * 为 Feign 客户端配置限流和熔断功能
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass({Feign.class, CircuitBreaker.class, RateLimiter.class})
@ConditionalOnProperty(value = "feign.resilience4j.enabled", havingValue = "true", matchIfMissing = true)
public class FeignResilience4jConfig {
    /**
     * 创建 Feign.Builder，集成 Resilience4j 的熔断器和限流器
     * 使用 @Primary 确保这个 Builder 被优先使用
     */
    @Bean
    @Primary
    @Scope("prototype")
    public Feign.Builder resilience4jFeignBuilder() {
        log.info("创建 Resilience4j Feign Builder");

        // 创建默认的装饰器，将在运行时根据具体的 Feign 客户端动态配置
        FeignDecorators decorators = FeignDecorators.builder()
                .build();

        return Resilience4jFeign.builder(decorators);
    }
}
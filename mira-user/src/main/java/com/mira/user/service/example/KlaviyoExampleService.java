package com.mira.user.service.example;

import com.mira.api.thirdparty.provider.IKlaviyoProvider;
import com.mira.core.response.CommonResult;
import com.mira.core.util.ThreadPoolUtil;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.ratelimiter.annotation.RateLimiter;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Klaviyo 服务使用示例
 * 展示如何使用配置了限流和熔断的 Feign Client
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KlaviyoExampleService {

    @Autowired
    private IKlaviyoProvider klaviyoProvider;

    /**
     * 发送应用事件到 Klaviyo
     * 该方法已经被 Resilience4j 保护，具有限流和熔断功能
     *
     * @param eventName 事件名称
     * @param properties 事件属性
     * @return 发送结果
     */
    @CircuitBreaker(name = "mira-third-party", fallbackMethod = "sendAppEventFallback")
    @RateLimiter(name = "mira-third-party")
    @Retry(name = "mira-third-party")
    public CommonResult<String> sendAppEvent(String eventName, Map<String, Object> properties) {
        try {
            // 构建请求参数
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("event", eventName);
            eventData.put("properties", properties);
            eventData.put("timestamp", System.currentTimeMillis());

            log.info("准备发送 Klaviyo 事件: {}", eventName);
            
            // 调用 Feign Client
            // 这个调用会自动应用限流和熔断策略
            CommonResult<String> result = klaviyoProvider.appEvent(eventData);
            
            if (result.getCode() == 0) {
                log.info("Klaviyo 事件发送成功: {}", result.getData());
            } else {
                log.error("Klaviyo 事件发送失败: {}", result.getMsg());
            }
            
            return result;
        } catch (Exception e) {
            // 如果触发了限流或熔断，会抛出异常
            log.error("调用 Klaviyo 服务异常", e);
            return CommonResult.FAILED("调用 Klaviyo 服务失败: " + e.getMessage());
        }
    }

    /**
     * 发送应用事件降级方法
     * 当熔断器打开或限流触发时调用
     */
    public CommonResult<String> sendAppEventFallback(String eventName, Map<String, Object> properties, Exception ex) {
        log.warn("Klaviyo 服务调用失败，触发降级处理。事件: {}, 异常: {}", eventName, ex.getMessage());
        return CommonResult.FAILED("Klaviyo 服务暂时不可用，请稍后重试");
    }

    /**
     * 批量发送事件示例
     * 用于测试限流功能
     * 注意：使用同步调用以确保限流器能正确工作
     */
    public void batchSendEvents(int count) {
        log.info("开始批量发送事件，总数: {}", count);

        for (int i = 0; i < count; i++) {
            try {
                Map<String, Object> properties = new HashMap<>();
                properties.put("index", i);
                properties.put("batchId", System.currentTimeMillis());

                CommonResult<String> result = sendAppEvent("test_event_" + i, properties);

                // 记录每次调用的结果
                if (result.getCode() == 0) {
                    log.info("第 {} 次调用成功", i + 1);
                } else {
                    log.warn("第 {} 次调用失败: {}", i + 1, result.getMsg());
                }

                // 添加小延迟，避免过快调用
                Thread.sleep(50);
            } catch (Exception e) {
                log.error("第 {} 次调用异常: {}", i + 1, e.getMessage());
            }
        }

        log.info("批量发送事件完成");
    }

    /**
     * 简单的测试方法，用于验证限流功能
     * 不使用异步，直接同步调用
     */
    @RateLimiter(name = "mira-third-party")
    public CommonResult<String> testRateLimit(String message) {
        log.info("执行限流测试调用: {}", message);

        // 直接返回成功，不调用远程服务
        return CommonResult.OK("限流测试成功: " + message);
    }
}
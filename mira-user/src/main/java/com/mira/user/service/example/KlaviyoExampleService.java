package com.mira.user.service.example;

import com.mira.api.thirdparty.provider.IKlaviyoProvider;
import com.mira.core.response.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Klaviyo 服务使用示例
 * 展示如何使用配置了限流和熔断的 Feign Client
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KlaviyoExampleService {

    @Autowired
    private IKlaviyoProvider klaviyoProvider;

    /**
     * 发送应用事件到 Klaviyo
     * 该方法已经被 Resilience4j 保护，具有限流和熔断功能
     *
     * @param eventName 事件名称
     * @param properties 事件属性
     * @return 发送结果
     */
    public CommonResult<String> sendAppEvent(String eventName, Map<String, Object> properties) {
        try {
            // 构建请求参数
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("event", eventName);
            eventData.put("properties", properties);
            eventData.put("timestamp", System.currentTimeMillis());

            log.info("准备发送 Klaviyo 事件: {}", eventName);
            
            // 调用 Feign Client
            // 这个调用会自动应用限流和熔断策略
            CommonResult<String> result = klaviyoProvider.appEvent(eventData);
            
            if (result.getCode() == 0) {
                log.info("Klaviyo 事件发送成功: {}", result.getData());
            } else {
                log.error("Klaviyo 事件发送失败: {}", result.getMsg());
            }
            
            return result;
        } catch (Exception e) {
            // 如果触发了限流或熔断，会抛出异常
            log.error("调用 Klaviyo 服务异常", e);
            return CommonResult.FAILED("调用 Klaviyo 服务失败: " + e.getMessage());
        }
    }

    /**
     * 批量发送事件示例
     * 用于测试限流功能
     */
    public void batchSendEvents(int count) {
        for (int i = 0; i < count; i++) {
            int finalI = i;
            CompletableFuture.runAsync(() -> {
                Map<String, Object> properties = new HashMap<>();
                properties.put("index", finalI);
                properties.put("batchId", System.currentTimeMillis());

                CommonResult<String> result = sendAppEvent("test_event_" + finalI, properties);

                // 记录每次调用的结果
                if (result.getCode() == 0) {
                    log.info("第 {} 次调用成功", finalI + 1);
                } else {
                    log.warn("第 {} 次调用失败: {}", finalI + 1, result.getMsg());
                }
            });
        }
    }
}
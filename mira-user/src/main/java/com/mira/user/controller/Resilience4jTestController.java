package com.mira.user.controller;

import com.mira.core.response.CommonResult;
import com.mira.user.service.example.KlaviyoExampleService;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Resilience4j 测试控制器
 * 用于测试限流和熔断功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/app/test/resilience4j")
@Api(tags = "Resilience4j测试接口")
public class Resilience4jTestController {

    @Autowired
    private KlaviyoExampleService klaviyoExampleService;

    @Autowired
    private CircuitBreakerRegistry circuitBreakerRegistry;

    @Autowired
    private RateLimiterRegistry rateLimiterRegistry;

    /**
     * 测试单次调用
     */
    @PostMapping("/klaviyo/single")
    @ApiOperation("测试单次Klaviyo调用")
    public CommonResult<String> testSingleCall(@ApiParam("事件属性") @RequestBody(required = false) Map<String, Object> properties) {
        
        if (properties == null) {
            properties = new HashMap<>();
        }
        properties.put("testTime", System.currentTimeMillis());
        
        return klaviyoExampleService.sendAppEvent("测试事件", properties);
    }

    /**
     * 测试批量调用（用于触发限流）
     */
    @PostMapping("/klaviyo/batch")
    @ApiOperation("测试批量Klaviyo调用（触发限流）")
    public CommonResult<String> testBatchCall(
            @ApiParam("调用次数") @RequestParam(defaultValue = "10") int count) {

        log.info("开始批量调用测试，次数: {}", count);

        StringBuilder result = new StringBuilder();
        result.append("批量调用测试结果:\n");

        for (int i = 0; i < count; i++) {
            try {
                Map<String, Object> properties = new HashMap<>();
                properties.put("testIndex", i);
                properties.put("testTime", System.currentTimeMillis());

                CommonResult<String> callResult = klaviyoExampleService.sendAppEvent("batch_test_" + i, properties);
                result.append(String.format("第%d次调用: %s\n", i + 1,
                    callResult.getCode() == 0 ? "成功" : "失败 - " + callResult.getMsg()));

                // 短暂延迟，避免过快调用
                Thread.sleep(100);
            } catch (Exception e) {
                result.append(String.format("第%d次调用异常: %s\n", i + 1, e.getMessage()));
            }
        }

        return CommonResult.OK(result.toString());
    }

    /**
     * 测试限流功能（快速连续调用）
     */
    @PostMapping("/klaviyo/rate-limit-test")
    @ApiOperation("测试限流功能（快速连续调用）")
    public CommonResult<String> testRateLimit(
            @ApiParam("调用次数") @RequestParam(defaultValue = "20") int count) {

        log.info("开始限流测试，快速连续调用 {} 次", count);

        StringBuilder result = new StringBuilder();
        result.append("限流测试结果:\n");

        for (int i = 0; i < count; i++) {
            try {
                long startTime = System.currentTimeMillis();

                Map<String, Object> properties = new HashMap<>();
                properties.put("testIndex", i);
                properties.put("testTime", startTime);

                CommonResult<String> callResult = klaviyoExampleService.sendAppEvent("rate_limit_test_" + i, properties);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                result.append(String.format("第%d次调用: %s (耗时: %dms)\n", i + 1,
                    callResult.getCode() == 0 ? "成功" : "失败 - " + callResult.getMsg(), duration));

                // 不添加延迟，快速连续调用以触发限流
            } catch (Exception e) {
                result.append(String.format("第%d次调用异常: %s\n", i + 1, e.getMessage()));
            }
        }

        return CommonResult.OK(result.toString());
    }

    /**
     * 简单限流测试（不调用远程服务）
     */
    @PostMapping("/klaviyo/simple-rate-limit-test")
    @ApiOperation("简单限流测试（不调用远程服务）")
    public CommonResult<String> testSimpleRateLimit(
            @ApiParam("调用次数") @RequestParam(defaultValue = "20") int count) {

        log.info("开始简单限流测试，快速连续调用 {} 次", count);

        StringBuilder result = new StringBuilder();
        result.append("简单限流测试结果:\n");

        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < count; i++) {
            try {
                long startTime = System.currentTimeMillis();

                CommonResult<String> callResult = klaviyoExampleService.testRateLimit("test_" + i);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                if (callResult.getCode() == 0) {
                    successCount++;
                    result.append(String.format("第%d次调用: 成功 (耗时: %dms)\n", i + 1, duration));
                } else {
                    failCount++;
                    result.append(String.format("第%d次调用: 失败 - %s (耗时: %dms)\n", i + 1, callResult.getMsg(), duration));
                }

            } catch (Exception e) {
                failCount++;
                result.append(String.format("第%d次调用异常: %s\n", i + 1, e.getMessage()));
            }
        }

        result.append(String.format("\n总结: 成功 %d 次, 失败 %d 次\n", successCount, failCount));

        return CommonResult.OK(result.toString());
    }

    /**
     * 获取熔断器状态
     */
    @GetMapping("/circuit-breaker/status")
    @ApiOperation("获取熔断器状态")
    public CommonResult<Map<String, Object>> getCircuitBreakerStatus(
            @ApiParam("熔断器名称") @RequestParam(defaultValue = "mira-third-party") String name) {
        
        var circuitBreaker = circuitBreakerRegistry.circuitBreaker(name);
        
        Map<String, Object> status = new HashMap<>();
        status.put("name", circuitBreaker.getName());
        status.put("state", circuitBreaker.getState());
        status.put("metrics", Map.of(
                "failureRate", circuitBreaker.getMetrics().getFailureRate(),
                "slowCallRate", circuitBreaker.getMetrics().getSlowCallRate(),
                "numberOfBufferedCalls", circuitBreaker.getMetrics().getNumberOfBufferedCalls(),
                "numberOfFailedCalls", circuitBreaker.getMetrics().getNumberOfFailedCalls(),
                "numberOfSlowCalls", circuitBreaker.getMetrics().getNumberOfSlowCalls(),
                "numberOfSuccessfulCalls", circuitBreaker.getMetrics().getNumberOfSuccessfulCalls()
        ));
        
        return CommonResult.OK(status);
    }

    /**
     * 获取限流器状态
     */
    @GetMapping("/rate-limiter/status")
    @ApiOperation("获取限流器状态")
    public CommonResult<Map<String, Object>> getRateLimiterStatus(
            @ApiParam("限流器名称") @RequestParam(defaultValue = "mira-third-party") String name) {
        
        var rateLimiter = rateLimiterRegistry.rateLimiter(name);
        
        Map<String, Object> status = new HashMap<>();
        status.put("name", rateLimiter.getName());
        status.put("metrics", Map.of(
                "availablePermissions", rateLimiter.getMetrics().getAvailablePermissions(),
                "numberOfWaitingThreads", rateLimiter.getMetrics().getNumberOfWaitingThreads()
        ));
        
        return CommonResult.OK(status);
    }

    /**
     * 重置熔断器
     */
    @PostMapping("/circuit-breaker/reset")
    @ApiOperation("重置熔断器")
    public CommonResult<String> resetCircuitBreaker(
            @ApiParam("熔断器名称") @RequestParam(defaultValue = "mira-third-party") String name) {
        
        var circuitBreaker = circuitBreakerRegistry.circuitBreaker(name);
        circuitBreaker.reset();
        
        return CommonResult.OK("熔断器已重置");
    }
}
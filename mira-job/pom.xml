<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mira</groupId>
    <artifactId>mira-job</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>
    <name>mira-job</name>
    <description>定时任务</description>

    <properties>
        <!-- maven -->
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <!-- 版本依赖 -->
        <mira.dependencies.version>1.0</mira.dependencies.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mira</groupId>
            <artifactId>common-web-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mira</groupId>
            <artifactId>common-mybatis-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mira</groupId>
            <artifactId>common-redis-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mira</groupId>
            <artifactId>mira-api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.firebase</groupId>
            <artifactId>firebase-admin</artifactId>
        </dependency>

        <!-- amplitude -->
        <dependency>
            <groupId>com.amplitude</groupId>
            <artifactId>java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.mira</groupId>
                <artifactId>mira-dependencies</artifactId>
                <version>${mira.dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>mira-job</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.12.RELEASE</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>

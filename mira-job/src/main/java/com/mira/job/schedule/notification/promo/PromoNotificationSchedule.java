package com.mira.job.schedule.notification.promo;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.enums.NotificationConditionEnum;
import com.mira.job.schedule.AbstractSchedule;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.manager.PushUserInfoCacheManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.mira.redis.cache.RedisComponent;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Promo Notification
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PromoNotificationSchedule extends AbstractSchedule {
    @Resource
    private CommonManager commonManager;
    @Resource
    private JobManager jobManager;
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private PushUserInfoCacheManager pushUserInfoCacheManager;

    /**
     * 用户当地通知时间
     */
    private final static String LOCAL_NOTIFICATION_TIME_START = "08:00:00";
    private final static String LOCAL_NOTIFICATION_TIME_END = "17:00:00";

    /**
     * 通知定义id
     */
    private final static long US_CONDITION_1_DEFINE_ID = 159L;
    private final static long US_CONDITION_2_DEFINE_ID = 160L;
    private final static long US_CONDITION_3_DEFINE_ID = 161L;
    private final static long CONDITION_1_DEFINE_ID = 173L;
    private final static long CONDITION_2_DEFINE_ID = 174L;
    private final static long CONDITION_3_DEFINE_ID = 175L;

    @XxlJob("promoNotificationHandler")
    public void promoNotificationHandler() {
        log.info("promoNotificationHandler: start execute");
        run();
        log.info("promoNotificationHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        List<String> cacheIndex = pushUserInfoCacheManager.getCacheIndex();
        for (String index : cacheIndex) {
            ArrayList<PushUserInfoDTO> pushUserInfoCache = pushUserInfoCacheManager.getPushUserInfoCache(index);
            if (CollectionUtils.isNotEmpty(pushUserInfoCache)) {
                push(pushUserInfoCache);
            }
        }
    }

    private void push(List<PushUserInfoDTO> userInfoList) {
        // 推送Map，value:user_id list
        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();

        for (PushUserInfoDTO userInfo : userInfoList) {
            Long userId = userInfo.getUserId();
            String timeZone = userInfo.getTimeZone();

            // user reminder info
            UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
            if (userReminderInfo == null) {
                continue;
            }
            // remind switch
            if (userReminderInfo.getRemindFlag() == 0) {
                continue;
            }
            // 用户当地时间
            String userLocalDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
            // 判断时间是否在设定的通知时间段内
            if (!LocalDateUtil.isBetweenTime(userLocalDate, LOCAL_NOTIFICATION_TIME_START, LOCAL_NOTIFICATION_TIME_END)) {
                continue;
            }
            // 周期信息、Hormone信息
            List<CycleDataDTO> cycleDataDTOS;
            List<HormoneDTO> hormoneDTOS;
            try {
                cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
                hormoneDTOS = JsonUtil.toArray(userInfo.getHormoneData(), HormoneDTO.class);
            } catch (Exception e) {
                continue;
            }
            // After 1st week of compliant testing：
            FirebasePushDTO firebasePushDTO1 = condition1(hormoneDTOS, userInfo, userLocalDate);
            if (firebasePushDTO1 != null) {
                firebasePushMap.putIfAbsent(firebasePushDTO1, new ArrayList<>());
                firebasePushMap.get(firebasePushDTO1).add(userId);
            }
            // After 2nd completed cycle：
            FirebasePushDTO firebasePushDTO2 = condition2(cycleDataDTOS, hormoneDTOS, userInfo, userLocalDate);
            if (firebasePushDTO2 != null) {
                firebasePushMap.putIfAbsent(firebasePushDTO2, new ArrayList<>());
                firebasePushMap.get(firebasePushDTO2).add(userId);
            }
            // After 3rd month of using Mira
//            FirebasePushDTO firebasePushDTO3 = condition3(cycleDataDTOS, hormoneDTOS, userInfo, userLocalDate);
//            if (firebasePushDTO3 != null) {
//                firebasePushMap.putIfAbsent(firebasePushDTO3, new ArrayList<>());
//                firebasePushMap.get(firebasePushDTO3).add(userId);
//            }
        }

        // push
        long[] pushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        log.info("PromoNotificationSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        commonManager.firebasePush(firebasePushMap, convertToPushUserInfoDTO(userInfoList), null);
    }

    /**
     * After 1st week of compliant testing：<br/>
     * 7 days from the first test have passed and we have at least 3 data points (test results)
     */
    private FirebasePushDTO condition1(List<HormoneDTO> hormoneDTOS, PushUserInfoDTO pushUserInfoDTO, String userLocalDate) {
        // check
        if (CollectionUtils.isEmpty(hormoneDTOS)) {
            return null;
        }

        Long userId = pushUserInfoDTO.getUserId();
        String timeZone = pushUserInfoDTO.getTimeZone();

        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.FIREBASE_SCHEDULE_CONDITION_1, userLocalDate)) {
            return null;
        }
        // 条件天数
        int days = 7;
        // 距离首次测试已过了7天，第8天如果满足条件就推送
        if (LocalDateUtil.minusToDay(userLocalDate, hormoneDTOS.get(0).getTest_time()) != days) {
            return null;
        }
        // 7天里有3条以上的有效测试数据
        boolean isMatch = false;
        int validTestCount = 0;
        for (HormoneDTO hormoneDTO : hormoneDTOS) {
            if (hormoneDTO.getFlag() == 1 && LocalDateUtil.minusToDay(userLocalDate, hormoneDTO.getTest_time()) > 0) {
                if (++validTestCount >= 3) {
                    isMatch = true;
                    break;
                }
            }
        }

        if (isMatch) {
            long defineId = timeZone.contains("America") ? US_CONDITION_1_DEFINE_ID : CONDITION_1_DEFINE_ID;
            return buildPushDTO(userId, userLocalDate, defineId, NotificationConditionEnum.FIREBASE_SCHEDULE_CONDITION_1);
        }

        return null;
    }

    /**
     * After 2nd completed cycle：<br/>
     * Have at least 15 data points (test results)
     */
    private FirebasePushDTO condition2(List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDTOS,
                                       PushUserInfoDTO pushUserInfoDTO, String userLocalDate) {
        // check
        if (CollectionUtils.isEmpty(cycleDataDTOS) || CollectionUtils.isEmpty(hormoneDTOS)) {
            return null;
        }

        Long userId = pushUserInfoDTO.getUserId();
        String timeZone = pushUserInfoDTO.getTimeZone();

        // redis key
        String redisKey = "promo:notification:condition2:".concat(String.valueOf(userId));
        // 已经推送过的，不再推送
        if (redisComponent.exists(redisKey)) {
            return null;
        }

        // 实周期数
        int actualCycle = 3;
        // 取第actualCycle个实周期信息
        long count = cycleDataDTOS.stream().filter(cycle -> CycleStatusEnum.REAL_CYCLE.getStatus() == cycle.getCycle_status()).count();
        if (count < actualCycle) {
            return null;
        }
        CycleDataDTO currentRealCycle = cycleDataDTOS.get(actualCycle - 1);
        if (currentRealCycle.getDate_period_start() == null || currentRealCycle.getDate_period_end() == null) {
            return null;
        }
        // 当天必须在新开始实周期的经期内
        if (!LocalDateUtil.isBetweenDateAndEqual(userLocalDate, currentRealCycle.getDate_period_start(), currentRealCycle.getDate_period_end())) {
            return null;
        }
        // 有15条以上的有效测试数据
        String lastRealCycleEndDate = LocalDateUtil.plusDay(currentRealCycle.getDate_period_start(), -1, DatePatternConst.DATE_PATTERN);
        boolean isMatch = false;
        int validTestCount = 0;
        for (HormoneDTO hormoneDTO : hormoneDTOS) {
            if (hormoneDTO.getFlag() == 1 && LocalDateUtil.minusToDay(lastRealCycleEndDate, hormoneDTO.getTest_time()) >= 0) {
                if (++validTestCount >= 15) {
                    isMatch = true;
                    break;
                }
            }
        }

        if (isMatch) {
            // 标记已推送
            redisComponent.set(redisKey, "1");
            // return
            long defineId = timeZone.contains("America") ? US_CONDITION_2_DEFINE_ID : CONDITION_2_DEFINE_ID;
            return buildPushDTO(userId, userLocalDate, defineId, NotificationConditionEnum.FIREBASE_SCHEDULE_CONDITION_2);
        }

        return null;
    }

    /**
     * After 3rd month of using Mira：<br/>
     * Screen for all 8 conditions> if a user meets at least 2 conditions from the list> trigger a telehealth notification<br/>
     * 1. The cycle length is shorter than 21 days.<br/>
     * 2. The cycle length is longer than 35 days.<br/>
     * 3. The period is longer than 8 days.<br/>
     * 4. No LH peak is detected during 42 days (missed cycle).<br/>
     * 5. No rise of PdG for 3 continuous days after LH peak detected.<br/>
     * 6. FSH higher than 10 mIU/ml on cycle days 2-4 before age 40.<br/>
     * 7. No E3G surge higher 100 ng/ml during 42 days (missed cycle).<br/>
     * 8. Luteal phase length shorter than 10 days.
     */
    private FirebasePushDTO condition3(List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDTOS,
                                       PushUserInfoDTO pushUserInfoDTO, String userLocalDate) {
        // check
        if (CollectionUtils.isEmpty(cycleDataDTOS) || CollectionUtils.isEmpty(hormoneDTOS)) {
            return null;
        }

        Long userId = pushUserInfoDTO.getUserId();
        String timeZone = pushUserInfoDTO.getTimeZone();

        // redis key
        String redisKey = "promo:notification:condition3:".concat(String.valueOf(userId));
        // 已经推送过的，不再推送
        if (redisComponent.exists(redisKey)) {
            return null;
        }
        // 首次测试3个月后
        int days = 90;
        if (LocalDateUtil.minusToDay(userLocalDate, hormoneDTOS.get(0).getTest_time()) <= days) {
            return null;
        }
        // 最近的一个实周期（不能是当前周期）
        int recentRealCycleIndex = getRecentRealCycleIndex(cycleDataDTOS, false, userLocalDate);
        if (recentRealCycleIndex == -1) {
            return null;
        }
        // 最近的一个实周期（包含当前周期）
        int recentRealCycleIndexContainCurr = getRecentRealCycleIndex(cycleDataDTOS, true, userLocalDate);
        if (recentRealCycleIndexContainCurr == -1) {
            return null;
        }
        CycleDataDTO recentRealCycle = cycleDataDTOS.get(recentRealCycleIndex);
        CycleDataDTO recentRealCycleContainCurr = cycleDataDTOS.get(recentRealCycleIndexContainCurr);
        // 当前周期
        CycleDataDTO currentCycleData = getCurrentCycleData(userLocalDate, cycleDataDTOS);
        if (currentCycleData == null || recentRealCycle.getDate_period_end() == null || recentRealCycle.getDate_period_start() == null) {
            return null;
        }

        // ------------------------------ 满足两个及以上条件 ------------------------------
        int matchCount = 0;
        // sub condition 1、2
        if (SubConditionHandler.subCondition1_2(recentRealCycle)) {
            matchCount++;
        }
        // sub condition 3
        if (matchCount < 2 && SubConditionHandler.subCondition3(recentRealCycle)) {
            matchCount++;
        }
        // sub condition 4
        if (matchCount < 2 && SubConditionHandler.subCondition4(cycleDataDTOS, recentRealCycleIndex, recentRealCycle)) {
            matchCount++;
        }
        // sub condition 5
        if (matchCount < 2 && SubConditionHandler.subCondition5(cycleDataDTOS, recentRealCycleIndex)) {
            matchCount++;
        }
        // sub condition 6
        if (matchCount < 2 && SubConditionHandler.subCondition6(cycleDataDTOS, hormoneDTOS, recentRealCycleIndexContainCurr, pushUserInfoDTO)) {
            matchCount++;
        }
        // sub condition 7
        if (matchCount < 2 && SubConditionHandler.subCondition7(hormoneDTOS, recentRealCycle, userLocalDate)) {
            matchCount++;
        }
        // sub condition 8
        if (matchCount < 2 && SubConditionHandler.subCondition8(cycleDataDTOS, recentRealCycleContainCurr, recentRealCycleIndexContainCurr)) {
            matchCount++;
        }

        // check
        if (matchCount >= 2) {
            // 标记已推送
            redisComponent.set(redisKey, "1");
            // return
            long defineId = timeZone.contains("America") ? US_CONDITION_3_DEFINE_ID : CONDITION_3_DEFINE_ID;
            return buildPushDTO(userId, userLocalDate, defineId, NotificationConditionEnum.FIREBASE_SCHEDULE_CONDITION_3);
        }

        return null;
    }

    /**
     * 获取最近实周期的索引
     *
     * @param cycleDataDTOS     周期数据
     * @param allowCurrentCycle 是否允许是当前周期
     * @return int
     */
    private static int getRecentRealCycleIndex(List<CycleDataDTO> cycleDataDTOS, boolean allowCurrentCycle, String nowDate) {
        int size = cycleDataDTOS.size();
        for (int i = size - 1; i > -1; i--) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(i);
            if (CycleStatusEnum.REAL_CYCLE.getStatus() == cycleDataDTO.getCycle_status()) {
                if (allowCurrentCycle) {
                    return i;
                } else {
                    String cycleEndDate = LocalDateUtil.plusDay(cycleDataDTO.getDate_period_start(), cycleDataDTO.getLen_cycle() - 1, DatePatternConst.DATE_PATTERN);
                    if (LocalDateUtil.minusToDay(cycleEndDate, nowDate) >= 0) {
                        continue;
                    }
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * 获取当前周期信息
     *
     * @param nowDate       当天日期
     * @param cycleDataDTOS 周期数据
     * @return CycleDataDTO
     */
    public static CycleDataDTO getCurrentCycleData(String nowDate, List<CycleDataDTO> cycleDataDTOS) {
        int size = cycleDataDTOS.size();
        for (int i = size - 1; i > -1; i--) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(i);
            if (LocalDateUtil.minusToDay(nowDate, cycleDataDTO.getDate_period_start()) >= 0) {
                return cycleDataDTOS.get(i);
            }
        }
        return null;
    }

    private FirebasePushDTO buildPushDTO(Long userId, String userLocalDate, long defineId,
                                         NotificationConditionEnum notificationConditionEnum) {
        // 构建推送内容
        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(commonManager.getNotificationDefine(defineId), false);
        // 当天已推送标记
        jobManager.markUserCurrentDayPush(userId, notificationConditionEnum, userLocalDate);
        return firebasePushDTO;
    }
}

package com.mira.job.schedule.notification.promo;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.AgeUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 推送条件
 *
 * <AUTHOR>
 */
public class SubConditionHandler {
    /**
     * 周期长度少于21天、周期长度超过35天
     *
     * @param recentRealCycle 最近实周期
     * @return true/false
     */
    public static boolean subCondition1_2(CycleDataDTO recentRealCycle) {
        return recentRealCycle.getLen_cycle() < 21 || recentRealCycle.getLen_cycle() > 35;
    }

    /**
     * 经期超过8天
     *
     * @param recentRealCycle 最近实周期
     * @return true/false
     */
    public static boolean subCondition3(CycleDataDTO recentRealCycle) {
        int periodLen = LocalDateUtil
                .minusToDay(recentRealCycle.getDate_period_end(), recentRealCycle.getDate_period_start()) + 1;
        return periodLen > 8;
    }

    /**
     * 从最近的一个实际经期开始，在42天内没有监测到LH峰值（错过周期）
     *
     * @param cycleDataDTOS        周期列表
     * @param recentRealCycleIndex 最近实周期索引
     * @param recentRealCycle      最近实周期
     * @return true/false
     */
    public static boolean subCondition4(List<CycleDataDTO> cycleDataDTOS, int recentRealCycleIndex, CycleDataDTO recentRealCycle) {
        boolean result = true;
        for (int index = cycleDataDTOS.size() - 1; index >= recentRealCycleIndex; index--) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(index);
            if (StringUtils.isNotBlank(cycleDataDTO.getDate_LH_surge())
                    && LocalDateUtil.minusToDay(cycleDataDTO.getDate_LH_surge(), recentRealCycle.getDate_period_start()) < 42) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 在检测到LH峰值后的连续3天内，PdG没有上升
     *
     * @param cycleDataDTOS        周期列表
     * @param recentRealCycleIndex 最近实周期索引
     * @return true/false
     */
    public static boolean subCondition5(List<CycleDataDTO> cycleDataDTOS, int recentRealCycleIndex) {
        boolean result = false;
        for (int index = cycleDataDTOS.size() - 1; index >= recentRealCycleIndex; index--) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(index);
            if (cycleDataDTO.getValue_LH_surge() != null) {
                if (CollectionUtils.isEmpty(cycleDataDTO.getDate_PDG_rise())) {
                    return true;
                }
                long dayCount = cycleDataDTO.getDate_PDG_rise().stream()
                        .filter(pdgRiseDate -> LocalDateUtil.minusToDay(pdgRiseDate, cycleDataDTO.getDate_LH_surge()) <= 3)
                        .count();
                if (dayCount == 0) {
                    result = true;
                }
            }
        }
        return result;
    }

    /**
     * 在40岁之前，周期第2-4天FSH高于10mlU/ml。
     *
     * @param cycleDataDTOS        周期列表
     * @param hormoneDTOS          测试数据
     * @param recentRealCycleIndex 最近实周期索引
     * @param appUserInfo          用户详情
     * @return true/false
     */
    public static boolean subCondition6(List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDTOS,
                                        int recentRealCycleIndex, PushUserInfoDTO appUserInfo) {
        // 在40岁之前，周期第2-4天FSH高于10mlU/ml。
        Integer age = AgeUtil.calculateAge(appUserInfo.getBirthYear(), appUserInfo.getBirthMonth(), appUserInfo.getBirthOfDay());
        if (age == null || age >= 40) {
            return false;
        }

        for (int index = cycleDataDTOS.size() - 1; index >= recentRealCycleIndex; index--) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(index);
            if (cycleDataDTO.getDate_period_end() == null || cycleDataDTO.getDate_period_start() == null) {
                continue;
            }

            for (int plusDay = 1; plusDay <= 3; plusDay++) {
                String date = LocalDateUtil.plusDay(cycleDataDTO.getDate_period_start(), plusDay, DatePatternConst.DATE_PATTERN);
                List<HormoneDTO> matchDateFSHList = hormoneDTOS.stream()
                        .filter(hormone -> LocalDateUtil.minusToDay(date, hormone.getTest_time()) == 0
                                && Objects.equals(WandTypeEnum.FSH.getInteger(), hormone.getTest_results().getWand_type()))
                        .collect(Collectors.toList());
                long count = matchDateFSHList.stream()
                        .filter(hormone -> hormone.getTest_results().getValue1() > 10F)
                        .count();
                if (count > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 在42天内未检测到E3G激增超过100ng/ml（错过周期）
     *
     * @param hormoneDTOS     测试数据
     * @param recentRealCycle 最近实周期索引
     * @param userLocalDate   用户当地时间
     * @return true/false
     */
    public static boolean subCondition7(List<HormoneDTO> hormoneDTOS, CycleDataDTO recentRealCycle, String userLocalDate) {
        if (LocalDateUtil.minusToDay(userLocalDate, recentRealCycle.getDate_period_start()) < 42) {
            return false;
        }

        long count = hormoneDTOS.stream()
                .filter(hormone -> {
                    int days = LocalDateUtil.minusToDay(hormone.getTest_time(), recentRealCycle.getDate_period_start());
                    return (days >= 0 && days < 42);
                })
                .filter(hormone -> {
                    if (Objects.equals(WandTypeEnum.E3G_LH.getInteger(), hormone.getTest_results().getWand_type())
                            && hormone.getTest_results().getValue1() > 100F) {
                        return true;
                    }
                    return Objects.equals(WandTypeEnum.LH_E3G_PDG.getInteger(), hormone.getTest_results().getWand_type())
                            && hormone.getTest_results().getValue3() > 100F;
                })
                .count();

        return count <= 0;
    }

    /**
     * 黄体期长度少于10天
     *
     * @param cycleDataDTOS        周期列表
     * @param recentRealCycle      最近实周期
     * @param recentRealCycleIndex 最近实周期索引
     * @return true/false
     */
    public static boolean subCondition8(List<CycleDataDTO> cycleDataDTOS, CycleDataDTO recentRealCycle, int recentRealCycleIndex) {
        // 目前为止只有一个实周期，计算不了黄体期（黄体期：排卵日到经期开始）
        if (recentRealCycleIndex == 0) {
            return false;
        }

        CycleDataDTO lastRealCycle = cycleDataDTOS.get(recentRealCycleIndex - 1);
        if (lastRealCycle.getDate_period_end() == null
                || lastRealCycle.getDate_period_start() == null
                || lastRealCycle.getDate_ovulation() == null) {
            return false;
        }

        int lutealLength = LocalDateUtil.minusToDay(recentRealCycle.getDate_period_start(), lastRealCycle.getDate_ovulation()) - 1;
        return lutealLength < 10;
    }
}

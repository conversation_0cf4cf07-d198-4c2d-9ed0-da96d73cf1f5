package com.mira.job.schedule.notification.upselling;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.OvulationTypeEnum;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.CountryCodeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.enums.NotificationConditionEnum;
import com.mira.job.schedule.AbstractSchedule;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.manager.PushUserInfoCacheManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Upselling flow
 * <p>https://www.notion.so/mirafertility/5504d71a81744c3eb2dbdfd93c760b72?v=5bb2c9112aed4c79bb562ceec1b63b6f</p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UpsellingFlowSchedule extends AbstractSchedule {
    @Resource
    private CommonManager commonManager;
    @Resource
    private JobManager jobManager;
    @Resource
    private PushUserInfoCacheManager pushUserInfoCacheManager;

    /**
     * 用户当地通知时间
     */
    private final static String LOCAL_NOTIFICATION_TIME_START = "08:00:00";
    private final static String LOCAL_NOTIFICATION_TIME_END = "17:00:00";

    @XxlJob("upsellingFlowHandler")
    public void upsellingFlowHandler() {
        log.info("upsellingFlowHandler: start execute");
        run();
        log.info("upsellingFlowHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        List<String> cacheIndex = pushUserInfoCacheManager.getCacheIndex();
        for (String index : cacheIndex) {
            ArrayList<PushUserInfoDTO> pushUserInfoCache = pushUserInfoCacheManager.getPushUserInfoCache(index);
            if (CollectionUtils.isNotEmpty(pushUserInfoCache)) {
                push(pushUserInfoCache);
            }
        }
    }

    private void push(List<PushUserInfoDTO> userInfoList) {
        // 推送Map，value:user_id list
        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();

        for (PushUserInfoDTO userInfo : userInfoList) {
            Long userId = userInfo.getUserId();
            String timeZone = userInfo.getTimeZone();

            // user reminder info
            UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
            if (userReminderInfo == null) {
                continue;
            }
            // remind switch
            if (0 == userReminderInfo.getRemindFlag()) {
                continue;
            }
            // 用户绑定了诊所不发送
            if (StringUtils.isNotBlank(userInfo.getClinicIds())) {
                continue;
            }
            // 用户当地时间
            String userLocalDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
            // 周期信息、Hormone信息
            List<CycleDataDTO> cycleDataDTOS;
            List<HormoneDTO> hormoneDTOS;
            try {
                cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
                hormoneDTOS = JsonUtil.toArray(userInfo.getHormoneData(), HormoneDTO.class);
            } catch (Exception e) {
                continue;
            }
            CycleDataDTO curentCycleData = CycleDataUtil.getCurrentCycleData(userLocalDate, cycleDataDTOS);
            if (CycleStatusEnum.REAL_CYCLE.getStatus() != curentCycleData.getCycle_status()) {
                continue;
            }
            if (CollectionUtils.isEmpty(hormoneDTOS)) {
                continue;
            }
            if (!LocalDateUtil.isBetweenTime(userLocalDate, LOCAL_NOTIFICATION_TIME_START, LOCAL_NOTIFICATION_TIME_END)) {
                continue;
            }
            handleCondition(userInfo, userLocalDate.substring(0, 10), curentCycleData, cycleDataDTOS, hormoneDTOS, firebasePushMap);
        }

        // push
        long[] pushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        log.info("UpsellingFlowSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        commonManager.firebasePush(firebasePushMap, convertToPushUserInfoDTO(userInfoList), null);
    }

    private void handleCondition(PushUserInfoDTO userInfo, String userLocalDate,
                                 CycleDataDTO currentCycleData, List<CycleDataDTO> cycleDataDTOS,
                                 List<HormoneDTO> hormoneDTOS,
                                 Map<FirebasePushDTO, List<Long>> firebasePushMap) {
        Long userId = userInfo.getUserId();
        // Condition1
        FirebasePushDTO firebasePushDTO1 = condition1(userInfo, userLocalDate, currentCycleData, cycleDataDTOS, hormoneDTOS);
        if (firebasePushDTO1 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO1, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO1).add(userId);
        }
        // Condition2
        FirebasePushDTO firebasePushDTO2 = condition2(userInfo, userLocalDate);
        if (firebasePushDTO2 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO2, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO2).add(userId);
        }
        // Condition3
        FirebasePushDTO firebasePushDTO3 = condition3(userInfo, userLocalDate, currentCycleData);
        if (firebasePushDTO3 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO3, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO3).add(userId);
        }
        // Condition4
        FirebasePushDTO firebasePushDTO4 = condition4(userInfo, userLocalDate, currentCycleData, cycleDataDTOS);
        if (firebasePushDTO4 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO4, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO4).add(userId);
        }
        // Condition5
        FirebasePushDTO firebasePushDTO5 = condition5(userInfo, userLocalDate, currentCycleData, cycleDataDTOS, hormoneDTOS);
        if (firebasePushDTO5 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO5, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO5).add(userId);
        }
        // Condition6
        FirebasePushDTO firebasePushDTO6 = condition6(userInfo, userLocalDate, currentCycleData, cycleDataDTOS, hormoneDTOS);
        if (firebasePushDTO6 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO6, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO6).add(userId);
        }
        // Condition7
        FirebasePushDTO firebasePushDTO7 = condition7(userInfo, userLocalDate, currentCycleData, hormoneDTOS);
        if (firebasePushDTO7 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO7, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO7).add(userId);
        }
        // Condition8
        FirebasePushDTO firebasePushDTO8 = condition8(userInfo, userLocalDate, currentCycleData, hormoneDTOS);
        if (firebasePushDTO8 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO8, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO8).add(userId);
        }
    }

    /**
     * User is using the app for 2 cycles, there is no LH peak at least 2 cycles
     * Only US users
     */
    private FirebasePushDTO condition1(PushUserInfoDTO userInfo, String userLocalDate,
                                       CycleDataDTO currentCycleData,
                                       List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDTOS) {
        Long userId = userInfo.getUserId();
        if (!userInfo.getCountryCode().equals(CountryCodeEnum.US.name())) {
            return null;
        }
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION1)) {
            return null;
        }
        if (currentCycleData.getCycle_index() != 2) {
            return null;
        }

        int noLhPeakCount = 0;
        for (int i = 0; i < 2; i++) {
            CycleDataDTO cycleDataDTO = cycleDataDTOS.get(i);
            List<HormoneDTO> hormones = CycleDataUtil.hormoneBySpecialCycle(cycleDataDTO, hormoneDTOS);
            if (CollectionUtils.isEmpty(hormones)) {
                continue;
            }
            if (StringUtils.isNotBlank(cycleDataDTO.getDate_LH_surge())) {
                continue;
            }
            noLhPeakCount++;
        }
        if (noLhPeakCount < 2) {
            return null;
        }

        jobManager.markUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION1);

        return buildPushDTO(userId, userLocalDate, 377, NotificationConditionEnum.UPSELLING_FLOW_CONDITION1);
    }

    /**
     * User has logged pregnancy and birth, 2 months after due date
     * Only US users
     */
    private FirebasePushDTO condition2(PushUserInfoDTO userInfo, String userLocalDate) {
        Long userId = userInfo.getUserId();
        if (!userInfo.getCountryCode().equals(CountryCodeEnum.US.name())) {
            return null;
        }
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION2)) {
            return null;
        }
        if (Objects.isNull(userInfo.getPregnantEnd()) || userInfo.getPregnantEnd() != 1
                || Objects.isNull(userInfo.getEndReason()) || userInfo.getEndReason() != 1) {
            return null;
        }
        String deliveryDate = userInfo.getDeliveryDate();
        if (StringUtils.isBlank(deliveryDate)
                || LocalDateUtil.minusToDay(userLocalDate, deliveryDate) != 60) {
            return null;
        }

        jobManager.markUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION2);

        return buildPushDTO(userId, userLocalDate, 378, NotificationConditionEnum.UPSELLING_FLOW_CONDITION2);
    }

    /**
     * User is in app for 6 cycles and hasn’t logged pregnancy
     * Only US users
     */
    private FirebasePushDTO condition3(PushUserInfoDTO userInfo, String userLocalDate,
                                       CycleDataDTO currentCycleData) {
        Long userId = userInfo.getUserId();
        if (!userInfo.getCountryCode().equals(CountryCodeEnum.US.name())) {
            return null;
        }
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION3)) {
            return null;
        }
        if (currentCycleData.getCycle_index() != 6) {
            return null;
        }
        // logged pregnancy
        if (Objects.nonNull(userInfo.getPregnant()) && userInfo.getPregnant()) {
            return null;
        }

        jobManager.markUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION3);

        return buildPushDTO(userId, userLocalDate, 379, NotificationConditionEnum.UPSELLING_FLOW_CONDITION3);
    }

    /**
     * User is using the app for 2 cycles, No PdG rise after ovulation prediction
     * Only US users
     */
    private FirebasePushDTO condition4(PushUserInfoDTO userInfo, String userLocalDate,
                                       CycleDataDTO currentCycleData, List<CycleDataDTO> cycleDataDTOS) {
        Long userId = userInfo.getUserId();
        if (!userInfo.getCountryCode().equals(CountryCodeEnum.US.name())) {
            return null;
        }
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION4)) {
            return null;
        }
        if (currentCycleData.getCycle_index() != 2) {
            return null;
        }
        // 第1、2个实际周期没有pdg rise
        if (CollectionUtils.isNotEmpty(cycleDataDTOS.get(0).getDate_PDG_rise())
                || CollectionUtils.isNotEmpty(cycleDataDTOS.get(1).getDate_PDG_rise())) {
            return null;
        }

        jobManager.markUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION4);

        return buildPushDTO(userId, userLocalDate, 380, NotificationConditionEnum.UPSELLING_FLOW_CONDITION4);
    }

    /**
     * User is using the app for 2 cycles,  user had FSH is higher than 10 for 2 measurements in each cycle for 2 cycles
     * Only US users
     */
    private FirebasePushDTO condition5(PushUserInfoDTO userInfo, String userLocalDate,
                                       CycleDataDTO currentCycleData,
                                       List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDTOS) {
        Long userId = userInfo.getUserId();
        if (!userInfo.getCountryCode().equals(CountryCodeEnum.US.name())) {
            return null;
        }
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION5)) {
            return null;
        }
        if (currentCycleData.getCycle_index() != 2) {
            return null;
        }
        // 前2个周期有>=2次以上FSH高于10
        Predicate<List<HormoneDTO>> countPredicate = (list) -> list.stream().filter(hormone -> {
            boolean b1 = Objects.equals(WandTypeEnum.FSH.getInteger(), hormone.getTest_results().getWand_type());
            boolean b2 = false;
            if (b1) {
                b2 = hormone.getTest_results().getValue1() > 10F;
            }
            return b1 && b2;
        }).count() < 2;
        if (countPredicate.test(CycleDataUtil.hormoneBySpecialCycle(cycleDataDTOS.get(0), hormoneDTOS))
                || countPredicate.test(CycleDataUtil.hormoneBySpecialCycle(cycleDataDTOS.get(1), hormoneDTOS))) {
            return null;
        }

        jobManager.markUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION5);

        return buildPushDTO(userId, userLocalDate, 381, NotificationConditionEnum.UPSELLING_FLOW_CONDITION5);
    }

    /**
     * User is using the app for 2 cycles,  LH average levels are <1.5 IU/ml for 2 cycles
     * Only US users
     */
    private FirebasePushDTO condition6(PushUserInfoDTO userInfo, String userLocalDate,
                                       CycleDataDTO currentCycleData,
                                       List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDTOS) {
        Long userId = userInfo.getUserId();
        if (!userInfo.getCountryCode().equals(CountryCodeEnum.US.name())) {
            return null;
        }
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION6)) {
            return null;
        }
        if (currentCycleData.getCycle_index() != 2) {
            return null;
        }
        // 所有lh数据的平均值
        Float lhSum = 0F;
        int count = 0;
        List<HormoneDTO> realCycle1Hormone = CycleDataUtil.hormoneBySpecialCycle(cycleDataDTOS.get(0), hormoneDTOS)
                .stream().filter(this::containLhWandType)
                .collect(Collectors.toList());
        List<HormoneDTO> realCycle2Hormone = CycleDataUtil.hormoneBySpecialCycle(cycleDataDTOS.get(1), hormoneDTOS)
                .stream().filter(this::containLhWandType)
                .collect(Collectors.toList());
        List<HormoneDTO> realCycleHormone = new ArrayList<>();
        realCycleHormone.addAll(realCycle1Hormone);
        realCycleHormone.addAll(realCycle2Hormone);
        for (HormoneDTO hormoneDTO : realCycleHormone) {
            Integer wandType = hormoneDTO.getTest_results().getWand_type();
            if (Objects.equals(WandTypeEnum.LH.getInteger(), wandType)) {
                lhSum += hormoneDTO.getTest_results().getValue1();
                count++;
                continue;
            }
            if (Objects.equals(WandTypeEnum.E3G_LH.getInteger(), wandType)) {
                lhSum += hormoneDTO.getTest_results().getValue2();
                count++;
                continue;
            }
            if (Objects.equals(WandTypeEnum.LH_E3G_PDG.getInteger(), wandType)) {
                lhSum += hormoneDTO.getTest_results().getValue1();
                count++;
            }
        }
        float average = lhSum / count;
        if (average >= 1.5F) {
            return null;
        }

        jobManager.markUserPush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION6);

        return buildPushDTO(userId, userLocalDate, 382, NotificationConditionEnum.UPSELLING_FLOW_CONDITION6);
    }

    private boolean containLhWandType(HormoneDTO hormoneDTO) {
        boolean b1 = Objects.equals(WandTypeEnum.LH.getInteger(), hormoneDTO.getTest_results().getWand_type());
        boolean b2 = Objects.equals(WandTypeEnum.E3G_LH.getInteger(), hormoneDTO.getTest_results().getWand_type());
        boolean b3 = Objects.equals(WandTypeEnum.LH_E3G_PDG.getInteger(), hormoneDTO.getTest_results().getWand_type());
        return b1 || b2 || b3;
    }

    /**
     * Ovulation was not confirmed, user was testing >=50% tests during the cycle,
     * 3 days before predicted period
     */
    private FirebasePushDTO condition7(PushUserInfoDTO userInfo, String userLocalDate,
                                       CycleDataDTO currentCycleData, List<HormoneDTO> hormoneDTOS) {
        Integer cycleIndex = currentCycleData.getCycle_index();
        Long userId = userInfo.getUserId();
        if (jobManager.checkUserCurrentCyclePush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION7, cycleIndex)) {
            return null;
        }
        Integer ovulationType = currentCycleData.getOvulation_type();
        if (Objects.equals(OvulationTypeEnum.DETECTED_CONFIRMED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.DETECTED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.CUSTOM.getCode(), ovulationType)) {
            return null;
        }
        long testRatio = CycleDataUtil.testRatioByCurrentCycle(currentCycleData, hormoneDTOS);
        if (testRatio < 50) {
            return null;
        }
        String nextCyclePeriodDate = LocalDateUtil.plusDay(currentCycleData.getDate_period_start(), currentCycleData.getLen_cycle(),
                DatePatternConst.DATE_PATTERN);
        int minusToDay = LocalDateUtil.minusToDay(nextCyclePeriodDate, userLocalDate);
        if (minusToDay != 3) {
            return null;
        }

        jobManager.markUserCurrentCyclePush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION7, cycleIndex);

        return buildPushDTO(userId, userLocalDate, 383, NotificationConditionEnum.UPSELLING_FLOW_CONDITION7);
    }

    /**
     * Ovulation was predicted but didn’t happen, no lh peak,
     * 10 days after predicted ovulation day
     */
    private FirebasePushDTO condition8(PushUserInfoDTO userInfo, String userLocalDate,
                                       CycleDataDTO currentCycleData, List<HormoneDTO> hormoneDTOS) {
        Integer cycleIndex = currentCycleData.getCycle_index();
        Long userId = userInfo.getUserId();
        if (jobManager.checkUserCurrentCyclePush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION8, cycleIndex)) {
            return null;
        }
        List<HormoneDTO> hormoneByCurrentCycle = CycleDataUtil.hormoneByCurrentCycle(currentCycleData, hormoneDTOS);
        if (CollectionUtils.isEmpty(hormoneByCurrentCycle)) {
            return null;
        }
        Integer ovulationType = currentCycleData.getOvulation_type();
        if (!Objects.equals(OvulationTypeEnum.PREDICTED.getCode(), ovulationType)) {
            return null;
        }
        if (StringUtils.isNotBlank(currentCycleData.getDate_LH_surge())) {
            return null;
        }
        int minusToDay = LocalDateUtil.minusToDay(userLocalDate, currentCycleData.getDate_ovulation());
        if (minusToDay != 10) {
            return null;
        }

        jobManager.markUserCurrentCyclePush(userId, NotificationConditionEnum.UPSELLING_FLOW_CONDITION8, cycleIndex);

        return buildPushDTO(userId, userLocalDate, 384, NotificationConditionEnum.UPSELLING_FLOW_CONDITION8);
    }

    private FirebasePushDTO buildPushDTO(Long userId, String userLocalDate, long defineId,
                                         NotificationConditionEnum notificationConditionEnum) {
        // 构建推送内容
        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(commonManager.getNotificationDefine(defineId), false);
        // 当天已推送标记
        jobManager.markUserCurrentDayPush(userId, notificationConditionEnum, userLocalDate);
        return firebasePushDTO;
    }
}

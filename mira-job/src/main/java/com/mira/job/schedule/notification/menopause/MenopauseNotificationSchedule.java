package com.mira.job.schedule.notification.menopause;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.enums.NotificationConditionEnum;
import com.mira.job.schedule.AbstractSchedule;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.manager.PushUserInfoCacheManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-10-08
 **/
@Slf4j
@Component
public class MenopauseNotificationSchedule extends AbstractSchedule {

    @Resource
    private CommonManager commonManager;
    @Resource
    private JobManager jobManager;
    @Resource
    private PushUserInfoCacheManager pushUserInfoCacheManager;


    /**
     * 通知定义id
     */
    private final static long START_TESTING_MENOPAUSE_STAGE_DEFINE_ID = 998L;
    private final static long START_TESTING_MENOPAUSE_STAGE_REGULAR_DEFINE_ID = 999L;

    @XxlJob("menopauseNotificationHandler")
    public void menopauseNotificationHandler() {
        log.info("menopauseNotificationHandler: start execute");
        run();
        log.info("menopauseNotificationHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        List<String> cacheIndex = pushUserInfoCacheManager.getCacheIndex();
        for (String index : cacheIndex) {
            ArrayList<PushUserInfoDTO> pushUserInfoCache = pushUserInfoCacheManager.getPushUserInfoCache(index);
            if (CollectionUtils.isNotEmpty(pushUserInfoCache)) {
                push(pushUserInfoCache);
            }
        }
    }

    private void push(List<PushUserInfoDTO> userInfoList) {
        // 推送Map，value:user_id list
        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();

        for (PushUserInfoDTO userInfo : userInfoList) {
            Long userId = userInfo.getUserId();
            String timeZone = userInfo.getTimeZone();

            Integer trackingMenopause = userInfo.getTrackingMenopause();
            if (trackingMenopause == null || trackingMenopause != 1) {
                continue;
            }

            // user reminder info
            UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
            if (userReminderInfo == null) {
                continue;
            }
            // remind switch
            if (userReminderInfo.getRemindFlag() == 0) {
                continue;
            }
            // 用户当地时间
            String userLocalDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);

            // 周期信息、Hormone信息
            List<CycleDataDTO> cycleDataDTOS;
            List<HormoneDTO> hormoneDTOS;
            try {
                cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
                hormoneDTOS = JsonUtil.toArray(userInfo.getHormoneData(), HormoneDTO.class);
            } catch (Exception e) {
                continue;
            }

            FirebasePushDTO firebasePushDTO = condition(userInfo, cycleDataDTOS, hormoneDTOS, userLocalDate);
            if (firebasePushDTO != null) {
                firebasePushMap.putIfAbsent(firebasePushDTO, new ArrayList<>());
                firebasePushMap.get(firebasePushDTO).add(userId);
            }
        }

        // push
        long[] pushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        log.info("MenopauseNotificationSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        commonManager.firebasePush(firebasePushMap, convertToPushUserInfoDTO(userInfoList, false), null);
    }

    /**
     * 1. user is menopause mode
     * 2. when to start the menopause mode (how to use that) - when the mode is launched, either by onboarding or profile （取menopause进入的时间，在userinfo 表）
     * 该时间必须处于用户的当前周期
     * 3. the testing compliance is 0 (check all testing data or just current cycle?) - current cycle and only FSH testing （当前周期没有fsh 测试数据）
     * 4. 10 days before the next predicted period ? - yes （今天距离下个预测周期正好 10 天）
     */
    private FirebasePushDTO condition(PushUserInfoDTO appUserInfo, List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDTOS, String userLocalDate) {
        // check
        Integer trackingMenopause = appUserInfo.getTrackingMenopause();
        if (trackingMenopause == null || trackingMenopause != 1) {
            return null;
        }
        String trackingMenopauseDate = appUserInfo.getTrackingMenopauseDate();
        CycleDataDTO currentCycle = getCurrentCycle(cycleDataDTOS, userLocalDate);
        if (currentCycle == null) {
            return null;
        }
        //        int minusDayOfTrackingMenopauseDate = LocalDateUtil.minusToDay(trackingMenopauseDate,
        //                currentCycle.getDate_period_start());
        //
        //        if (minusDayOfTrackingMenopauseDate < 0 || minusDayOfTrackingMenopauseDate >= currentCycle.getLen_cycle()) {
        //            return null;
        //        }
        //改成 menopause 时间距离今天在 30天内
        int minusDayOfTrackingMenopauseDate = LocalDateUtil.minusToDay(userLocalDate, trackingMenopauseDate);
        if (minusDayOfTrackingMenopauseDate < 0 || minusDayOfTrackingMenopauseDate > 30) {
            return null;
        }
        if (CollectionUtils.isEmpty(hormoneDTOS)) {
            return null;
        }

        List<HormoneDTO> fshTestingDatas = hormoneDTOS
                .stream()
                .filter(hormoneDTO -> WandTypeEnum.FSH.getInteger().equals(hormoneDTO.getTest_results().getWand_type()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fshTestingDatas)) {
            return null;
        }
        String nextCycleStartDate = LocalDateUtil.plusDay(currentCycle.getDate_period_start(), currentCycle.getLen_cycle(), DatePatternConst.DATE_PATTERN);
        int minusToNextCycle = LocalDateUtil.minusToDay(nextCycleStartDate, userLocalDate);
        if (minusToNextCycle != 10) {
            return null;
        }

        Long userId = appUserInfo.getUserId();

        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_MENOPAUSE_STAGE_1, userLocalDate)) {
            return null;
        }

        Integer definedIrregularCycle = appUserInfo.getDefinedIrregularCycle();

        Long defineId = null;
        if (definedIrregularCycle == null || definedIrregularCycle == 0) {//是否被定义为不规则周期;null未判断;0否;1是
            defineId = START_TESTING_MENOPAUSE_STAGE_REGULAR_DEFINE_ID;
        } else {
            defineId = START_TESTING_MENOPAUSE_STAGE_DEFINE_ID;
        }

        return buildPushDTO(userId, userLocalDate, defineId, NotificationConditionEnum.TESTING_MENOPAUSE_STAGE_1);

    }


    /**
     * 取包含当天日期在内的周期信息
     *
     * @param cycleDataList 周期数据
     * @param userLocalTime 用户当地时间
     * @return CycleDataDTO
     */
    public static CycleDataDTO getCurrentCycle(List<CycleDataDTO> cycleDataList, String userLocalTime) {
        if (cycleDataList.isEmpty()) {
            return null;
        }
        CycleDataDTO currentCycle = null;
        for (CycleDataDTO cycleDataDTO : cycleDataList) {
            int minusDay = LocalDateUtil.minusToDay(userLocalTime, cycleDataDTO.getDate_period_start());
            if (minusDay >= 0 && minusDay < cycleDataDTO.getLen_cycle()) {
                currentCycle = cycleDataDTO;
            }
        }
        return currentCycle;
    }

    private FirebasePushDTO buildPushDTO(Long userId, String userLocalDate, long defineId, NotificationConditionEnum notificationConditionEnum) {
        // 构建推送内容
        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(commonManager.getNotificationDefine(defineId), false);
        // 当天已推送标记
        jobManager.markUserCurrentDayPush(userId, notificationConditionEnum, userLocalDate);
        return firebasePushDTO;
    }
}

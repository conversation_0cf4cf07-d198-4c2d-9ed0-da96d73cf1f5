package com.mira.job.schedule.notification.test_compliance;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.enums.NotificationConditionEnum;
import com.mira.job.schedule.AbstractSchedule;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.manager.PushUserInfoCacheManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.mira.redis.cache.RedisComponent;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Testing compliance
 * <p>https://www.notion.so/mirafertility/Dev-Implement-testing-compliance-notifications-22af54305b6a43f0884d5bc725cbcbd4</p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TestingComplianceSchedule extends AbstractSchedule {
    @Resource
    private CommonManager commonManager;
    @Resource
    private JobManager jobManager;
    @Resource
    private PushUserInfoCacheManager pushUserInfoCacheManager;
    @Resource
    private RedisComponent redisComponent;

    /**
     * 用户当地通知时间
     */
    private final static String LOCAL_NOTIFICATION_TIME_START = "09:00:00";
    private final static String LOCAL_NOTIFICATION_TIME_END = "12:00:00";

    /**
     * menopause progress status key
     */
    private final static String MENOPAUSE_P_STATUS = "MENOPAUSE_P_STATUS:";

    @XxlJob("testingComplianceHandler")
    public void testingComplianceHandler() {
        log.info("testingComplianceHandler: start execute");
        run();
        log.info("testingComplianceHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        List<String> cacheIndex = pushUserInfoCacheManager.getCacheIndex();
        for (String index : cacheIndex) {
            ArrayList<PushUserInfoDTO> pushUserInfoCache = pushUserInfoCacheManager.getPushUserInfoCache(index);
            if (CollectionUtils.isNotEmpty(pushUserInfoCache)) {
                push(pushUserInfoCache);
            }
        }
    }

    private void push(List<PushUserInfoDTO> userInfoList) {
        // 推送Map，value:user_id list
        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();

        for (PushUserInfoDTO userInfo : userInfoList) {
            Long userId = userInfo.getUserId();
            String timeZone = userInfo.getTimeZone();

            // user reminder info
            UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
            if (userReminderInfo == null) {
                continue;
            }
            // remind switch
            if (0 == userReminderInfo.getRemindFlag()) {
                continue;
            }
            // 用户当地时间
            String userLocalDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
            // 周期信息、Hormone信息
            List<CycleDataDTO> cycleDataDTOS;
            List<HormoneDTO> hormoneDTOS;
            try {
                cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
                hormoneDTOS = JsonUtil.toArray(userInfo.getHormoneData(), HormoneDTO.class);
            } catch (Exception e) {
                continue;
            }
            CycleDataDTO curentCycleData = CycleDataUtil.getCurrentCycleData(userLocalDate, cycleDataDTOS);
            if (CycleStatusEnum.REAL_CYCLE.getStatus() != curentCycleData.getCycle_status()) {
                continue;
            }
            if (CollectionUtils.isEmpty(hormoneDTOS)) {
                continue;
            }
            // 必须30天内有测试过
            HormoneDTO lastHormoneDTO = hormoneDTOS.get(hormoneDTOS.size() - 1);
            int minusToDay = LocalDateUtil.minusToDay(userLocalDate, lastHormoneDTO.getTest_time());
            if (minusToDay > 30) {
                continue;
            }
            // menopause mode, 额外处理
            if (Objects.nonNull(userInfo.getTrackingMenopause()) && userInfo.getTrackingMenopause() == 1) {
                Float progressStatus = userInfo.getProgressStatus();
//                conditionByTrackingMenopause1(userId, userLocalDate, curentCycleData, firebasePushMap);
                conditionByTrackingMenopause2(userId, userLocalDate, progressStatus, curentCycleData, firebasePushMap);
                conditionByTrackingMenopause3(userId, userLocalDate, progressStatus,
                        curentCycleData, hormoneDTOS, firebasePushMap);
                // cache progress status
                if (Objects.nonNull(progressStatus)) {
                    String key = MENOPAUSE_P_STATUS + userId + ":" + curentCycleData.getCycle_index();
                    redisComponent.setEx(key, progressStatus.toString(), 90, TimeUnit.DAYS);
                }
            }
            // 判断时间是否在设定的通知时间段内
            if (!LocalDateUtil.isBetweenTime(userLocalDate, LOCAL_NOTIFICATION_TIME_START, LOCAL_NOTIFICATION_TIME_END)) {
                continue;
            }
            // 处理条件
            handleCondition(userId, userLocalDate.substring(0, 10), timeZone, curentCycleData, hormoneDTOS, firebasePushMap);
        }

        // push
        long[] pushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        log.info("TestingComplianceSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        commonManager.firebasePush(firebasePushMap, convertToPushUserInfoDTO(userInfoList), null);
    }

    private void handleCondition(Long userId, String userLocalDate, String timeZone,
                                 CycleDataDTO curentCycleData, List<HormoneDTO> hormoneDTOS,
                                 Map<FirebasePushDTO, List<Long>> firebasePushMap) {
        // Condition1
//        FirebasePushDTO firebasePushDTO1 = condition1(userId, userLocalDate, curentCycleData);
//        if (firebasePushDTO1 != null) {
//            firebasePushMap.putIfAbsent(firebasePushDTO1, new ArrayList<>());
//            firebasePushMap.get(firebasePushDTO1).add(userId);
//        }
        // Condition2
//        FirebasePushDTO firebasePushDTO2 = condition2(userId, userLocalDate, curentCycleData);
//        if (firebasePushDTO2 != null) {
//            firebasePushMap.putIfAbsent(firebasePushDTO2, new ArrayList<>());
//            firebasePushMap.get(firebasePushDTO2).add(userId);
//        }
        // Condition3
//        FirebasePushDTO firebasePushDTO3 = condition3(userId, userLocalDate, curentCycleData);
//        if (firebasePushDTO3 != null) {
//            firebasePushMap.putIfAbsent(firebasePushDTO3, new ArrayList<>());
//            firebasePushMap.get(firebasePushDTO3).add(userId);
//        }

        // 用户当天触发了经期延长操作
        if (jobManager.extendPeriodMark(userId, userLocalDate)) {
            return;
        }
        // 用户当天已经测试过了
        long todayTestCount = hormoneDTOS.stream()
                .filter(hormone -> LocalDateUtil.minusToDay(userLocalDate, hormone.getTest_time()) == 0)
                .count();
        if (todayTestCount > 0) {
            return;
        }

        // Condition4
        FirebasePushDTO firebasePushDTO4 = condition4(userId, userLocalDate, curentCycleData, hormoneDTOS);
        if (firebasePushDTO4 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO4, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO4).add(userId);
        }
        // Condition5
        FirebasePushDTO firebasePushDTO5 = condition5(userId, userLocalDate, curentCycleData, hormoneDTOS);
        if (firebasePushDTO4 == null) {
            if (firebasePushDTO5 != null) {
                firebasePushMap.putIfAbsent(firebasePushDTO5, new ArrayList<>());
                firebasePushMap.get(firebasePushDTO5).add(userId);
            }
        }
        // Condition6
//        if (firebasePushDTO5 == null) {
//            FirebasePushDTO firebasePushDTO6 = condition6(userId, userLocalDate, timeZone, curentCycleData, hormoneDTOS);
//            if (firebasePushDTO6 != null) {
//                firebasePushMap.putIfAbsent(firebasePushDTO6, new ArrayList<>());
//                firebasePushMap.get(firebasePushDTO6).add(userId);
//            }
//        }
    }

    private FirebasePushDTO buildPushDTO(Long userId, String userLocalDate, long defineId,
                                         NotificationConditionEnum notificationConditionEnum) {
        // 构建推送内容
        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(commonManager.getNotificationDefine(defineId), false);
        // 当天已推送标记
        jobManager.markUserCurrentDayPush(userId, notificationConditionEnum, userLocalDate);
        return firebasePushDTO;
    }

    private boolean nextDayHaveTest(CycleDataDTO currentCycleData, String userLocalDate) {
        Set<String> testDaySet = CycleDataUtil.getTestDaySet(currentCycleData);
        String nextDay = LocalDateUtil.plusDay(userLocalDate, 1, DatePatternConst.DATE_PATTERN);
        return testDaySet.contains(nextDay);
    }

    private boolean nextDayHaveFshTest(CycleDataDTO currentCycleData, String userLocalDate) {
        Set<String> testDaySet = new HashSet<>(currentCycleData.getTesting_day_list().getProduct16());
        String nextDay = LocalDateUtil.plusDay(userLocalDate, 1, DatePatternConst.DATE_PATTERN);
        return testDaySet.contains(nextDay);
    }

    private Set<String> getHormoneTestDate(List<HormoneDTO> hormoneDTOS) {
        return hormoneDTOS.stream()
                .map(hormone -> hormone.getTest_time().substring(0, 10))
                .collect(Collectors.toSet());
    }

    private List<String> getTestDayList(CycleDataDTO currentCycleData, String userLocalDate) {
        Set<String> testDaySet = CycleDataUtil.getTestDaySet(currentCycleData);
        testDaySet.removeIf(testDay -> testDay.compareTo(userLocalDate) >= 0);
        ArrayList<String> testDayList = new ArrayList<>(testDaySet);
        testDayList.sort(String::compareTo);

        return testDayList;
    }

    private List<String> getInARowList(List<String> testDayList, int skipCount) {
        List<String> inARowList = new ArrayList<>();
        int size = testDayList.size();
        for (int i = 1; i <= skipCount; i++) {
            inARowList.add(testDayList.get(size - i));
        }
        return inARowList;
    }

    /**
     * 1. User has a scheduled test for the next day
     * 2. Cycle number is 1
     * 3. Cycle day is: 27
     */
    private FirebasePushDTO condition1(Long userId, String userLocalDate, CycleDataDTO currentCycleData) {
        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION1, userLocalDate)) {
            return null;
        }
        if (currentCycleData.getCycle_index() != 0) {
            return null;
        }
        if (LocalDateUtil.minusToDay(userLocalDate, currentCycleData.getDate_period_start()) != 26) {
            return null;
        }
        if (!nextDayHaveTest(currentCycleData, userLocalDate)) {
            return null;
        }

        return buildPushDTO(userId, userLocalDate, 276, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION1);
    }

    /**
     * 1. User has a scheduled test for the next day
     * 2. Cycle number is 1
     * 3. Cycle days are: 13, 14
     */
    private FirebasePushDTO condition2(Long userId, String userLocalDate, CycleDataDTO currentCycleData) {
        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION2, userLocalDate)) {
            return null;
        }
        if (currentCycleData.getCycle_index() != 0) {
            return null;
        }
        int minusToDay = LocalDateUtil.minusToDay(userLocalDate, currentCycleData.getDate_period_start());
        if (minusToDay != 12 && minusToDay != 13) {
            return null;
        }
        if (!nextDayHaveTest(currentCycleData, userLocalDate)) {
            return null;
        }

        return buildPushDTO(userId, userLocalDate, 277, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION2);
    }

    /**
     * 1. User has a scheduled test for the next day
     * 2. Cycle number is 1
     * 3. Cycle day is: 8
     */
    private FirebasePushDTO condition3(Long userId, String userLocalDate, CycleDataDTO currentCycleData) {
        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION3, userLocalDate)) {
            return null;
        }
        if (currentCycleData.getCycle_index() != 0) {
            return null;
        }
        int minusToDay = LocalDateUtil.minusToDay(userLocalDate, currentCycleData.getDate_period_start());
        if (minusToDay != 7) {
            return null;
        }
        if (!nextDayHaveTest(currentCycleData, userLocalDate)) {
            return null;
        }

        return buildPushDTO(userId, userLocalDate, 281, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION3);
    }

    /**
     * 1. User skips 3 recommended tests in a row
     * Send only once per cycle
     * Check before sending on the day of sending if there really were scheduled 3 tests and if user hasn’t performed them.
     * If user skipped but then performed one test - don’t send
     * Before sending notification perform the check:
     * If the tests were recalculated do not send the notification this day but send the next if user matches the conditions again
     */
    private FirebasePushDTO condition4(Long userId, String userLocalDate,
                                       CycleDataDTO currentCycleData, List<HormoneDTO> hormoneDTOS) {
        if (currentCycleData.getCycle_index() == 0) {
            return null;
        }
        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION4, userLocalDate)) {
            return null;
        }
        // 存在互斥标记，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_MUTEX, userLocalDate)) {
            return null;
        }
        // 用户当前周期已经推送过的，不再推送
        if (jobManager.checkUserCurrentCyclePush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION4, currentCycleData.getCycle_index())) {
            return null;
        }
        // 以当天为准，是否已经连续3次跳过建议的测试（昨天必须包括）
        List<String> testDayList = getTestDayList(currentCycleData, userLocalDate);
        if (testDayList.size() < 3) {
            return null;
        }
        // 遍历推荐的测试日，往前取3次
        List<String> inARowList = getInARowList(testDayList, 3);
        Set<String> hormoneDaySet = getHormoneTestDate(hormoneDTOS);
        // 今天已经测试过了
        if (hormoneDaySet.contains(userLocalDate)) {
            return null;
        }
        // 昨天没有推荐的测试
        if (!LocalDateUtil.plusDay(userLocalDate, -1, DatePatternConst.DATE_PATTERN).equals(inARowList.get(0))) {
            return null;
        }
        // 做差集后元素没有减少，说明这几天没有测试数据
        inARowList.removeAll(hormoneDaySet);
        if (inARowList.size() != 3) {
            return null;
        }
        // 标记
        jobManager.markUserCurrentCyclePush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION4, currentCycleData.getCycle_index());
        jobManager.markUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_MUTEX, userLocalDate);

        return buildPushDTO(userId, userLocalDate, 278, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION4);
    }

    /**
     * 1. User has skipped one test
     * 2. Send notification next day
     * Limit with 3 per cycle
     * If user skipped test yesterday, but has already performed it today - don’t send. Check before sending notification
     * Before sending notification perform the check:
     * If the tests were recalculated do not send the notification this day but send the next if user matches the conditions again
     */
    private FirebasePushDTO condition5(Long userId, String userLocalDate,
                                       CycleDataDTO currentCycleData, List<HormoneDTO> hormoneDTOS) {
        if (currentCycleData.getCycle_index() == 0) {
            return null;
        }
        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION5, userLocalDate)) {
            return null;
        }
        // 存在互斥标记，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_MUTEX, userLocalDate)) {
            return null;
        }
        // 用户当前周期已经推送过的3次，不再推送
        Integer userCyclePushCount = jobManager.getUserCyclePushCount(userId,
                NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION5, currentCycleData.getCycle_index());
        if (3 <= userCyclePushCount) {
            return null;
        }
        // 以当天为准，是否没有按照推荐进行上次的推荐测试
        List<String> testDayList = getTestDayList(currentCycleData, userLocalDate);
        if (testDayList.isEmpty()) {
            return null;
        }
        // 按照日期倒序，取最近的一个推荐测试日（必须是昨天）
        String previousRecommendTestDay = testDayList.get(testDayList.size() - 1);
        if (!previousRecommendTestDay.equals(LocalDateUtil.plusDay(userLocalDate, -1, DatePatternConst.DATE_PATTERN))) {
            return null;
        }
        Set<String> hormoneDaySet = getHormoneTestDate(hormoneDTOS);
        // 今天已经测试过了
        if (hormoneDaySet.contains(userLocalDate)) {
            return null;
        }
        // 已经测试的数据中是否包含了最近的一个推荐测试日
        if (hormoneDaySet.contains(previousRecommendTestDay)) {
            return null;
        }
        // 标记
        jobManager.markUserCyclePushCount(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION5, currentCycleData.getCycle_index());
        jobManager.markUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_MUTEX, userLocalDate);

        return buildPushDTO(userId, userLocalDate, 279, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION5);
    }

    /**
     * 1. User has a scheduled test for this day
     * 2. The test is not performed until 9 AM user’s time
     * 3. Send the notification at 9.10 AM user’s time
     * Limit with 3 per cycle
     * Before sending notification perform the check:
     * If the tests were recalculated do not send the notification this day but send the next if user matches the conditions again
     */
    private FirebasePushDTO condition6(Long userId, String userLocalDate, String timeZone,
                                       CycleDataDTO currentCycleData, List<HormoneDTO> hormoneDTOS) {
        if (currentCycleData.getCycle_index() == 0) {
            return null;
        }
        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION6, userLocalDate)) {
            return null;
        }
        // 存在互斥标记，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_MUTEX, userLocalDate)) {
            return null;
        }
        // 用户当前周期已经推送过的3次，不再推送
        Integer userCyclePushCount = jobManager.getUserCyclePushCount(userId,
                NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION6, currentCycleData.getCycle_index());
        if (3 <= userCyclePushCount) {
            return null;
        }
        // 当天是否是推荐的测试日
        Set<String> testDaySet = CycleDataUtil.getTestDaySet(currentCycleData);
        testDaySet.removeIf(testDay -> testDay.compareTo(userLocalDate) != 0);
        int size = testDaySet.size();
        if (size < 1) {
            return null;
        }
        // 取出推荐的测试日
        String currentRecommendTestDay = testDaySet.stream().findFirst().get();
        // 已经测试了的日期
        Set<String> hormoneDaySet = getHormoneTestDate(hormoneDTOS);
        // 用户当地时间
        String userLocalTime = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.TIME_PATTERN);
        // 过了用户的上午9点了，用户还没开始测试
        if (hormoneDaySet.contains(currentRecommendTestDay)) {
            return null;
        }
        if (userLocalTime.substring(0, 2).compareTo("09") != 0) {
            return null;
        }
        // 标记
        jobManager.markUserCyclePushCount(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION6, currentCycleData.getCycle_index());
        jobManager.markUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_MUTEX, userLocalDate);

        return buildPushDTO(userId, userLocalDate, 280, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION6);
    }

    /**
     * 1. User is in menopause mode
     * 2. User has scheduled FSH test for tomorrow
     * Send for all the FSH tests scheduled. Send as push-notification, that opens in-app notification.
     * Send 8PM user’s time
     */
    private void conditionByTrackingMenopause1(Long userId, String userLocalDate,
                                               CycleDataDTO currentCycleData,
                                               Map<FirebasePushDTO, List<Long>> firebasePushMap) {
        if (!LocalDateUtil.isBetweenTime(userLocalDate, "20:00:00", "20:20:00")) {
            return;
        }
        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION_MENOPAUSE1, userLocalDate)) {
            return;
        }

        if (!nextDayHaveFshTest(currentCycleData, userLocalDate)) {
            return;
        }

        FirebasePushDTO firebasePushDTO = buildPushDTO(userId, userLocalDate, 305, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION_MENOPAUSE1);
        firebasePushMap.putIfAbsent(firebasePushDTO, new ArrayList<>());
        firebasePushMap.get(firebasePushDTO).add(userId);
    }

    /**
     * 1. User is in a menopause mode
     * 2. User skipped 2 tests for FSH in her testing cycle
     * Send next day after 2 tests is missed, 8AM PT
     */
    private void conditionByTrackingMenopause2(Long userId, String userLocalDate, Float progressStatus,
                                               CycleDataDTO curentCycleData, Map<FirebasePushDTO, List<Long>> firebasePushMap) {
        if (!LocalDateUtil.isBetweenTime(userLocalDate, "08:00:00", "08:20:00")) {
            return;
        }
        // 用户当天已经推送过的，不再推送
        if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION_MENOPAUSE2, userLocalDate)) {
            return;
        }
        if (Objects.isNull(progressStatus)) {
            return;
        }
        // get cache key
        String key = MENOPAUSE_P_STATUS + userId + ":" + curentCycleData.getCycle_index();
        String previousProgressStatusStr = redisComponent.get(key);
        if (StringUtils.isEmpty(previousProgressStatusStr)) {
            return;
        }
        float previousProgressStatus = Float.parseFloat(previousProgressStatusStr);
        // 当前进度比之前记录的进度低，说明错过了2天测试
        if (progressStatus < previousProgressStatus && progressStatus == 0.2f) {
            FirebasePushDTO firebasePushDTO = buildPushDTO(userId, userLocalDate, 307, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION_MENOPAUSE2);
            firebasePushMap.putIfAbsent(firebasePushDTO, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO).add(userId);
        }
    }

    /**
     * 1. User is in menopause mode
     * 2. User has passed first cycle of FSH testing for menopause
     * 3. User has scheduled FSH test the day after tomorrow
     * Send only once. Send as push-notification, that opens in-app notification.
     * Send 8PM user’s time
     */
    private void conditionByTrackingMenopause3(Long userId, String userLocalDate, Float progressStatus,
                                               CycleDataDTO currentCycleData, List<HormoneDTO> hormoneDTOS,
                                               Map<FirebasePushDTO, List<Long>> firebasePushMap) {
        if (!LocalDateUtil.isBetweenTime(userLocalDate, "20:00:00", "20:20:00")) {
            return;
        }
        // 已经推送过的，不再推送
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION_MENOPAUSE3)) {
            return;
        }
        if (Objects.isNull(progressStatus)) {
            return;
        }
        // 1 cycle of testing completed, 1 month of tracking completed
        if (0.6f != progressStatus) {
            return;
        }
        // 距离下一次fsh推荐测试日还有2天
        List<String> product16 = new ArrayList<>(currentCycleData.getTesting_day_list().getProduct16());
        if (CollectionUtils.isEmpty(product16)) {
            return;
        }
        if (LocalDateUtil.minusToDay(product16.get(0), userLocalDate) != 2) {
            return;
        }
        // 只发一次
        jobManager.markUserPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION_MENOPAUSE3);

        FirebasePushDTO firebasePushDTO = buildPushDTO(userId, userLocalDate, 308, NotificationConditionEnum.TESTING_COMPLIANCE_CONDITION_MENOPAUSE3);
        firebasePushMap.putIfAbsent(firebasePushDTO, new ArrayList<>());
        firebasePushMap.get(firebasePushDTO).add(userId);
    }
}

package com.mira.job.schedule;

import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.dal.entity.master.AppUserInfoEntity;
import com.mira.web.properties.SysDictProperties;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * schedule
 *
 * <AUTHOR>
 */
public abstract class AbstractSchedule {
    @Resource
    private SysDictProperties sysDictProperties;

    protected Map<Long, PushUserInfoDTO> convertToPushUserInfoDTO(Map<Long, AppUserInfoEntity> allUserInfoMap) {
        return convertToPushUserInfoDTO(allUserInfoMap, true);
    }

    protected Map<Long, PushUserInfoDTO> convertToPushUserInfoDTO(Map<Long, AppUserInfoEntity> allUserInfoMap, boolean checkPfizerAccount) {
        List<Long> pfizerAccounts = sysDictProperties.getPfizerAccounts();

        Map<Long, PushUserInfoDTO> pushUserInfoDTOMap = new HashMap<>();
        for (Map.Entry<Long, AppUserInfoEntity> entry : allUserInfoMap.entrySet()) {
            Long userId = entry.getKey();
            AppUserInfoEntity value = entry.getValue();

            if (checkPfizerAccount && pfizerAccounts.contains(userId)) {
                continue;
            }

            PushUserInfoDTO pushUserInfoDTO = new PushUserInfoDTO();
            pushUserInfoDTO.setUserId(userId);
            pushUserInfoDTO.setPushToken(value.getPushToken());
            pushUserInfoDTO.setPlatform(value.getPlatform());
            pushUserInfoDTO.setTimeZone(value.getTimeZone());
            pushUserInfoDTOMap.put(userId, pushUserInfoDTO);
        }
        return pushUserInfoDTOMap;
    }

    protected Map<Long, PushUserInfoDTO> convertToPushUserInfoDTO(List<PushUserInfoDTO> userInfoList) {
        return convertToPushUserInfoDTO(userInfoList, true);
    }

    protected Map<Long, PushUserInfoDTO> convertToPushUserInfoDTO(List<PushUserInfoDTO> userInfoList, boolean checkPfizerAccount) {
        List<Long> pfizerAccounts = sysDictProperties.getPfizerAccounts();

        if (checkPfizerAccount) {
            return userInfoList.stream()
                    .filter(u -> !pfizerAccounts.contains(u.getUserId()))
                    .collect(Collectors.toMap(PushUserInfoDTO::getUserId, Function.identity()));
        }
        return userInfoList.stream().collect(Collectors.toMap(PushUserInfoDTO::getUserId, Function.identity()));
    }

    protected boolean checkPfizerAccount(Long userId) {
        List<Long> pfizerAccounts = sysDictProperties.getPfizerAccounts();
        return pfizerAccounts.contains(userId);
    }
}

package com.mira.job.schedule.notification.test_compliance;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.OvulationTypeEnum;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.enums.NotificationConditionEnum;
import com.mira.job.schedule.AbstractSchedule;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.manager.PushUserInfoCacheManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * Testing compliance flow
 * <p>https://www.notion.so/mirafertility/Testing-compliance-flow-95a690bcfa08497d80b9f965294bab1c</p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TestingComplianceFlowSchedule extends AbstractSchedule {
    @Resource
    private CommonManager commonManager;
    @Resource
    private JobManager jobManager;
    @Resource
    private PushUserInfoCacheManager pushUserInfoCacheManager;

    /**
     * 用户当地通知时间
     */
    private final static String LOCAL_NOTIFICATION_TIME_START = "08:00:00";
    private final static String LOCAL_NOTIFICATION_TIME_END = "17:00:00";

    @XxlJob("testingComplianceFlowHandler")
    public void testingComplianceFlowHandler() {
        log.info("testingComplianceFlowHandler: start execute");
        run();
        log.info("testingComplianceFlowHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        List<String> cacheIndex = pushUserInfoCacheManager.getCacheIndex();
        for (String index : cacheIndex) {
            ArrayList<PushUserInfoDTO> pushUserInfoCache = pushUserInfoCacheManager.getPushUserInfoCache(index);
            if (CollectionUtils.isNotEmpty(pushUserInfoCache)) {
                push(pushUserInfoCache);
            }
        }
    }

    private void push(List<PushUserInfoDTO> userInfoList) {
        // 推送Map，value:user_id list
        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();

        for (PushUserInfoDTO userInfo : userInfoList) {
            Long userId = userInfo.getUserId();
            String timeZone = userInfo.getTimeZone();

            // user reminder info
            UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
            if (userReminderInfo == null) {
                continue;
            }
            // remind switch
            if (0 == userReminderInfo.getRemindFlag()) {
                continue;
            }
            // 用户当地时间
            String userLocalDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
            // 周期信息、Hormone信息
            List<CycleDataDTO> cycleDataDTOS;
            List<HormoneDTO> hormoneDTOS;
            try {
                cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
                hormoneDTOS = JsonUtil.toArray(userInfo.getHormoneData(), HormoneDTO.class);
            } catch (Exception e) {
                continue;
            }
            CycleDataDTO curentCycleData = CycleDataUtil.getCurrentCycleData(userLocalDate, cycleDataDTOS);
            if (CycleStatusEnum.REAL_CYCLE.getStatus() != curentCycleData.getCycle_status()) {
                continue;
            }
            // 1st cycle finished
            if (curentCycleData.getCycle_index() != 1) {
                continue;
            }
            if (CollectionUtils.isEmpty(hormoneDTOS)) {
                continue;
            }
            if (!LocalDateUtil.isBetweenTime(userLocalDate, LOCAL_NOTIFICATION_TIME_START, LOCAL_NOTIFICATION_TIME_END)) {
                continue;
            }
            // handle 1st cycle
            handle1stCycleCondition(userId, cycleDataDTOS.get(0), hormoneDTOS, firebasePushMap);
        }

        // push
        long[] pushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        log.info("TestingComplianceFlowSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        commonManager.firebasePush(firebasePushMap, convertToPushUserInfoDTO(userInfoList), null);
    }

    private void handle1stCycleCondition(Long userId,
                                         CycleDataDTO cycleDataDTO, List<HormoneDTO> hormoneDTOS,
                                         Map<FirebasePushDTO, List<Long>> firebasePushMap) {
        // condition1
        FirebasePushDTO firebasePushDTO1 = condition1(userId, cycleDataDTO, hormoneDTOS);
        if (firebasePushDTO1 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO1, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO1).add(userId);
        }
        // condition2
        FirebasePushDTO firebasePushDTO2 = condition2(userId, cycleDataDTO, hormoneDTOS);
        if (firebasePushDTO2 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO2, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO2).add(userId);
        }
        // condition3
        FirebasePushDTO firebasePushDTO3 = condition3(userId, cycleDataDTO, hormoneDTOS);
        if (firebasePushDTO3 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO3, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO3).add(userId);
        }
        // condition4
        FirebasePushDTO firebasePushDTO4 = condition4(userId, cycleDataDTO, hormoneDTOS);
        if (firebasePushDTO4 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO4, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO4).add(userId);
        }
        // condition5
        FirebasePushDTO firebasePushDTO5 = condition5(userId, cycleDataDTO, hormoneDTOS);
        if (firebasePushDTO5 != null) {
            firebasePushMap.putIfAbsent(firebasePushDTO5, new ArrayList<>());
            firebasePushMap.get(firebasePushDTO5).add(userId);
        }
    }

    /**
     * 1 cycle, user has made 85% of recommended tests -
     * user hasn’t confirmed ovulation during 1st cycle
     * define id:343
     */
    private FirebasePushDTO condition1(Long userId, CycleDataDTO cycleDataDTO,
                                       List<HormoneDTO> hormoneDTOS) {
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION1)) {
            return null;
        }

        long testRatio = CycleDataUtil.testRatioByCurrentCycle(cycleDataDTO, hormoneDTOS);
        if (testRatio < 85 || testRatio > 99) {
            return null;
        }

        Integer ovulationType = cycleDataDTO.getOvulation_type();
        if (Objects.equals(OvulationTypeEnum.PREDICTED_CONFIRMED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.DETECTED_CONFIRMED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.CUSTOM.getCode(), ovulationType)) {
            return null;
        }

        return buildPushDTO(userId, 343, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION1);
    }

    /**
     * 1 cycle, user hasn’t made 85% of recommended tests (has made 51-84%)
     * define id:344
     */
    private FirebasePushDTO condition2(Long userId, CycleDataDTO cycleDataDTO,
                                       List<HormoneDTO> hormoneDTOS) {
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION2)) {
            return null;
        }

        long testRatio = CycleDataUtil.testRatioByCurrentCycle(cycleDataDTO, hormoneDTOS);
        if (testRatio < 51 || testRatio > 84) {
            return null;
        }

        return buildPushDTO(userId, 344, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION2);
    }

    /**
     * 1 cycle, user has made 100% of recommended tests
     * define id:345
     */
    private FirebasePushDTO condition3(Long userId, CycleDataDTO cycleDataDTO,
                                       List<HormoneDTO> hormoneDTOS) {
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION3)) {
            return null;
        }

        long testRatio = CycleDataUtil.testRatioByCurrentCycle(cycleDataDTO, hormoneDTOS);
        if (testRatio < 100) {
            return null;
        }

        return buildPushDTO(userId, 345, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION3);
    }

    /**
     * 1 cycle, user has made 85% of recommended tests -
     * user has confirmed ovulation during 1st cycle
     * define id:346
     */
    private FirebasePushDTO condition4(Long userId, CycleDataDTO cycleDataDTO,
                                       List<HormoneDTO> hormoneDTOS) {
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION4)) {
            return null;
        }

        long testRatio = CycleDataUtil.testRatioByCurrentCycle(cycleDataDTO, hormoneDTOS);
        if (testRatio < 85 || testRatio > 99) {
            return null;
        }

        Integer ovulationType = cycleDataDTO.getOvulation_type();
        if (Objects.equals(OvulationTypeEnum.PREDICTED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.DETECTED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.CUSTOM.getCode(), ovulationType)) {
            return null;
        }

        return buildPushDTO(userId, 346, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION4);
    }

    /**
     * 1 cycle, user has made 50% of recommended tests
     * define id:347
     */
    private FirebasePushDTO condition5(Long userId, CycleDataDTO cycleDataDTO,
                                       List<HormoneDTO> hormoneDTOS) {
        if (jobManager.checkUserPush(userId, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION5)) {
            return null;
        }

        long testRatio = CycleDataUtil.testRatioByCurrentCycle(cycleDataDTO, hormoneDTOS);
        if (testRatio != 50) {
            return null;
        }

        return buildPushDTO(userId, 347, NotificationConditionEnum.TESTING_COMPLIANCE_FLOW_1ST_CONDITION5);
    }

    private FirebasePushDTO buildPushDTO(Long userId, long defineId,
                                         NotificationConditionEnum notificationConditionEnum) {
        // 构建推送内容
        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(commonManager.getNotificationDefine(defineId), false);
        // 已推送标记
        jobManager.markUserPush(userId, notificationConditionEnum);
        return firebasePushDTO;
    }
}

package com.mira.job.schedule.testing.push;

import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.job.consts.dto.JobNotificationDTO;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.enums.ReminderEnum;
import com.mira.job.dal.dao.master.SysNotificationTestingHistoryDAO;
import com.mira.job.dal.dao.master.SysNotificationThresholdStatisticsDAO;
import com.mira.job.dal.entity.master.AppUserInfoEntity;
import com.mira.job.dal.entity.master.SysNotificationDefineEntity;
import com.mira.job.dal.entity.master.SysNotificationTestingHistoryEntity;
import com.mira.job.dal.entity.master.SysNotificationThresholdStatisticsEntity;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 需求：sys_notification_threshold_statistics表记录的是高值复测的用户和时间点，该表的数据都需要即时firebase推送出去
 * <p>
 * Test env： 0 1,11,21,31,41,51 * * * ?
 * Product env：0 1,11,21,31,41,51 * * * ?
 * <p>
 * 推送高值测试提醒
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2021-08-05
 **/
@Slf4j
@Component
public class PushTestingAgainSchedule extends AbstractPushTesting {
    @Resource
    private SysNotificationThresholdStatisticsDAO sysNotificationThresholdStatisticsDAO;
    @Resource
    private SysNotificationTestingHistoryDAO sysNotificationTestingHistoryDAO;

    @Resource
    private JobManager jobManager;
    @Resource
    private CommonManager commonManager;

    @XxlJob("pushTestingAgainHandler")
    public void pushTestingAgainHandler() {
        log.info("pushTestingAgainHandler: start execute");
        run();
        log.info("pushTestingAgainHandler: end execute");
        log.info("------------------------------------");
    }

    public void run() {
        Long pushEndTime = System.currentTimeMillis();
        Long pushStartTime = pushEndTime - 30 * 60 * 1000;
        //在30分钟前到当前时间区间内没有推送过的，需要发推送，并记录
        List<JobNotificationDTO> jobNotificationDTOS = buildJobNotificationDTOS(pushEndTime, pushStartTime);
        push(jobNotificationDTOS, jobManager.getAllUserInfo());
    }


    /**
     * 在pushStartTime~pushEndTime时间区间内没有推送过的，需要发推送，并记录
     * <p>
     * 如果间隔是半小时，则推送最多延迟半小时
     */
    private List<JobNotificationDTO> buildJobNotificationDTOS(Long pushEndTime, Long pushStartTime) {
        List<SysNotificationThresholdStatisticsEntity> pushTestAgainList = sysNotificationThresholdStatisticsDAO
                .listBetweenPushTime(pushStartTime, pushEndTime);
        log.info("push pushTestAgainList size:{}", pushTestAgainList.size());
        List<JobNotificationDTO> jobNotificationDTOS = new ArrayList<JobNotificationDTO>();
        for (SysNotificationThresholdStatisticsEntity thresholdStatisticsEntity : pushTestAgainList) {
            Long userId = thresholdStatisticsEntity.getUserId();
            String timeZone = thresholdStatisticsEntity.getTimeZone();
            Long reminderTime = thresholdStatisticsEntity.getReminderTime();
            Long notificationDefineId = thresholdStatisticsEntity.getNotificationDefineId();
            boolean exist = sysNotificationTestingHistoryDAO.checkExist(userId, notificationDefineId, reminderTime);
            if (exist) {
                continue;
            }
            JobNotificationDTO jobNotificationDTO = new JobNotificationDTO(userId, timeZone, notificationDefineId, reminderTime);
            jobNotificationDTOS.add(jobNotificationDTO);
        }
        return jobNotificationDTOS;
    }


    private void push(List<JobNotificationDTO> jobNotificationDTOS, Map<Long, AppUserInfoEntity> allUserInfoMap) {
        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();
        List<SysNotificationTestingHistoryEntity> testingHistoryEntityList = new ArrayList<>();

        for (JobNotificationDTO jobNotificationDTO : jobNotificationDTOS) {
            // app user info
            Long userId = jobNotificationDTO.getUserId();
            AppUserInfoEntity appUserInfo = allUserInfoMap.get(userId);
            if (appUserInfo == null) {
                continue;
            }
            // notification define
            SysNotificationDefineEntity notificationDefine = commonManager.getNotificationDefine(jobNotificationDTO.getDefineId());
            if (notificationDefine == null) {
                continue;
            }
            // firebase push dto
            FirebasePushDTO firebasePushDTO = FirebaseBuildUtil
                    .buildPushNotification(notificationDefine, jobManager.userRemindOpen(userId, ReminderEnum.HIDE_CONTENT_SWITCH));
            // remind check
            // user reminder info
            UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
            if (userReminderInfo == null) {
                continue;
            }
            boolean schedulePushFlag = 1 == userReminderInfo.getTestingScheduleFlag();
            if (!schedulePushFlag) {
                continue;
            }
            boolean firebasePushFlag = 1 == userReminderInfo.getRemindFlag();
            if (firebasePushFlag) {
                firebasePushMap.putIfAbsent(firebasePushDTO, new ArrayList<>());
                firebasePushMap.get(firebasePushDTO).add(userId);
            }
            // sys_notification_testing_history
            Integer pushType = commonManager.getPushType(appUserInfo.getPlatform());
            testingHistoryEntityList.add(buildTestingHistory(jobNotificationDTO, pushType));
        }

        // save testing push history
        if (CollectionUtils.isNotEmpty(testingHistoryEntityList)) {
            sysNotificationTestingHistoryDAO.saveBatch(testingHistoryEntityList);
        }

        // push
        long[] pushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        log.info("PushTestingAgainSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        commonManager.firebasePush(firebasePushMap, buildPushUserInfoDTO(allUserInfoMap), null);
    }

    private Map<Long, PushUserInfoDTO> buildPushUserInfoDTO(Map<Long, AppUserInfoEntity> allUserInfoMap) {
        Map<Long, PushUserInfoDTO> pushUserInfoDTOMap = new HashMap<>();
        for (Map.Entry<Long, AppUserInfoEntity> entry : allUserInfoMap.entrySet()) {
            Long userId = entry.getKey();
            AppUserInfoEntity value = entry.getValue();

            PushUserInfoDTO pushUserInfoDTO = new PushUserInfoDTO();
            pushUserInfoDTO.setUserId(userId);
            pushUserInfoDTO.setPushToken(value.getPushToken());
            pushUserInfoDTO.setPlatform(value.getPlatform());
            pushUserInfoDTO.setTimeZone(value.getTimeZone());
            pushUserInfoDTOMap.put(userId, pushUserInfoDTO);
        }
        return pushUserInfoDTOMap;
    }
}

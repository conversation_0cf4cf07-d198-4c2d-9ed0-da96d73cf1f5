package com.mira.job.schedule.integration;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.OvulationTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.dal.dao.master.*;
import com.mira.job.dal.entity.master.AppUserDiarySymptomsEntity;
import com.mira.job.dal.entity.master.DataIntegrationEntity;
import com.mira.job.service.manager.PushUserInfoCacheManager;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * data integration
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataIntegrationSchedule {
    @Resource
    private AppUserDAO appUserDAO;
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private AppUserDiaryDAO appUserDiaryDAO;
    @Resource
    private AppDataUploadDAO appDataUploadDAO;
    @Resource
    private AppUserDiarySymptomsDAO appUserDiarySymptomsDAO;
    @Resource
    private DataIntegrationDAO dataIntegrationDAO;

    @Resource
    private PushUserInfoCacheManager pushUserInfoCacheManager;

    @XxlJob("dataIntegrationHandler")
    public void dataIntegrationHandler() {
        log.info("dataIntegrationHandler: start execute");
        run();
        log.info("dataIntegrationHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        List<DataIntegrationEntity> dataIntegrationEntities = new ArrayList<>();

        loggedPregnant(dataIntegrationEntities);
        noLoggedPregnant(dataIntegrationEntities);
        goalStatus(dataIntegrationEntities);
        uploadTotal(dataIntegrationEntities);
        symptomsLogged(dataIntegrationEntities);

        Long cycleMonitoredCount = 0L;
        Long ovulationPredicatedCount = 0L;
        Long ovulationConfirmedCount = 0L;
        List<String> cacheIndex = pushUserInfoCacheManager.getCacheIndex();
        for (String index : cacheIndex) {
            ArrayList<PushUserInfoDTO> pushUserInfoCache = pushUserInfoCacheManager.getPushUserInfoCache(index);
            if (CollectionUtils.isNotEmpty(pushUserInfoCache)) {
                cycleMonitoredCount += cycleMonitored(pushUserInfoCache);
                ovulationPredicatedCount += ovulationPredicated(pushUserInfoCache);
                ovulationConfirmedCount += ovulationConfirmed(pushUserInfoCache);
            }
        }
        updateCycleMonitored(cycleMonitoredCount, dataIntegrationEntities);
        updateOvulationPredicated(ovulationPredicatedCount, dataIntegrationEntities);
        updateOvulationConfirmed(ovulationConfirmedCount, dataIntegrationEntities);

        dataIntegrationDAO.updateBatchById(dataIntegrationEntities);
    }

    private void loggedPregnant(List<DataIntegrationEntity> dataIntegrationEntities) {
        Long loggedPregnantCount = appUserDAO.getLoggedPregnantCount();
        DataIntegrationEntity dataIntegrationEntity = new DataIntegrationEntity();
        dataIntegrationEntity.setId(1L);
        dataIntegrationEntity.setNums(loggedPregnantCount);
        dataIntegrationEntities.add(dataIntegrationEntity);
    }

    private void noLoggedPregnant(List<DataIntegrationEntity> dataIntegrationEntities) {
        Long noLoggedPregnantCount = appUserDiaryDAO.getNoLoggedPregnantCount();
        DataIntegrationEntity dataIntegrationEntity = new DataIntegrationEntity();
        dataIntegrationEntity.setId(2L);
        dataIntegrationEntity.setNums(noLoggedPregnantCount);
        dataIntegrationEntities.add(dataIntegrationEntity);
    }

    private void goalStatus(List<DataIntegrationEntity> dataIntegrationEntities) {
        Map<String, BigDecimal> modeSumMap = appUserInfoDAO.getModeSumMap();

        DataIntegrationEntity cc = new DataIntegrationEntity();
        cc.setId(3L);
        cc.setNums(modeSumMap.get("cc").longValue());
        dataIntegrationEntities.add(cc);

        DataIntegrationEntity tta = new DataIntegrationEntity();
        tta.setId(4L);
        tta.setNums(modeSumMap.get("tta").longValue());
        dataIntegrationEntities.add(tta);

        DataIntegrationEntity ttc = new DataIntegrationEntity();
        ttc.setId(5L);
        ttc.setNums(modeSumMap.get("ttc").longValue());
        dataIntegrationEntities.add(ttc);

        DataIntegrationEntity pregnancy = new DataIntegrationEntity();
        pregnancy.setId(6L);
        pregnancy.setNums(modeSumMap.get("pregnancy").longValue());
        dataIntegrationEntities.add(pregnancy);

        DataIntegrationEntity oft = new DataIntegrationEntity();
        oft.setId(7L);
        oft.setNums(modeSumMap.get("oft").longValue());
        dataIntegrationEntities.add(oft);

        DataIntegrationEntity menopause = new DataIntegrationEntity();
        menopause.setId(8L);
        menopause.setNums(modeSumMap.get("menopause").longValue());
        dataIntegrationEntities.add(menopause);
    }

    private void uploadTotal(List<DataIntegrationEntity> dataIntegrationEntities) {
        Long totalCount = appDataUploadDAO.getTotalCount();

        DataIntegrationEntity uploadTotal = new DataIntegrationEntity();
        uploadTotal.setId(9L);
        uploadTotal.setNums(totalCount);
        dataIntegrationEntities.add(uploadTotal);
    }

    private void symptomsLogged(List<DataIntegrationEntity> dataIntegrationEntities) {
        long total = 0L;

        int queryBatch = 10000;
        long pageSize = 0;
        long recordCount = appUserDiarySymptomsDAO.getCount();
        while (recordCount > 0) {
            String sql = "select symptoms from app_user_diary_symptoms a " +
                    "join (select id from app_user_diary_symptoms order by id asc limit " + pageSize + "," + queryBatch + ") b " +
                    "on a.id=b.id";
            List<AppUserDiarySymptomsEntity> diarySymptomsEntities = appUserDiarySymptomsDAO.getBaseMapper().listBySql(sql);
            for (AppUserDiarySymptomsEntity diarySymptomsEntity : diarySymptomsEntities) {
                String symptoms = diarySymptomsEntity.getSymptoms();
                if (StringUtils.isBlank(symptoms)) {
                    continue;
                }
                List<Map> array = JsonUtil.toArray(symptoms, Map.class);
                total += array.size();
            }

            pageSize += queryBatch;
            recordCount -= queryBatch;
        }

        DataIntegrationEntity symptomsLogged = new DataIntegrationEntity();
        symptomsLogged.setId(10L);
        symptomsLogged.setNums(total);
        dataIntegrationEntities.add(symptomsLogged);
    }

    private Long cycleMonitored(ArrayList<PushUserInfoDTO> pushUserInfoCache) {
        long total = 0L;

        for (PushUserInfoDTO pushUserInfoDTO : pushUserInfoCache) {
            String cycleData = pushUserInfoDTO.getCycleData();
            if (StringUtils.isBlank(cycleData)) {
                continue;
            }
            List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cycleData, CycleDataDTO.class);
            total += cycleDataDTOS.stream()
                    .filter(cycleDataDTO -> {
                        Integer cycleStatus = cycleDataDTO.getCycle_status();
                        boolean b1 = CycleStatusEnum.EXTRA_CYCLE.getStatus() != cycleStatus;
                        boolean b2 = CycleStatusEnum.EMPTY_CYCLE.getStatus() != cycleStatus;
                        boolean b3 = CycleStatusEnum.FORECAST_CYCLE.getStatus() != cycleStatus;
                        return b1 && b2 && b3;
                    }).count();
        }

        return total;
    }

    private Long ovulationPredicated(ArrayList<PushUserInfoDTO> pushUserInfoCache) {
        long total = 0L;

        for (PushUserInfoDTO pushUserInfoDTO : pushUserInfoCache) {
            String cycleData = pushUserInfoDTO.getCycleData();
            if (StringUtils.isBlank(cycleData)) {
                continue;
            }
            List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cycleData, CycleDataDTO.class);
            total += cycleDataDTOS.stream()
                    .filter(cycleDataDTO ->
                            OvulationTypeEnum.PREDICTED.getCode().equals(cycleDataDTO.getOvulation_type()))
                    .count();
        }

        return total;
    }

    private Long ovulationConfirmed(ArrayList<PushUserInfoDTO> pushUserInfoCache) {
        long total = 0L;

        for (PushUserInfoDTO pushUserInfoDTO : pushUserInfoCache) {
            String cycleData = pushUserInfoDTO.getCycleData();
            if (StringUtils.isBlank(cycleData)) {
                continue;
            }
            List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cycleData, CycleDataDTO.class);
            total += cycleDataDTOS.stream()
                    .filter(cycleDataDTO ->
                            OvulationTypeEnum.PREDICTED_CONFIRMED.getCode().equals(cycleDataDTO.getOvulation_type())
                                    || OvulationTypeEnum.DETECTED_CONFIRMED.getCode().equals(cycleDataDTO.getOvulation_type())
                                    || OvulationTypeEnum.CUSTOM.getCode().equals(cycleDataDTO.getOvulation_type())
                    )
                    .count();
        }

        return total;
    }

    private void updateCycleMonitored(Long cycleMonitoredCount,
                                      List<DataIntegrationEntity> dataIntegrationEntities) {
        DataIntegrationEntity symptomsLogged = new DataIntegrationEntity();
        symptomsLogged.setId(11L);
        symptomsLogged.setNums(cycleMonitoredCount);
        dataIntegrationEntities.add(symptomsLogged);
    }

    private void updateOvulationPredicated(Long ovulationPredicatedCount,
                                           List<DataIntegrationEntity> dataIntegrationEntities) {
        DataIntegrationEntity ovulationPredicated = new DataIntegrationEntity();
        ovulationPredicated.setId(12L);
        ovulationPredicated.setNums(ovulationPredicatedCount);
        dataIntegrationEntities.add(ovulationPredicated);
    }

    private void updateOvulationConfirmed(Long ovulationConfirmedCount,
                                          List<DataIntegrationEntity> dataIntegrationEntities) {
        DataIntegrationEntity ovulationConfirmed = new DataIntegrationEntity();
        ovulationConfirmed.setId(13L);
        ovulationConfirmed.setNums(ovulationConfirmedCount);
        dataIntegrationEntities.add(ovulationConfirmed);
    }
}

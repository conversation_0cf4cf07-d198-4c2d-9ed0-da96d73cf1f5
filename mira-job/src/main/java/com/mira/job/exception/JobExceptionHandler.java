package com.mira.job.exception;

import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.core.Ordered;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 定时任务异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@ResponseBody
@RestControllerAdvice
public class JobExceptionHandler implements Ordered {
    @ExceptionHandler(JobException.class)
    public CommonResult<String> jobExHandler(HttpServletRequest request, JobException ex) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        String userId = ObjectUtils.isNotEmpty(loginInfo) ? loginInfo.getId().toString() : "Anonymous";
        String url = request.getRequestURI();
        log.info("user:{}, url:{}, code:{}, msg:{}", userId, url, ex.getCode(), ex.getMsg());
        return CommonResult.FAILED(ex.getCode(), ex.getMsg());
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE + 8;
    }
}

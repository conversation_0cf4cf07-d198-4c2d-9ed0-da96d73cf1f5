package com.mira.job.exception;

import com.mira.core.response.enums.BaseCodeEnum;
import com.mira.core.response.enums.ICodeEnum;
import lombok.Getter;

/**
 * 定时任务相关异常
 *
 * <AUTHOR>
 */
@Getter
public class JobException extends RuntimeException {
    private final Integer code;
    private final String msg;

    public JobException(ICodeEnum iCodeEnum) {
        super(iCodeEnum.getMsg());
        this.code = iCodeEnum.getCode();
        this.msg = iCodeEnum.getMsg();
    }

    public JobException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public JobException(String msg) {
        super(msg);
        this.code = BaseCodeEnum.INTERNAL_SERVER_ERROR.getCode();
        this.msg = msg;
    }
}

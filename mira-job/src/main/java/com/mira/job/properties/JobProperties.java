package com.mira.job.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * job properties
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
public class JobProperties {
    /**
     * tenant host
     */
    @Value("${email.tenantHost}")
    private String tenantHost;

    /**
     * cache switch
     */
    @Value("${cache.switch:1}")
    private Integer cacheSwitch;
}

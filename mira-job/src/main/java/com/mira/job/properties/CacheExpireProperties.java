package com.mira.job.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * cache expire properties
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "cache.expire")
public class CacheExpireProperties {
    /**
     * firebase push token
     */
    private long pushToken;
}

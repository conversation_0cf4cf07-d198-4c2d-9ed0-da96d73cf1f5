package com.mira.job.controller;

import com.mira.job.schedule.notification.menopause.MenopauseNotificationSchedule;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-10-11
 **/
@Api(tags = "01.test notification")
@RestController
@RequestMapping("/test")
public class TestMenopauseController {
    @Resource
    private MenopauseNotificationSchedule menopauseNotificationSchedule;

    @ApiOperation("menopause notification")
    @GetMapping("/menopause")
    public void menopauseNotificationSchedule() {
        menopauseNotificationSchedule.menopauseNotificationHandler();
    }
}

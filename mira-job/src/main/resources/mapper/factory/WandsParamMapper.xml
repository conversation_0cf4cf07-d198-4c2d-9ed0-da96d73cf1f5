<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.job.dal.mapper.factory.WandsParamMapper">
    <select id="listDistinctUBatch" resultType="com.mira.job.dal.entity.factory.WandsParamEntity">
        SELECT a.uBatch, a.uStripType
        from (select *
              from wands_param
              where 1 = 1
                and uStampManufacture >= #{latestUStempTime}
             ) a
        GROUP BY a.uBatch, a.uStripType
    </select>

    <select id="listDistinctUBatchByMaxId" resultType="com.mira.job.dal.entity.factory.WandsParamEntity">
        SELECT a.uBatch, a.uStripType,max(a.id) as id
        from (select *
              from wands_param
              where 1 = 1
                and id >= #{maxId}
             ) a
        GROUP BY a.uBatch, a.uStripType
    </select>
</mapper>

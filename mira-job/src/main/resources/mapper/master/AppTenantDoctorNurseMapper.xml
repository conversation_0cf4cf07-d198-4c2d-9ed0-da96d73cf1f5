<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.job.dal.mapper.master.AppTenantDoctorNurseMapper">
    <select id="listNurseDTO" resultType="com.mira.api.clinic.dto.TenantDoctorNurseListDTO">
        select tdn.nurse_id, tdn.doctor_id, td.name, td.email
        from app_tenant_doctor_nurse tdn
        left join app_tenant_doctor td on td.id = tdn.nurse_id
        where tdn.doctor_id in
        <foreach item="doctorId" index="index" collection="doctorIds" open="(" separator="," close=")">
            #{doctorId}
        </foreach>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.job.dal.mapper.master.AppUserInfoMapper">
    <select id="queryUserInfo" resultType="com.mira.job.consts.dto.PushUserInfoDTO">
        select a.user_id,a.goal_status,a.conditions,a.bind_device,
               a.birth_year,a.birth_month,a.birth_of_day,a.push_token,a.platform,
               a.tracking_menopause,a.tracking_menopause_date,a.defined_irregular_cycle,
               c.current_ip,c.ip_modify_time_str,c.time_zone,c.country_code,c.continent_code,c.current_currency,
               r.cycle_data,r.hormone_data,
               m.progress_status,
               pv2.is_end as pregnantEnd,
               pv2.end_reason,
               pv2.delivery_date,
               diary.pregnant,
               if(s.user_id is not null, 1, 0) as subscription,
               if(o.user_id is not null, 1, 0) as coaching,
               GROUP_CONCAT(t.id SEPARATOR ',') as clinicIds
        from app_user_info a
        join (select id from app_user_info where deleted=0 order by id asc limit #{pageSize},#{queryBatch}) b on a.id=b.id
        join app_user c on a.user_id=c.id
        join app_user_algorithm_result r on a.user_id=r.user_id
        left join app_user_menopause_result m on a.user_id=m.user_id
        left join (
            select user_id, MAX(modify_time) AS latest_modify
            from app_pregnant_mode_info_v2
            group by user_id
        ) pv2_max ON a.user_id = pv2_max.user_id
        left join app_pregnant_mode_info_v2 pv2 on pv2.user_id = pv2_max.user_id and pv2.modify_time = pv2_max.latest_modify
        left join admin_notification_subscription s on a.user_id=s.user_id
        left join admin_notification_coaching o on a.user_id=o.user_id
        left join (
            select user_id,pregnant
            from app_user_diary
            where pregnant = 1
            group by user_id
        ) diary on a.user_id=diary.user_id
        left join app_tenant_patient pa on a.user_id=pa.user_id
        left join app_tenant t on pa.tenant_code=t.code
        where a.deleted=0
        group by a.id
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.job.dal.mapper.master.AppTenantDoctorPatientMapper">
    <select id="listDoctorPatientDTO" resultType="com.mira.api.clinic.dto.DoctorPatientDTO">
        select p.tenant_code, p.user_id,p.patient_number,tdp.patient_id, tdp.doctor_id
        from app_tenant_patient p
        INNER JOIN app_tenant_doctor_patient tdp on p.id=tdp.patient_id
        where p.user_id in
        <foreach item="userId" index="index" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and p.deleted=0
    </select>
</mapper>

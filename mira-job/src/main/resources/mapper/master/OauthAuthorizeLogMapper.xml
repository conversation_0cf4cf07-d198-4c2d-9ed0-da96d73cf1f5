<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.job.dal.mapper.master.OauthAuthorizeLogMapper">
    <select id="list" resultType="com.mira.job.consts.dto.OauthAuthorizeLogDTO">
        WITH result AS (
            SELECT client_id, tenant_code, email, ROW_NUMBER() OVER (PARTITION BY email ORDER BY id DESC) AS rn
            FROM oauth_authorize_log
        )
        SELECT r.client_id, r.tenant_code, r.email, u.ip_modify_time_str, u.time_zone
        FROM result r
            JOIN app_user u
            ON r.email = u.email
        WHERE r.rn=1
        LIMIT #{pageSize},#{queryBatch}
    </select>

    <select id="count" resultType="Long">
        SELECT count(DISTINCT email) FROM oauth_authorize_log
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.job.dal.mapper.master.SysNotificationDefineMapper">
    <select id="getMaxDefineId" resultType="long">
        select max(define_id) from sys_notification_define
    </select>

    <delete id="delteByDefineId">
        delete from sys_notification_define where define_id=#{defineId}
    </delete>
</mapper>

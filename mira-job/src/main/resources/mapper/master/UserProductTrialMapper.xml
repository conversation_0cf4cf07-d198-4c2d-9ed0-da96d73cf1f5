<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.job.dal.mapper.master.UserProductTrialMapper">
    <select id="listTrialUsers" resultType="com.mira.api.user.dto.user.TrialUserDTO">
        select t.email, t.flag, u.id as 'user_id'
        from user_product_trial t
        left join app_user u on t.email = u.email
    </select>

</mapper>

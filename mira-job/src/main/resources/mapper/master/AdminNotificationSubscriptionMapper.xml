<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.job.dal.mapper.master.AdminNotificationSubscriptionMapper">
    <insert id="batchInsertTempTable">
        insert into temp_subscription (email) values
            <foreach collection="emails" item="email" separator=",">
                (#{email})
            </foreach>
    </insert>

    <insert id="batchInsert">
        insert into admin_notification_subscription (user_id) values
        <foreach collection="userIds" item="userId" separator=",">
            (#{userId})
        </foreach>
    </insert>
</mapper>

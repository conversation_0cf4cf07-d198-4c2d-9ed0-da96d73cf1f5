package com.mira.core.consts.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 货币枚举
 *
 * <AUTHOR>
 */
@Getter
public enum CurrencyEnum {
    USD("USD", "美元"),
    GBP("GBP", "英镑"),
    EUR("EUR", "欧元"),
    CAD("CAD", "加拿大元"),
    AUD("AUD", "澳大利亚元"),
    TEST("TEST", "测试环境用");

    private final String code;
    private final String description;

    CurrencyEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static CurrencyEnum get(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (CurrencyEnum currencyEnum : CurrencyEnum.values()) {
            if (Objects.equals(code, currencyEnum.getCode())) {
                return currencyEnum;
            }
        }
        return null;
    }
}

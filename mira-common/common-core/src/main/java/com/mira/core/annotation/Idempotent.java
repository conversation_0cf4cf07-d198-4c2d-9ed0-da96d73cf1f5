package com.mira.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 幂等性注解
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface Idempotent {
    /**
     * 过期时间，秒
     *
     * @return long
     */
    long expire() default 2L;

    /**
     * 返回值
     *
     * @return String
     */
    String returnValue() default "";
}

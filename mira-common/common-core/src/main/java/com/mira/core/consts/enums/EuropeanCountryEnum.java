package com.mira.core.consts.enums;

import lombok.Getter;

/**
 * 欧洲国家代码
 *
 * <AUTHOR>
 */
@Getter
public enum EuropeanCountryEnum {
    ALBANIA("AL", "阿尔巴尼亚"),
    ANDORRA("AD", "安道尔"),
    ARMENIA("AM", "亚美尼亚"),
    AUSTRIA("AT", "奥地利"),
    AZERBAIJAN("AZ", "阿塞拜疆"),
    BELARUS("BY", "白俄罗斯"),
    BELGIUM("BE", "比利时"),
    BOSNIA_HERZEGOVINA("BA", "波斯尼亚和黑塞哥维那"),
    BULGARIA("BG", "保加利亚"),
    CROATIA("HR", "克罗地亚"),
    CYPRUS("CY", "塞浦路斯"),
    CZECH_REPUBLIC("CZ", "捷克共和国"),
    DENMARK("DK", "丹麦"),
    ESTONIA("EE", "爱沙尼亚"),
    FINLAND("FI", "芬兰"),
    FRANCE("FR", "法国"),
    GEORGIA("GE", "格鲁吉亚"),
    GERMANY("DE", "德国"),
    GREECE("GR", "希腊"),
    HUNGARY("HU", "匈牙利"),
    ICELAND("IS", "冰岛"),
    IRELAND("IE", "爱尔兰"),
    ITALY("IT", "意大利"),
    KAZAKHSTAN("KZ", "哈萨克斯坦"),
    LATVIA("LV", "拉脱维亚"),
    LIECHTENSTEIN("LI", "列支敦士登"),
    LITHUANIA("LT", "立陶宛"),
    LUXEMBOURG("LU", "卢森堡"),
    MALTA("MT", "马耳他"),
    MOLDOVA("MD", "摩尔多瓦"),
    MONACO("MC", "摩纳哥"),
    MONTENEGRO("ME", "黑山"),
    NETHERLANDS("NL", "荷兰"),
    NORTH_MACEDONIA("MK", "北马其顿"),
    NORWAY("NO", "挪威"),
    POLAND("PL", "波兰"),
    PORTUGAL("PT", "葡萄牙"),
    ROMANIA("RO", "罗马尼亚"),
    RUSSIA("RU", "俄罗斯"),
    SAN_MARINO("SM", "圣马力诺"),
    SERBIA("RS", "塞尔维亚"),
    SLOVAKIA("SK", "斯洛伐克"),
    SLOVENIA("SI", "斯洛文尼亚"),
    SPAIN("ES", "西班牙"),
    SWEDEN("SE", "瑞典"),
    SWITZERLAND("CH", "瑞士"),
    TURKEY("TR", "土耳其"),
    UKRAINE("UA", "乌克兰"),
//    UNITED_KINGDOM("GB", "英国"),
    VATICAN_CITY("VA", "梵蒂冈城");

    private final String code;
    private final String desc;

    EuropeanCountryEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EuropeanCountryEnum get(String code) {
        for (EuropeanCountryEnum europeanCountryEnum : EuropeanCountryEnum.values()) {
            if (europeanCountryEnum.getCode().equals(code)) {
                return europeanCountryEnum;
            }
        }
        return null;
    }
}

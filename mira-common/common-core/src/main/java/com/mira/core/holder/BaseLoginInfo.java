package com.mira.core.holder;

import lombok.Getter;
import lombok.Setter;

/**
 * 基础登录信息
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class BaseLoginInfo {
    /**
     * user id
     */
    private Long id;

    /**
     * partner id
     */
    private Long partnerId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户类型，参考UserTypeEnum
     * @see com.mira.core.consts.enums.UserTypeEnum
     */
    private String userType;

    /**
     * ip
     */
    private String ip;

    /**
     * 时区
     */
    private String timeZone;
}

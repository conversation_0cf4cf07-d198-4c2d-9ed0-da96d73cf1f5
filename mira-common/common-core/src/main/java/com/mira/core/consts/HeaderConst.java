package com.mira.core.consts;

/**
 * 请求头参数
 *
 * <AUTHOR>
 */
public class HeaderConst {
    /**
     * 内部令牌
     */
    public final static String AUTHORIZATION = "Authorization";

    /**
     * 时区
     */
    public final static String TIME_ZONE = "time-zone";

    /**
     * 本地语言
     */
    public final static String LOCAL_LANGUAGE = "local-language";

    /**
     * ip
     */
    public final static String IP = "ip";

    /**
     * 匿名访问
     */
    public final static String ANONYMOUS = "anonymous-api";

    /**
     * 用户类别
     *
     * @see com.mira.core.consts.enums.UserTypeEnum
     */
    public final static String USER_TYPE = "user-type";

    /**
     * 货币
     */
    public final static String CURRENCY = "currency";

    /**
     * 国家标识，针对澳大利亚app增加的请求头。澳大利亚为AU,正常app版本没有这个请求头
     */
    public final static String COUNTRY_FLAG = "mira-country";
}

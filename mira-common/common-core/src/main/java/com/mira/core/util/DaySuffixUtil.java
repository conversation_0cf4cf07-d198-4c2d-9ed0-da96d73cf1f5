package com.mira.core.util;

import java.util.HashMap;
import java.util.Map;

/**
 * english day
 *
 * <AUTHOR>
 */
public class DaySuffixUtil {
    private final static Map<Integer, String> DAY_SUFFIX_MAP = new HashMap<Integer, String>() {
        {
            put(1, "st");
            put(2, "nd");
            put(3, "rd");
            put(0, "th");
        }
    };

    /**
     * 获取day后缀表示
     *
     * @param dayCD 从1开始
     * @return suffix
     */
    public static String getDaySuffix(int dayCD) {
        Integer ovulationEstimateUnit = (dayCD / 10 % 10 != 1 ? 1 : 0) * ((dayCD % 10) < 4 ? 1 : 0) * dayCD % 10;
        return DAY_SUFFIX_MAP.get(ovulationEstimateUnit);
    }
}

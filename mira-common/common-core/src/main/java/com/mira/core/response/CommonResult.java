package com.mira.core.response;

import com.mira.core.consts.enums.BizCodeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 通用出参类
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class CommonResult<T> {
    /**
     * 响应编码
     */
    private Integer code;

    /**
     * 响应描述
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    public static <T> CommonResult<T> OK() {
        return new CommonResult<>(BizCodeEnum.SUCCESS);
    }

    public static <T> CommonResult<T> OK(T data) {
        return new CommonResult<>(BizCodeEnum.SUCCESS, data);
    }

    public static <T> CommonResult<T> FAILED() {
        return new CommonResult<>(BizCodeEnum.INTERNAL_SERVER_ERROR);
    }

    public static <T> CommonResult<T> FAILED(Integer code, String msg) {
        return new CommonResult<>(code, msg);
    }

    public static <T> CommonResult<T> FAILED(String msg) {
        return new CommonResult<>(BizCodeEnum.INTERNAL_SERVER_ERROR.getCode(), msg);
    }

    public CommonResult(BizCodeEnum baseCodeEnum) {
        build(baseCodeEnum.getCode(), baseCodeEnum.getMsg(), null);
    }

    public CommonResult(BizCodeEnum baseCodeEnum, T data) {
        build(baseCodeEnum.getCode(), baseCodeEnum.getMsg(), data);
    }

    public CommonResult(Integer code, String msg) {
        build(code, msg, null);
    }

    public CommonResult() {}

    private void build(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
}

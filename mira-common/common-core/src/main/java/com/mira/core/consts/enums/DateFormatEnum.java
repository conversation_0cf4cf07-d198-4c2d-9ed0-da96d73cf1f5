package com.mira.core.consts.enums;

import com.mira.core.consts.DatePatternConst;
import lombok.Getter;

import java.time.format.DateTimeFormatter;

/**
 * 日期时间格式器枚举
 *
 * <AUTHOR>
 */
@Getter
public enum DateFormatEnum {
    DATE_PATTERN(DatePatternConst.DATE_PATTERN, DateTimeFormatter.ofPattern(DatePatternConst.DATE_PATTERN)),
    TIME_PATTERN(DatePatternConst.TIME_PATTERN, DateTimeFormatter.ofPattern(DatePatternConst.TIME_PATTERN)),
    DATE_TIME_PATTERN(DatePatternConst.DATE_TIME_PATTERN, DateTimeFormatter.ofPattern(DatePatternConst.DATE_TIME_PATTERN)),

    YYYYMMDD(DatePatternConst.YYYYMMDD, DateTimeFormatter.ofPattern(DatePatternConst.YYYYMMDD)),
    MONTH_PATTERN(DatePatternConst.MONTH_PATTERN, DateTimeFormatter.ofPattern(DatePatternConst.MONTH_PATTERN)),
    MMDDYY(DatePatternConst.MMDDYY, DateTimeFormatter.ofPattern(DatePatternConst.MMDDYY)),
    ;
    private final String pattern;
    private final DateTimeFormatter formatter;

    DateFormatEnum(String pattern, DateTimeFormatter formatter) {
        this.pattern = pattern;
        this.formatter = formatter;
    }

    public static DateFormatEnum getDateFormatter(String pattern) {
        for (DateFormatEnum dateFormatEnum : DateFormatEnum.values()) {
            if (dateFormatEnum.getPattern().equals(pattern)) {
                return dateFormatEnum;
            }
        }
        return null;
    }
}

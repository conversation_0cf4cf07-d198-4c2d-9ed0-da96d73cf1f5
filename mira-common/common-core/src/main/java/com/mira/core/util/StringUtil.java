package com.mira.core.util;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StringUtil {
    private final static char[] LOWER = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k',
            'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};

    private final static char[] UPPER = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K',
            'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};

    private final static char[] NUMBER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};

    private final static char[] ALL = ArrayUtils.addAll(ArrayUtils.addAll(LOWER, UPPER), NUMBER);
    private final static char[] LETTERS = ArrayUtils.addAll(LOWER, UPPER);

    /**
     * 返回 count 个包含随机英文大小写和数字的字符串
     *
     * @param count 字符串字符数
     * @return 随机字符串
     */
    public static String random(int count) {
        return RandomStringUtils.random(count, ALL);
    }

    /**
     * 返回 count 个包含随机英文大小写的字符串
     *
     * @param count 字符串字符数
     * @return 随机字符串
     */
    public static String randomLetters(int count) {
        return RandomStringUtils.random(count, LETTERS);
    }

    /**
     * 返回 count 个包含随机英文小写的字符串
     *
     * @param count 字符串字符数
     * @return 随机字符串
     */
    public static String randomLowerLetters(int count) {
        return RandomStringUtils.random(count, LOWER);
    }

    /**
     * 返回 count 个包含随机英文大写的字符串
     *
     * @param count 字符串字符数
     * @return 随机字符串
     */
    public static String randomUpperLetters(int count) {
        return RandomStringUtils.random(count, UPPER);
    }

    /**
     * 返回 count 个包含随机数字的字符串
     *
     * @param count 字符串字符数
     * @return 随机字符串
     */
    public static String randomNumber(int count) {
        return RandomStringUtils.random(count, NUMBER);
    }
}

package com.mira.core.util;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

public class AESUtil {
    static {
        // 添加 BouncyCastle 提供者，以支持 PKCS7Padding
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final String AES_ALGORITHM = "AES/ECB/PKCS5Padding"; // Java不支持PKCS7Padding，PKCS5等效

    private static final String SECRET_KEY = "544F50494E454544"; // 16字节密钥（128bit）

    /**
     * AES 解密（HEX 编码输入）
     */
    public static String decrypt(String cipherTextHex) {
        return decrypt(cipherTextHex, SECRET_KEY);
    }

    public static String decrypt(String cipherTextHex,String key) {
        try {
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            SecretKey secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");

            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            // 处理 HEX 编码并解密
            byte[] encryptedBytes = hexStringToByteArray(cipherTextHex);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);

            // 返回解密后的 HEX 编码数据
            return bytesToHex(decryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败", e);
        }
    }

    /**
     * AES 加密（返回 HEX 编码）
     */
    public static String encrypt(String plainTextHex) {
       return encrypt(plainTextHex, SECRET_KEY);
    }

    public static String encrypt(String plainTextHex,String key) {
        try {
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            SecretKey secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");

            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            // 处理 HEX 编码并加密
            byte[] plainBytes = hexStringToByteArray(plainTextHex);
            byte[] encryptedBytes = cipher.doFinal(plainBytes);

            // 返回加密后的 HEX 编码数据
            return bytesToHex(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * HEX 转 Byte[]
     */
    private static byte[] hexStringToByteArray(String hex) {
        return DatatypeConverter.parseHexBinary(hex);
    }

    /**
     * Byte[] 转 HEX
     */
    private static String bytesToHex(byte[] bytes) {
        return DatatypeConverter.printHexBinary(bytes);
    }

    public static void main(String[] args) {
        // 示例：解密
        String encryptedHex = "8a3cddc0bee577c7c8039d21d22732b15a0eb76b823dcf00f747dc57b773316d6f2eea01bd4662580ee8465965d428b5d2856baba46d3351392b9453cfb8ac8df0caa35a1243d377ad0762d71b2cbbe83191737a9c847bcf118f0c1b8735ddfaa4b79b9c10c469aaf64105b3e56bd8687f5053752b1613d2ff9dfe8b6f589b0b29a00c710e1e7be9b60b2e5d0731e146301bc2bb2ead0be977df6d977eede35b1b788088a4f7525e5bc1c0631ecdb2125fa8e2ba484da5c92d6da2cf48f0383d";
        String decryptedHex = decrypt(encryptedHex);

        System.out.println("解密后 HEX 数据：" + decryptedHex);

        // 示例：加密（验证一下加密后是否和原密文匹配）
        String encryptedAgainHex = encrypt(decryptedHex);
        System.out.println("加密后 HEX 数据：" + encryptedAgainHex);

        // 验证加密后的数据是否和原密文一致
        System.out.println("加密后数据是否匹配原始密文：" + encryptedAgainHex.equalsIgnoreCase(encryptedHex));
    }
}

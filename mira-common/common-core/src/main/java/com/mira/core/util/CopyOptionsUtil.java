package com.mira.core.util;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import org.springframework.cglib.core.ReflectUtils;

import java.beans.PropertyDescriptor;
import java.util.HashMap;
import java.util.Map;

/**
 * Bean Copy 选项
 *
 * <AUTHOR>
 */
public class CopyOptionsUtil {
    /**
     * 下划线转驼峰
     */
    public static CopyOptions getCopyOptions(Class sourceClass) {
        Map<String, String> fieldMap = new HashMap<>();
        PropertyDescriptor[] beanProperties = ReflectUtils.getBeanProperties(sourceClass);
        for (PropertyDescriptor field : beanProperties) {
            String name = field.getName();
            String camel = StrUtil.toCamelCase(name);
            if (!name.equalsIgnoreCase(camel)) {
                fieldMap.put(name, camel);
            }
            String under = StrUtil.toUnderlineCase(name);
            if (!name.equalsIgnoreCase(under)) {
                fieldMap.put(name, under);
            }
        }

        return CopyOptions.create().setFieldMapping(fieldMap);
    }
}

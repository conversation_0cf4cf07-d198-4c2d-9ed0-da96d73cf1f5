package com.mira.core.consts.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 试剂类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum BiomarkerEnum {
    LH(1),
    HCG(2),
    E3G(3),
    PDG(9),
    E3G_HIGH_RANGE(13),
    HCG_QUALITATIVE(14),
    FSH(16),
    BBT(99)
    ;

    private final Integer biomarker;

    BiomarkerEnum(Integer biomarker) {
        this.biomarker = biomarker;
    }

    public static BiomarkerEnum get(Integer biomarker) {
        for (BiomarkerEnum biomarkerEnum : BiomarkerEnum.values()) {
            if (Objects.equals(biomarkerEnum.getBiomarker(), biomarker)) {
                return biomarkerEnum;
            }
        }
        return null;
    }
}

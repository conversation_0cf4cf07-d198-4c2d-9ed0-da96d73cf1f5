package com.mira.core.util;

import cn.hutool.core.util.RandomUtil;
import org.apache.shiro.crypto.hash.Sha256Hash;

import java.util.regex.Pattern;

/**
 * 密码工具类
 *
 * <AUTHOR>
 */
public class PasswordUtil {
    public static void main(String[] args) {
        String end = "@miracare.com";
        for (int i = 1; i <= 10; i++) {
            String prev = "LabelOvu";
            if (i < 10) {
                prev += "0";
            }
            String email = prev + i + end;

            String salt = PasswordUtil.generateSalt(20);
            String password = RandomUtil.randomString(5) + "@mira" + i;
            String dbPassword = PasswordUtil.encryptPassword(password, salt);
            System.out.println("【" + email + "】:" + "，【password】:" + password + "，【salt】:" + salt + "，【dbPassword】:" + dbPassword);
        }
    }

    /**
     * 生成盐
     *
     * @param count 位数
     * @return 盐
     */
    public static String generateSalt(int count) {
        return StringUtil.random(count);
    }

    /**
     * 生成加密密码
     *
     * @param password 密码明文
     * @param salt     盐
     * @return Encrypt Password
     */
    public static String encryptPassword(CharSequence password, String salt) {
        return new Sha256Hash(password, salt).toHex();
    }

    /**
     * 密码匹配
     *
     * @param originPassword  密码明文
     * @param encryptPassword 加密密码
     * @param salt            盐
     * @return true/false
     */
    public static boolean match(CharSequence originPassword, String encryptPassword, String salt) {
        return new Sha256Hash(originPassword, salt).toHex().equals(encryptPassword);
    }

    /**
     * 校验密码规则
     *
     * @param password 密码
     * @return true/false
     */
    public static boolean checkRules(String password) {
        String pattern = "^(?=.*[0-9])(?=.*[a-zA-Z])[0-9A-Za-z~!@#$%^&*():,;'=?./-]{8,30}$";
        return Pattern.matches(pattern, password);
    }
}

package com.mira.core.consts.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * 用户类别枚举
 *
 * <AUTHOR>
 */
@Getter
public enum UserTypeEnum {
    APP_USER("0", "APP用户", Lists.newArrayList("mira-access-token", "mira-check-token", "mira-web-token")),
    PARTNER_USER("1", "Partner用户", Lists.newArrayList("mira-partner-token")),
    CLINIC_USER("2", "Clinic用户", Lists.newArrayList("mira-clinic-token")),
    THIRD_PARTY_USER("3", "第三方对接用户", Lists.newArrayList("Authorization")),
    BACKEND_ADMIN("99", "后台管理系统用户", Lists.newArrayList("mira-admin-token")),
    CLINIC_CHECK_USER("100", "Clinic check用户", Lists.newArrayList("mira-clinic-check-token")),

    COMPATIBLE_CLINIC_USER(CLINIC_USER.getType(), "兼容旧的Clinic用户请求头",
            Lists.newArrayList("mira-doctor-token", "mira-doctor-check-token", "mira-tenant-token", "mira-tenant-check-token"));

    private final String type;
    private final String desc;
    private final List<String> tokenHeaderList;

    UserTypeEnum(String type, String desc, List<String> tokenHeaderList) {
        this.type = type;
        this.desc = desc;
        this.tokenHeaderList = tokenHeaderList;
    }

    public static UserTypeEnum get(String type) {
        for (UserTypeEnum userTypeEnum : UserTypeEnum.values()) {
            if (userTypeEnum.getType().equals(type)) {
                return userTypeEnum;
            }
        }
        return UserTypeEnum.APP_USER;
    }
}

package com.mira.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Thread Pool Util
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ThreadPoolUtil {
    private static final int coreSize = Runtime.getRuntime().availableProcessors() + 1;
    private final ThreadPoolExecutor threadPoolExecutor;

    private ThreadPoolUtil() {
        threadPoolExecutor = new ThreadPoolExecutor(
                coreSize,
                coreSize,
                0L,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(200),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    private static class ThreadPoolHolder {
        private final static ThreadPoolUtil instance = new ThreadPoolUtil();
    }

    public static ThreadPoolExecutor getPool() {
        return ThreadPoolHolder.instance.threadPoolExecutor;
    }

    @PreDestroy
    public void destroy() {
        threadPoolExecutor.shutdown();
        log.info("ThreadPoolUtil destroy ...");
    }
}

package com.mira.core.util;

import java.time.LocalDate;
import java.time.Period;

/**
 * 年龄计算工具
 *
 * <AUTHOR>
 */
public class AgeUtil {


    public static Integer calculateAge(Integer birthYear, Integer birthMonth, Integer birthDay) {
        if ((birthMonth == null || birthDay == null) && birthYear == null) {
            return null;
        } else if ((birthMonth == null || birthDay == null) && birthYear != null) {
            return LocalDateUtil.getCurrentYear() - birthYear;
        }

        LocalDate birthDate = LocalDate.of(birthYear, birthMonth, birthDay);
        LocalDate currentDate = LocalDate.now();
        Period period = Period.between(birthDate, currentDate);
        return period.getYears();
    }
}

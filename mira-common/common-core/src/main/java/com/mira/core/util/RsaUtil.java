package com.mira.core.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.json.JSONObject;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * RSA 加解密
 *
 * <AUTHOR>
 */
public class RsaUtil {
    private static final String[] BASE_TYPES = {
            "java.lang.Integer", "java.lang.Byte", "java.lang.Long", "java.lang.Double", "java.lang.Float",
            "java.lang.Character", "java.lang.Short", "java.lang.Boolean", "java.lang.String"
    };

    /**
     * 加密
     *
     * @param data       加密数据
     * @param publicKey  公钥
     * @return String
     */
    public static String encryptionRsa(String data, String publicKey) {
        RSA rsa = SecureUtil.rsa(null, publicKey);
        byte[] encrypt = rsa.encrypt(data, KeyType.PublicKey);
        return Base64.encode(encrypt);
    }

    /**
     * 验签
     *
     * @param orgData   原始数据
     * @param signData  加签数据
     * @param publicKey 公钥
     * @return boolean
     */
    public static boolean checkRsa(String orgData, String signData, String publicKey) {
        RSA rsa = SecureUtil.rsa(null, publicKey);
        String data = new String(rsa.decrypt(signData, KeyType.PublicKey));
        return StringUtils.hasText(data) && orgData.equals(data);
    }

    /**
     * 解密
     *
     * @param data       加密数据
     * @param privateKey 私钥
     * @return String
     */
    public static String decodeRsa(String data, String privateKey) {
        RSA rsa = SecureUtil.rsa(privateKey, null);
        return new String(rsa.decrypt(data, KeyType.PrivateKey));
    }

    /**
     * 生成签名
     *
     * @param map        请求参数
     * @param privateKey 私钥
     * @return String
     */
    public static String generateSign(TreeMap<String, Object> map, String privateKey) {
        //签名规则
        Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA, privateKey, null);
        //参数值
        StringBuffer param = new StringBuffer();
        //循环拼接参数
        mapToString(map, param);
        //将String转换为byte
        byte[] data = Convert.toStr(param).getBytes();
        //签名
        byte[] signed = sign.sign(data);
        return Base64.encode(signed);
    }

    /**
     * 生成签名
     *
     * @param json       json对象串
     * @param privateKey 私钥
     * @return String
     */
    public static String generateSign(String json, String privateKey) {
        //签名规则
        Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA, privateKey, null);
        //参数值
        StringBuffer param = new StringBuffer();
        //循环拼接参数
        mapToString(JsonUtil.toObject(json, TreeMap.class), param);
        //将String转换为byte
        byte[] data = Convert.toStr(param).getBytes();
        //签名
        byte[] signed = sign.sign(data);
        return Base64.encode(signed);
    }

    /**
     * 生成签名
     *
     * @param val        对象
     * @param privateKey 私钥
     * @return String
     */
    public static <T> String generateSign(T val, String privateKey) {
        TreeMap<String, Object> map = Convert.convert(new TypeReference<TreeMap<String, Object>>() {
        }, val);
        //签名规则
        Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA, privateKey, null);
        //参数值
        StringBuffer param = new StringBuffer();
        //循环拼接参数
        mapToString(map, param);
        //将String转换为byte
        byte[] data = Convert.toStr(param).getBytes();
        //签名
        byte[] signed = sign.sign(data);
        return Base64.encode(signed);
    }

    /**
     * 验签
     *
     * @param map      参数
     * @param signData 签名
     * @return boolean
     */
    public static Boolean verifySign(TreeMap<String, Object> map, String signData, String publicKey) {
        //签名规则
        Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA, null, publicKey);
        //参数值
        StringBuffer param = new StringBuffer();
        //循环拼接参数
        mapToString(map, param);
        //将String转换为byte
        byte[] data = Convert.toStr(param).getBytes();
        //验证签名
        //返回
        return sign.verify(data, Base64.decode(signData));
    }

    /**
     * 验签
     *
     * @param val      参数
     * @param signData 签名
     * @return boolean
     */
    public static <T> Boolean verifySign(T val, String signData, String publicKey) {
        TreeMap<String, Object> map = Convert.convert(new TypeReference<TreeMap<String, Object>>() {
        }, val);
        //签名规则
        Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA, null, publicKey);
        //参数值
        StringBuffer param = new StringBuffer();
        //循环拼接参数
        mapToString(map, param);
        //将String转换为byte
        byte[] data = Convert.toStr(param).getBytes();
        //验证签名
        //返回
        return sign.verify(data, Base64.decode(signData));
    }

    /**
     * 签名验证
     *
     * @param json     参数
     * @param signData 签名
     * @return boolean
     */
    public static boolean verifySign(String json, String signData, String publicKey) {
        //签名规则
        Sign sign = SecureUtil.sign(SignAlgorithm.SHA256withRSA, null, publicKey);
        //参数值
        StringBuffer param = new StringBuffer();
        //循环拼接参数
        mapToString(JsonUtil.toObject(json, TreeMap.class), param);
        //将String转换为byte
        byte[] data = Convert.toStr(param).getBytes();
        //验证签名
        byte[] decode = Base64.decode(signData);
        Boolean verify = sign.verify(data, decode);
        //返回
        return verify;
    }

    /**
     * map转字符串
     *
     * @param map   集合
     * @param param 追加参数
     * @return
     */
    public static void mapToString(TreeMap<String, Object> map, StringBuffer param) {
        //循环集合
        for (String key : map.keySet()) {
            //值
            Object obj = map.get(key);
            //判断不同类型，执行不同参数转换
            if (obj instanceof List) {
                // 转list
                List list = Convert.convert(List.class, obj);
                // 递归遍历
                for (Object m : list) {
                    if (isBaseType(m)) {
                        param.append(Convert.toStr(m));
                    } else {
                        mapToString(Convert.convert(TreeMap.class, m), param);
                    }
                }
            } else if (obj instanceof Map) {
                // 递归遍历
                mapToString(Convert.convert(TreeMap.class, obj), param);
            } else if (obj instanceof JSONObject) {
                mapToString(Convert.convert(TreeMap.class, obj), param);
            } else {
                //判断是否为空
                if (org.apache.commons.lang3.StringUtils.isNotBlank(Convert.toStr(obj))) {
                    //附值
                    param.append(Convert.toStr(obj));
                }
            }
        }
    }

    private static boolean isBaseType(Object object) {
        String typeName = object.getClass().getTypeName();
        for (String baseType : BASE_TYPES) {
            if (baseType.equals(typeName)) {
                return true;
            }
        }
        return false;
    }
}
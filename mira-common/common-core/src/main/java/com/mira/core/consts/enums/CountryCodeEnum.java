package com.mira.core.consts.enums;

import lombok.Getter;

/**
 * 国家编码枚举
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Getter
public enum CountryCodeEnum {
    US, AU, EU, GB, CA;

    /**
     * 通过国家编码和洲编码获取货币编码
     *
     * @param countryCode   国家编码
     * @param continentCode 洲编码
     * @return 默认返回美元
     */
    private static CountryCodeEnum getCurrencyCodeByCountry(String countryCode, String continentCode) {
        if ("US".equals(countryCode)) {
            return US;
        }
        if ("AU".equals(countryCode)) {
            return AU;
        }
        if ("GB".equals(countryCode)) {
            return GB;
        }
        if ("CA".equals(countryCode)) {
            return CA;
        }
        if (EuropeanCountryEnum.get(countryCode) != null) {
            return EU;
        }

        return US;
    }

    /**
     * 通过国家编码和洲编码获取货币
     *
     * @param countryCode   国家编码
     * @param continentCode 洲编码
     * @return CurrencyEnum
     */
    public static CurrencyEnum getCurrencyByCountry(String countryCode, String continentCode) {
        CurrencyEnum currencyEnum;
        // 通过国家编码和洲编码获取货币枚举
        switch (getCurrencyCodeByCountry(countryCode, continentCode)) {
            case US:
                currencyEnum = CurrencyEnum.USD;
                break;
            case AU:
                currencyEnum = CurrencyEnum.AUD;
                break;
            case EU:
                currencyEnum = CurrencyEnum.EUR;
                break;
            case GB:
                currencyEnum = CurrencyEnum.GBP;
                break;
            case CA:
                currencyEnum = CurrencyEnum.CAD;
                break;
            default:
                currencyEnum = CurrencyEnum.USD;
                break;
        }
        return currencyEnum;
    }
}

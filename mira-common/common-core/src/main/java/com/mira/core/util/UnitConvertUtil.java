package com.mira.core.util;

import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.consts.enums.WeightUnitEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 单位转换
 *
 * <AUTHOR>
 */
public class UnitConvertUtil {
    /**
     * Temp:
     * ℃ to ℉: temp_value = temp_value * 9.0/5.0 + 32.0
     * ℉ to ℃: temp_value = (temp_value – 32.0) * 5.0 / 9.0
     *
     * @param unit  单位
     * @param value 值
     */
    public static BigDecimal getTemp(String unit, BigDecimal value) {
        if (TempUnitEnum.C.getValue().equals(unit)) {
            // ℃ to ℉: temp_value = temp_value * 9.0/5.0 + 32.0
            if (value != null) {
                return value.multiply(BigDecimal.valueOf(9.0)).divide(BigDecimal.valueOf(5.0), 6, RoundingMode.HALF_DOWN).add(BigDecimal.valueOf(32.0));
            }
        } else if (TempUnitEnum.F.getValue().equals(unit)) {
            // ℉ to ℃: temp_value = (temp_value – 32.0) * 5.0 / 9.0
            if (value != null) {
                return value.subtract(BigDecimal.valueOf(32.0)).multiply(BigDecimal.valueOf(5.0)).divide(BigDecimal.valueOf(9.0), 6, RoundingMode.HALF_DOWN);
            }
        }
        return null;
    }

    /**
     * Weight:
     * Kg to lb:  weight_value = weight_value * 2.2046226218488
     * lb to Kg:  weight_value = weight_value * 0.453592369999995
     *
     * @param unit  单位
     * @param value 值
     */
    public static BigDecimal getWeight(String unit, BigDecimal value) {
        if (WeightUnitEnum.K.getValue().equals(unit)) {
            //Kg to lb:  weight_value = weight_value * 2.2046226218488
            if (value != null) {
                return value.multiply(BigDecimal.valueOf(2.2046226218488));
            }
        } else if (WeightUnitEnum.L.getValue().equals(unit)) {
            //lb to Kg:  weight_value = weight_value * 0.453592369999995
            if (value != null) {
                return value.multiply(BigDecimal.valueOf(0.453592369999995));
            }
        }
        return null;
    }

    public static BigDecimal getUnitConvert(String type, String unit, BigDecimal value) {
        if ("temp".equals(type)) {
            return getTemp(unit, value);
        } else if ("weight".equals(type)) {
            return getWeight(unit, value);
        }
        return null;
    }
}

package com.mira.core.util;

import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.DateFormatEnum;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;

/**
 * 本地日期工具类
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class LocalDateUtil {
    private static DateTimeFormatter getFormatter(String pattern) {
        DateFormatEnum dateFormatEnum = DateFormatEnum.getDateFormatter(pattern);
        if (Objects.isNull(dateFormatEnum)) {
            throw new RuntimeException("Date format pattern is not support.");
        }
        return dateFormatEnum.getFormatter();
    }

    /**
     * 日期格式化
     *
     * @param date    日期
     * @param pattern 格式
     * @return 格式化后的日期
     */
    public static String format(LocalDate date, String pattern) {
        DateTimeFormatter formatter = getFormatter(pattern);
        return date.format(formatter);
    }

    /**
     * 日期格式化
     *
     * @param date    日期
     * @param pattern 格式
     * @return 格式化后的日期
     */
    public static String format(LocalDateTime time, String pattern) {
        DateTimeFormatter formatter = getFormatter(pattern);
        return time.format(formatter);
    }

    /**
     * 日期格式转化，从旧格式转化为新格式
     *
     * @param date          日期字符串
     * @param originPattern 旧格式
     * @param targetPattern 新格式
     * @return 新格式日期字符串
     */
    public static String format(String date, String originPattern, String targetPattern) {
        DateTimeFormatter originFormatter = getFormatter(originPattern);
        DateTimeFormatter targetFormatter = getFormatter(targetPattern);

        if (date.length() == 10) {
            LocalDate parseDate = LocalDate.parse(date, originFormatter);
            return parseDate.format(targetFormatter);
        }

        LocalDateTime parseDateTime = LocalDateTime.parse(date, originFormatter);
        return parseDateTime.format(targetFormatter);
    }

    /**
     * 日期加减
     *
     * @param date or datetime 日期字符串
     * @param days 天数
     * @return 新日期
     */
    public static String plusDay(String date, int days, String pattern) {
        DateTimeFormatter formatter = getFormatter(pattern);

        if (DatePatternConst.DATE_PATTERN.equals(pattern)) {
            if (date.length() > DatePatternConst.DATE_PATTERN.length()) {
                date = date.substring(0, 10);
            }
            LocalDate parseDate = LocalDate.parse(date, formatter).plusDays(days);
            return parseDate.format(formatter);

        } else if (DatePatternConst.DATE_TIME_PATTERN.equals(pattern)) {
            LocalDateTime parseDateTime = LocalDateTime.parse(date, formatter).plusDays(days);
            return parseDateTime.format(formatter);
        }
        throw new RuntimeException("Date format pattern is not support.");
    }

    /**
     * 获取相差的天数
     *
     * @param minuend  被减日期
     * @param subtract 减日期
     * @return 天数
     */
    public static int minusToDay(String minuend, String subtract) {
        if (minuend.length() > DatePatternConst.DATE_PATTERN.length()) {
            minuend = minuend.substring(0, 10);
        }
        if (subtract.length() > DatePatternConst.DATE_PATTERN.length()) {
            subtract = subtract.substring(0, 10);
        }

        DateTimeFormatter formatter = getFormatter(DatePatternConst.DATE_PATTERN);
        LocalDate minuendDate = LocalDate.parse(minuend, formatter);
        LocalDate subtractDate = LocalDate.parse(subtract, formatter);
        return (int) (minuendDate.toEpochDay() - subtractDate.toEpochDay());
    }

    /**
     * 时间日期字符串日期字符串
     */
    public static String dateTime2Date(String dateTimeStr) {
        if (dateTimeStr.length() == 19) {
            return dateTimeStr.substring(0, 10);
        }
        return dateTimeStr;
    }

    /**
     * 获取当前年份
     *
     * @return 年份，e.g. 1970
     */
    public static int getCurrentYear() {
        return LocalDate.now().getYear();
    }

    /**
     * 获取时间戳
     *
     * @param date 时间
     * @return 时间戳
     */
    public static long getTimestamp(String date) {
        if (date.length() == 10) {
            date = date + " 00:00:00";
        }
        DateTimeFormatter formatter = getFormatter(DatePatternConst.DATE_TIME_PATTERN);
        LocalDateTime localDateTime = LocalDateTime.parse(date, formatter);
        return localDateTime.atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 返回两个时间间隔内的所有日期,含首不含尾
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期列表
     */
    public static List<String> getBetweenDate(String startDate, String endDate) {
        List<String> result = new ArrayList<>();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        while (start.isBefore(end)) {
            result.add(start.toString());
            start = start.plusDays(1);
        }
        return result;
    }

    /**
     * 判断日期是否在指定日期之后
     *
     * @param date        日期
     * @param compareDate 比较日期
     * @param pattern     日期格式
     * @return true/false
     */
    public static boolean after(String date, String compareDate, String pattern) {
        DateFormatEnum dateFormatEnum = DateFormatEnum.getDateFormatter(pattern);
        if (dateFormatEnum == null) {
            throw new RuntimeException("Date format pattern is not support.");
        }
        DateTimeFormatter formatter = dateFormatEnum.getFormatter();

        switch (dateFormatEnum) {
            case DATE_PATTERN:
                LocalDate localDate = LocalDate.parse(date, formatter);
                LocalDate compareLocalDate = LocalDate.parse(compareDate, formatter);
                return localDate.isAfter(compareLocalDate);
            case DATE_TIME_PATTERN:
                LocalDateTime localDateTime = LocalDateTime.parse(date, formatter);
                LocalDateTime compareLocalDateTime = LocalDateTime.parse(compareDate, formatter);
                return localDateTime.isAfter(compareLocalDateTime);
            default:
                throw new RuntimeException("Date format pattern is not support.");
        }
    }

    /**
     * 判断时间是否在指定日期之间
     *
     * @param time      时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return true/false
     */
    public static boolean isBetweenTime(String time, String startTime, String endTime) {
        if (time.length() == 19) {
            time = time.substring(11, 19);
        }
        DateTimeFormatter dateTimeFormatter = DateFormatEnum.TIME_PATTERN.getFormatter();
        LocalTime targetLocalTime = LocalTime.parse(time, dateTimeFormatter);
        LocalTime startLocalTime = LocalTime.parse(startTime, dateTimeFormatter);
        LocalTime endLocalTime = LocalTime.parse(endTime, dateTimeFormatter);
        return targetLocalTime.isAfter(startLocalTime) && (targetLocalTime.isBefore(endLocalTime));
    }

    /**
     * 判断时间是否在指定日期之间
     *
     * @param date      时间
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return true/false
     */
    public static boolean isBetweenDate(String date, String startDate, String endDate) {
        if (date.length() > DatePatternConst.DATE_PATTERN.length()) {
            date = date.substring(0, 10);
        }
        if (startDate.length() > DatePatternConst.DATE_PATTERN.length()) {
            startDate = startDate.substring(0, 10);
        }
        if (endDate.length() > DatePatternConst.DATE_PATTERN.length()) {
            endDate = endDate.substring(0, 10);
        }

        DateTimeFormatter dateFormatter = DateFormatEnum.DATE_PATTERN.getFormatter();
        LocalDate targetLocalDate = LocalDate.parse(date, dateFormatter);
        LocalDate startLocalDate = LocalDate.parse(startDate, dateFormatter);
        LocalDate endLocalDate = LocalDate.parse(endDate, dateFormatter);
        return targetLocalDate.isAfter(startLocalDate) && (targetLocalDate.isBefore(endLocalDate));
    }

    /**
     * 判断时间是否在指定日期之间，包含
     *
     * @param date      时间
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return true/false
     */
    public static boolean isBetweenDateAndEqual(String date, String startDate, String endDate) {
        if (date.length() > DatePatternConst.DATE_PATTERN.length()) {
            date = date.substring(0, 10);
        }
        if (startDate.length() > DatePatternConst.DATE_PATTERN.length()) {
            startDate = startDate.substring(0, 10);
        }
        if (endDate.length() > DatePatternConst.DATE_PATTERN.length()) {
            endDate = endDate.substring(0, 10);
        }

        DateTimeFormatter dateFormatter = DateFormatEnum.DATE_PATTERN.getFormatter();
        LocalDate targetLocalDate = LocalDate.parse(date, dateFormatter);
        LocalDate startLocalDate = LocalDate.parse(startDate, dateFormatter);
        LocalDate endLocalDate = LocalDate.parse(endDate, dateFormatter);

        return (targetLocalDate.isAfter(startLocalDate) || targetLocalDate.isEqual(startLocalDate))
                && (targetLocalDate.isBefore(endLocalDate) || targetLocalDate.isEqual(endLocalDate));
    }

    /**
     * 判断时间是否在指定日期之间，包含
     *
     * @param date      时间
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return true/false
     */
    public static boolean isBetweenDateAndEqualLeft(String date, String startDate, String endDate) {
        if (date.length() > DatePatternConst.DATE_PATTERN.length()) {
            date = date.substring(0, 10);
        }
        if (startDate.length() > DatePatternConst.DATE_PATTERN.length()) {
            startDate = startDate.substring(0, 10);
        }
        if (endDate.length() > DatePatternConst.DATE_PATTERN.length()) {
            endDate = endDate.substring(0, 10);
        }

        DateTimeFormatter dateFormatter = DateFormatEnum.DATE_PATTERN.getFormatter();
        LocalDate targetLocalDate = LocalDate.parse(date, dateFormatter);
        LocalDate startLocalDate = LocalDate.parse(startDate, dateFormatter);
        LocalDate endLocalDate = LocalDate.parse(endDate, dateFormatter);

        return (targetLocalDate.isAfter(startLocalDate) || targetLocalDate.isEqual(startLocalDate))
                && (targetLocalDate.isBefore(endLocalDate));
    }

    /**
     * 获取localdate
     *
     * @param date 日期
     * @return LocalDate
     */
    public static LocalDate getLocalDate(String date) {
        if (date.length() > DatePatternConst.DATE_PATTERN.length()) {
            date = date.substring(0, 10);
        }
        return LocalDate.parse(date, DateFormatEnum.DATE_PATTERN.getFormatter());
    }

    /**
     * 计算给定时间与今天的月份差距。
     *
     * @param dateTime 输入日期，可以是毫秒时间戳或 yyyy-MM-dd 格式的字符串
     * @return 月份差距，未满一个月按1个月计算，未满15天算0，满15天算一月。
     */
    public static int calculateMonthGap(Object dateTime) {
        LocalDate dateInput;
        LocalDate today = LocalDate.now();

        if (dateTime instanceof Long) {
            // 将毫秒时间戳转换为LocalDate
            dateInput = LocalDate.ofEpochDay(((Long) dateTime) / 86400000);
        } else if (dateTime instanceof String) {
            // 解析日期字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            dateInput = LocalDate.parse((String) dateTime, formatter);
        } else {
            throw new IllegalArgumentException("Invalid type for dateTime parameter.");
        }

        // 计算周期差
        Period period = Period.between(dateInput, today);
        int months = period.getMonths();
        int years = period.getYears();
        int days = period.getDays();

        // 转换年份差到月份并根据天数调整
        months += years * 12;
        if (days >= 15) {
            months += 1;  // 满15天算1月
        }

        return months;
    }

    /**
     * 对给定的时间字符串随机增加1到10秒。
     *
     * @param timeStr 时间字符串，假设格式为 "yyyy-MM-dd HH:mm:ss"
     * @return 增加随机秒数后的时间字符串
     */
    public static String addRandomSeconds(String timeStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 解析字符串为 LocalDateTime
        LocalDateTime dateTime = LocalDateTime.parse(timeStr, formatter);

        // 创建随机数生成器
        Random random = new Random();
        int randomSeconds = 1 + random.nextInt(10); // 生成1到10之间的随机数

        // 在日期时间上添加随机秒数
        LocalDateTime updatedDateTime = dateTime.plusSeconds(randomSeconds);

        // 将更新后的 LocalDateTime 转换回字符串
        return updatedDateTime.format(formatter);
    }
}

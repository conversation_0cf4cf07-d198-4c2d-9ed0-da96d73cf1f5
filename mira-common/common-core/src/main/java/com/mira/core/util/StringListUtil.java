package com.mira.core.util;

import com.google.common.base.Joiner;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字符串与列表互转工具类
 *
 * <AUTHOR>
 */
public class StringListUtil {
    /**
     * 字符串转列表
     *
     * @param str       字符串
     * @param separator 分隔符
     * @return List<Integer>
     */
    public static List<Integer> strToIntegerList(String str, String separator) {
        if (StringUtils.isBlank(str)) {
            return new ArrayList<>();
        }
        return Arrays.stream(str.split(separator))
                .map(Integer::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 字符串转列表
     *
     * @param str       字符串
     * @param separator 分隔符
     * @return List<Long>
     */
    public static List<Long> strToLongList(String str, String separator) {
        if (StringUtils.isBlank(str)) {
            return new ArrayList<>();
        }
        return Arrays.stream(str.split(separator))
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 字符串转列表
     *
     * @param str       字符串
     * @param separator 分隔符
     * @return List<String>
     */
    public static List<String> strToList(String str, String separator) {
        if (StringUtils.isBlank(str)) {
            return new ArrayList<>();
        }
        return Arrays.stream(str.split(separator))
                .collect(Collectors.toList());
    }

    /**
     * 列表转字符串
     *
     * @param list      列表
     * @param separator 分隔符
     * @return String
     */
    public static <T> String listToString(List<T> list, String separator) {
        if (CollectionUtils.isNotEmpty(list)) {
            return Joiner.on(separator).join(list);
        }
        return null;
    }
}

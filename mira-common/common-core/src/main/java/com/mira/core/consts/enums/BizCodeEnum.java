package com.mira.core.consts.enums;

import com.mira.core.response.enums.ICodeEnum;
import lombok.Getter;

/**
 * 错误码
 *
 * <AUTHOR>
 */
@Getter
public enum BizCodeEnum implements ICodeEnum {
    SUCCESS(0, "success"),
    TOKEN_INVALID(1, "Time flies! Your login expired. Please log in again."),
    CREATE_ACCOUNT_FAIL(2, "Create account fail, please try again later or contact with us."),
    PARAM_ERROR(3, "Request param error."),
    /**
     * check if user exist in db when register (yes)
     */
    USER_ALREADY_ENABLED(5, "Hmm, something's off here. Please try to sign in."),
    USER_NOT_ENABLED(6, "User not enabled."),
    EMAIL_USER_WILL_CHANGE(7, "We sent you an email. Please confirm to continue."),
    CONTACT_WITH_US(8, "Hmm, something's off here. Please contact support for assistance."),
    WITHOUT_PERMISSION(10, "Without permission."),
    LINK_HAS_EXPIRED(11, "This link has expired. Please request a new one."),
    EMAIL_SEND_ERROR(12, "Email send error."),
    USER_EXIST(13, "Email already exists."),
    PARTNER_EXIST(14, "Email already exists."),
    PARTNER_ACTIVE_NOT_SIGNUP(15, "Your partner's account has been activated. Set up your password to sign in."),
    RESET_PASSWORD(16, "Your password isn’t secure. Please reset it!"),
    BLOG_NETWORK_ERROR(20, "Blog network error."),
    DEVICEID_ERROR(21, "Device id not equal."),
    USER_NOT_EXIST_IN_DB_AND_SHOPIFY(22, "need to redirect to register page"),
    USER_NOT_EXIST_IN_DB_BUT_EXIST_SHOPIFY(23, "need to redirect to register page and let user know she has an account in shopify, " +
            "she need to register with this email and we will use this email to create an app account."),
    PREGNANT_DUE_DATE_TOO_CLOSE(24, "Your due date is too close / too far away from last period data, please correct " +
            "it"),
    NO_PERIOD_NOT_ENTER(25, "You’ve marked your period as starting today. To switch to ‘No period’ mode in the Mira app, please remove your log before proceeding."),

    INTERNAL_SERVER_ERROR(500, "Something went wrong. We'll fix it soon. Please contact us.");

    private final int code;
    private final String msg;

    BizCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}

package com.mira.core.holder;

import java.util.HashMap;
import java.util.Map;

/**
 * 上下文保存
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class ContextHolder<T> {
    private final static ThreadLocal<Map<String, Object>> LOCAL_CONTEXT = new MapThreadLocal();

    public static boolean isEmpty() {
        return LOCAL_CONTEXT.get().size() == 0;
    }

    public static void put(String key, Object value) {
        LOCAL_CONTEXT.get().put(key, value);
    }

    public static <T> T get(String key) {
        return (T) LOCAL_CONTEXT.get().get(key);
    }

    public static void remove(String key) {
        LOCAL_CONTEXT.get().remove(key);
    }

    public static void removeAll() {
        LOCAL_CONTEXT.get().clear();
    }

    public static <T> void setLoginInfo(T t) {
        put("loginInfo", t);
    }

    public static <T> T getLoginInfo() {
        return (T) get("loginInfo");
    }

    private static class MapThreadLocal extends ThreadLocal<Map<String, Object>> {
        @Override
        protected Map<String, Object> initialValue() {
            return new HashMap<>();
        }
    }
}

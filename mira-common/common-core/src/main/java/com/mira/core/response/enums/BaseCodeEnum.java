package com.mira.core.response.enums;

/**
 * 基础响应编码
 *
 * <AUTHOR>
 */
public enum BaseCodeEnum implements ICodeEnum {
    OK(200, "OK"),
    BAD_REQUEST(400, "Bad Request"),
    UNAUTHORIZED(401, "Unauthorized"),
    FORBIDDEN(403, "Forbidden"),
    NOT_FOUND(404, "Not Found"),
    INTERNAL_SERVER_ERROR(500, "Internal Server Error");

    private final Integer code;
    private final String msg;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    BaseCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}

package com.mira.kafka.util;

import com.mira.core.util.JsonUtil;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * kafka util
 *
 * <AUTHOR>
 */
@Component
public class KafkaUtil {
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    private static KafkaUtil kafkaUtil;

    @PostConstruct
    public void init() {
        kafkaUtil = this;
    }

    public static void send(String topic, String json) {
        kafkaUtil.kafkaTemplate.send(topic, json);
    }

    public static void send(String topic, Object jsonObj) {
        send(topic, JsonUtil.toJson(jsonObj));
    }
}

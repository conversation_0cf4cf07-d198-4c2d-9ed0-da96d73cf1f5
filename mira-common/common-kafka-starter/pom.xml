<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.mira</groupId>
		<artifactId>mira-common</artifactId>
		<version>1.0</version>
	</parent>

	<artifactId>common-kafka-starter</artifactId>
	<version>${common.kafka.version}</version>
	<packaging>jar</packaging>
	<name>common-kafka</name>
	<description>Kafka 消息队列</description>

	<dependencies>
		<dependency>
			<groupId>com.mira</groupId>
			<artifactId>common-core</artifactId>
		</dependency>

		<!-- kafka -->
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
	</dependencies>

</project>

package com.mira.redis.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * redis 工具类，纯 string 操作
 * <p>一般用作手动操作的redis key</p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StringRedisComponent {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 检查key是否存在
     */
    public Boolean exists(String key) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return stringRedisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * 删除key
     */
    public Boolean delete(String key) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return stringRedisTemplate.delete(key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * 删除多个key
     */
    public Long delete(String... keys) {
        try {
            return stringRedisTemplate.delete(Arrays.stream(keys).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * string
     * 设置缓存
     */
    public void set(String key, String val) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        try {
            stringRedisTemplate.opsForValue().set(key, val);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * string
     * 设置缓存，过期时间
     */
    public void setEx(String key, String val, long timeout, TimeUnit unit) {
        if (!StringUtils.hasText(key)) {
            return;
        }
        try {
            stringRedisTemplate.opsForValue().set(key, val, timeout, unit);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * string
     * 获取缓存，字符串
     */
    public String get(String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return stringRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * set
     * 元素是否存在
     */
    public Boolean sIsmember(String key, String value) {
        if (!StringUtils.hasText(key)) {
            return Boolean.FALSE;
        }
        try {
            return stringRedisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }
}

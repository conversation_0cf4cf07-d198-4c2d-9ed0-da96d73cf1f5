<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mira</groupId>
        <artifactId>mira-common</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>common-mongo-starter</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>
    <name>common-mongo</name>
    <description>mongodb</description>

    <dependencies>
        <dependency>
            <groupId>com.mira</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <!-- Spring Boot Starter Data MongoDB -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <!-- If you are using reactive programming with MongoDB, you need this dependency as well -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
        </dependency>
    </dependencies>

</project>
package com.mira.mongo.util;

import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mongo.domain.BaseDomain;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-01-31
 **/
public class DomainTimeUtil {
    /**
     * 更新修改时间
     *
     * @param timeZone   时区
     * @param baseDomain 时间时区字段
     */
    public static void updateEntityTime(String timeZone, BaseDomain baseDomain) {
        long now = System.currentTimeMillis();
        baseDomain.setTimeZone(timeZone);
        baseDomain.setModifyTime(now);
        baseDomain.setModifyTimeStr(ZoneDateUtil.format(timeZone, now, DatePatternConst.DATE_TIME_PATTERN));
    }

    /**
     * 设置时间字段
     *
     * @param timeZone   时区
     * @param baseDomain 时间时区字段
     */
    public static void setEntityTime(String timeZone, BaseDomain baseDomain) {
        long now = System.currentTimeMillis();
        baseDomain.setTimeZone(timeZone);
        baseDomain.setCreateTime(now);
        baseDomain.setModifyTime(now);
        baseDomain.setCreateTimeStr(ZoneDateUtil.format(timeZone, now, DatePatternConst.DATE_TIME_PATTERN));
        baseDomain.setModifyTimeStr(baseDomain.getCreateTimeStr());
    }
}

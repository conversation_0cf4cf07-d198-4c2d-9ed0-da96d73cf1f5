package com.mira.rpc.config;

import feign.Retryer;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * auto configuration
 *
 * <AUTHOR>
 */
@Configuration
public class RpcAutoConfiguration {
    @Bean
    public Retryer retryer() {
        return new Retryer.Default();
    }

    @Bean
    public OkHttpClient okHttpClient() {
        ConnectionPool connectionPool = new ConnectionPool(100, 5, TimeUnit.MINUTES);
        return new OkHttpClient().newBuilder().connectionPool(connectionPool)
                // socket连接超时
                .connectTimeout(30, TimeUnit.SECONDS)
                // socket数据读取超时
                .readTimeout(60, TimeUnit.SECONDS)
                // socker数据写入超时
                .writeTimeout(30, TimeUnit.SECONDS)
                // 自动重连
                .retryOnConnectionFailure(true)
                .build();
    }
}

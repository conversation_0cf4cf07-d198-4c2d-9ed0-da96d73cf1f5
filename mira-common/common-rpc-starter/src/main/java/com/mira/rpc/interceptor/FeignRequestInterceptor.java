package com.mira.rpc.interceptor;

import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

/**
 * feign 请求拦截器
 *
 * <AUTHOR>
 */
@Component
public class FeignRequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
        // 匿名访问请求头
        requestTemplate.header(HeaderConst.ANONYMOUS, "anonymous");
        requestTemplate.header(HeaderConst.USER_TYPE, ContextHolder.<String>get(HeaderConst.USER_TYPE));
        // 时区
        requestTemplate.header(HeaderConst.TIME_ZONE, ContextHolder.<String>get(HeaderConst.TIME_ZONE));
        // 语言
        requestTemplate.header(HeaderConst.LOCAL_LANGUAGE, ContextHolder.<String>get(HeaderConst.LOCAL_LANGUAGE));
        // IP
        requestTemplate.header(HeaderConst.IP, ContextHolder.<String>get(HeaderConst.IP));
    }
}

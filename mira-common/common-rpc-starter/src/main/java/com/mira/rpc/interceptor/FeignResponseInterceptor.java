package com.mira.rpc.interceptor;

import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.response.CommonResult;
import com.mira.core.util.JsonUtil;
import feign.FeignException;
import feign.Response;
import feign.codec.DecodeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.HttpMessageConverterCustomizer;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * feign 响应拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FeignResponseInterceptor extends SpringDecoder {
    public FeignResponseInterceptor(ObjectFactory<HttpMessageConverters> messageConverters, ObjectProvider<HttpMessageConverterCustomizer> customizers) {
        super(messageConverters, customizers);
    }

    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        if (response.status() != HttpStatus.OK.value()) {
            throw new DecodeException(response.status(), response.reason(), response.request());
        }
        String body = IOUtils.toString(response.body().asReader(StandardCharsets.UTF_8));
        CommonResult result = JsonUtil.toObject(body, CommonResult.class);
        if (result.getCode() != BizCodeEnum.SUCCESS.getCode() && result.getCode() != HttpStatus.OK.value()) {
            log.error("FeignResponseInterceptor result error, body: {}", body);
            throw new DecodeException(result.getCode(), result.getMsg(), response.request());
        }

        return super.decode(response.toBuilder().body(body, StandardCharsets.UTF_8).build(), type);
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.mira</groupId>
		<artifactId>mira-common</artifactId>
		<version>1.0</version>
	</parent>

	<artifactId>common-rpc-starter</artifactId>
	<version>${common.rpc.version}</version>
	<packaging>jar</packaging>
	<name>common-rpc</name>
	<description>RPC 远程调用</description>

	<dependencies>
		<dependency>
			<groupId>com.mira</groupId>
			<artifactId>common-core</artifactId>
		</dependency>

		<!-- feign -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>

		<!-- loadbalancer -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
		</dependency>

		<!-- okhttp -->
		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-okhttp</artifactId>
		</dependency>
	</dependencies>

</project>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.mira</groupId>
		<artifactId>mira-dependencies</artifactId>
		<version>1.0</version>
		<relativePath/>
	</parent>

	<artifactId>mira-common</artifactId>
	<version>1.0</version>
	<packaging>pom</packaging>
	<name>mira-common</name>

	<properties>
		<maven.compiler.source>11</maven.compiler.source>
		<maven.compiler.target>11</maven.compiler.target>
	</properties>

	<modules>
		<module>common-core</module>
		<module>common-mybatis-starter</module>
		<module>common-redis-starter</module>
		<module>common-rpc-starter</module>
		<module>common-web-starter</module>
		<module>common-swagger-starter</module>
		<module>common-kafka-starter</module>
        <module>common-mongo-starter</module>
    </modules>

</project>

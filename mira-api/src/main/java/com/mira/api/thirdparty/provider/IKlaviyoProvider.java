package com.mira.api.thirdparty.provider;

import com.mira.api.thirdparty.consts.path.KlaviyoApiConst;
import com.mira.core.response.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * Klaviyo 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "mira-third-party", contextId = "klaviyo")
public interface IKlaviyoProvider {
    /**
     * app事件
     *
     * @param receiveMap 参数
     * @return String
     */
    @PostMapping(KlaviyoApiConst.APP_EVENT)
    CommonResult<String> appEvent(@RequestBody Map<String, Object> receiveMap);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.message.dal.mapper.SysNotificationRecordMapper">
    <select id="pageByUserIdAndDefineType" resultType="com.mira.message.dal.entity.SysNotificationRecordEntity">
        select r.id,
               r.user_id,
               r.notification_define_id,
               r.read,
               r.push_type,
               r.push_status,
               r.content,
               r.time_zone,
               r.expire_time,
               r.create_time_str
        from sys_notification_record r
        where r.user_id = #{userId}
        and r.deleted = 0
        and exists (select 1 from sys_notification_define
                    where define_id = r.notification_define_id
                    and type in
                        <foreach item="type" index="index" collection="notificaitonTypeList" open="(" separator="," close=")">
                            #{type}
                        </foreach>
                    )
        and (r.expire_time &gt; #{expireTime} or r.expire_time is null or r.expire_time=-1)
        order by r.id desc
        limit #{index},#{size}
    </select>

    <select id="countByUserIdAndDefineType" resultType="java.lang.Integer">
        select count(1)
        from sys_notification_record r
        where r.user_id = #{userId}
        and r.deleted = 0
        and exists (select 1 from sys_notification_define
                    where define_id = r.notification_define_id
                    and type in
                        <foreach item="type" index="index" collection="notificaitonTypeList" open="(" separator="," close=")">
                            #{type}
                        </foreach>
                    )
        and (r.expire_time &gt; #{expireTime} or r.expire_time is null or r.expire_time=-1)
    </select>

    <select id="isAllReadByUserId" resultType="java.lang.Integer">
        select count(1)
        from sys_notification_record record
            join sys_notification_define define
            on record.notification_define_id=define.define_id
        where record.user_id=#{userId}
        and record.deleted=0
        and record.`read` in (0, 2)
        and define.language='en-us'
        and (record.expire_time &gt; #{expireTime} or record.expire_time is null or record.expire_time=-1)
        and not (
            define.type in
                <foreach item="type" index="index" collection="notificaitonTypeList" open="(" separator="," close=")">
                    #{type}
                </foreach>
            and record.create_time &lt; UNIX_TIMESTAMP(CURDATE() - INTERVAL #{days} DAY) * 1000
        );
    </select>

    <select id="getByUserIdAndDefineType" resultType="com.mira.message.dal.entity.SysNotificationRecordEntity">
        select r.id,
               r.user_id,
               r.notification_define_id,
               r.read,
               r.push_type,
               r.push_status,
               r.content,
               r.time_zone,
               r.expire_time,
               r.create_time_str
        from sys_notification_record r
        where r.user_id = #{userId}
        and r.deleted = 0
        and r.id &lt; #{recordId}
        and exists (select 1 from sys_notification_define
                    where define_id = r.notification_define_id
                    and type in
                        <foreach item="type" index="index" collection="notificaitonTypeList" open="(" separator="," close=")">
                            #{type}
                        </foreach>
                    )
        and (r.expire_time &gt; #{expireTime} or r.expire_time is null or r.expire_time=-1)
        order by r.id desc
        limit 1
    </select>

    <select id="getSilent" resultType="com.mira.message.dal.entity.SysNotificationRecordEntity">
        select r.id,
               r.user_id,
               r.notification_define_id,
               r.read,
               r.push_type,
               r.push_status,
               r.content,
               r.time_zone,
               r.expire_time,
               r.create_time_str
        from sys_notification_record r
        where r.user_id = #{userId}
        and r.deleted = 0
        and r.read = 0
        and (r.expire_time &gt; #{expireTime} or r.expire_time is null or r.expire_time=-1)
        order by r.id desc
        limit 1
    </select>

    <update id="removeByUserIdAndCreateTime">
        update sys_notification_record r
        set r.deleted = 1
        where r.user_id = #{userId}
        and exists (select 1 from sys_notification_define
                    where define_id = r.notification_define_id
                    and type in
                        <foreach item="type" index="index" collection="notificaitonTypeList" open="(" separator="," close=")">
                            #{type}
                        </foreach>
                    )
        and r.create_time &lt; #{timestamp}
    </update>

    <update id="updateAllReadByUserId">
        update sys_notification_record r
        set r.read = 1
        where r.user_id = #{userId}
    </update>
</mapper>

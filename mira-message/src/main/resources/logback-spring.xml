<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>logback</contextName>
    <property name="log.path" value="/root/logs/mira-message/"/>
    <springProperty scope="context" name="server.port" source="server.port"/>
    <!-- 输出到控制台 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%yellow([%-4level]) [%d{HH:mm:ss}] [${server.port}] %X{traceId} --- [%cyan(%logger{36})] --- %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 输出到文件 -->
    <appender name="info_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <prudent>true</prudent>
        <file>${log.path}/info.log</file>
        <!-- 过滤器，只记录info -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- 输出格式 -->
        <encoder>
            <pattern>[%-4level] [%d{HH:mm:ss,Asia/Shanghai}] [${server.port}] %X{traceId} --- [%logger{36}] --- %msg%n</pattern>
        </encoder>
        <!-- 每天生成日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留1个月 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="error_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <prudent>true</prudent>
        <file>${log.path}/error.log</file>
        <!-- 过滤器，只记录error -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>[%-4level] [%d{HH:mm:ss,Asia/Shanghai}] [${server.port}] %X{traceId} --- [%logger{36}] --- %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留1个月 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="warn_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <prudent>true</prudent>
        <file>${log.path}/warn.log</file>
        <!-- 过滤器，只记录warn -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>[%-4level] [%d{HH:mm:ss,Asia/Shanghai}] [${server.port}] %X{traceId} --- [%logger{36}] --- %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/warn.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留1个月 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="info_file"/>
        <appender-ref ref="error_file"/>
        <appender-ref ref="warn_file"/>
    </root>

    <logger name="appendLogger" level="info" additivity="true">
        <appender-ref ref="error_file"/>
        <appender-ref ref="warn_file"/>
    </logger>

    <!-- kafka日志只打印错误信息，关闭打印使用off -->
    <logger name="org.apache.kafka" level="error" additivity="true"/>
    <logger name="org.springframework.kafka.listener" level="error" additivity="true"/>

    <!-- nacos client只打印错误日志 -->
    <logger name="com.alibaba.nacos.client" level="error" additivity="false"/>

    <!-- 打印sql参数 -->
<!--    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>-->
</configuration>
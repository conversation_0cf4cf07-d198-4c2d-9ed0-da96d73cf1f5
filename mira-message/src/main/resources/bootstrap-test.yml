spring:
  cloud:
    nacos:
      # 配置
      config:
        server-addr: 172.31.24.183:8848 # 地址
        namespace: 12d924dc-f63f-4b30-b1c2-c660a810bfb0 # 命名空间
        group: DEFAULT_GROUP # 组
        file-extension: yaml # 扩展文件格式
        # 通用配置
        shared-configs[0]:
          data_id: common.yaml
          group: DEFAULT_GROUP
          refresh: true
        # 字典表
        shared-configs[1]:
          data_id: sys_dict.yaml
          group: DEFAULT_GROUP
          refresh: true
        username: nacos
        password: <EMAIL>
      # 服务注册发现
      discovery:
        server-addr: 172.31.24.183:8848
        namespace: 12d924dc-f63f-4b30-b1c2-c660a810bfb0
        group: DEFAULT_GROUP
        username: nacos
        password: <EMAIL>
logging:
  config: classpath:logback/logback-${spring.profiles.active}.xml
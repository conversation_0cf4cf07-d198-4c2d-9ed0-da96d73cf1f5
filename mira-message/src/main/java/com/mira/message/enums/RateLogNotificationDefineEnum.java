package com.mira.message.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 好评邀请通知定义枚举
 *
 * <AUTHOR>
 */
@Getter
public enum RateLogNotificationDefineEnum {
    NO_COMMENT(26L, "We care about your opinion! How would you rate the experience of using Mira App?"),
    NO_CMMENT_AFTER_30_DAY(27L, "We're constantly trying to improve Mira App and your feedback means everything to us. How would you rate the experience of using Mira App?"),
    NO_CMMENT_AFTER_60_DAY(28L, "Since last time we asked, we tried to improve your experience using Mira App. How would you rate your experience now?")
    ;

    private final Long defineId;
    private final String title;

    RateLogNotificationDefineEnum(Long defineId, String title) {
        this.defineId = defineId;
        this.title = title;
    }

    public static RateLogNotificationDefineEnum get(Long defineId) {
        for (RateLogNotificationDefineEnum defineEnum : RateLogNotificationDefineEnum.values()) {
            if (defineEnum.getDefineId().equals(defineId)) {
                return defineEnum;
            }
        }
        return null;
    }

    public static List<Long> getDefineIdList() {
        return Arrays.stream(RateLogNotificationDefineEnum.values())
                .map(RateLogNotificationDefineEnum::getDefineId)
                .collect(Collectors.toList());
    }
}

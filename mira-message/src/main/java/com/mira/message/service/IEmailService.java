package com.mira.message.service;

import com.mira.api.clinic.dto.TenantDoctorDTO;
import com.mira.api.message.dto.CommonEmailDTO;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.api.user.dto.user.AppPartnerDTO;
import com.mira.api.user.dto.user.AppUserDTO;

import java.util.Map;

/**
 * 邮件服务
 *
 * <AUTHOR>
 */
public interface IEmailService {
    /**
     * 发送邮件
     *
     * @param appUserDTO    用户信息
     * @param emailTypeEnum 邮件模版类型
     * @param variableMap   邮件变量
     * @return true/false
     */
    boolean sendEmail(AppUserDTO appUserDTO, EmailTypeEnum emailTypeEnum, Map<String, String> variableMap);

    /**
     * 发送邮件
     *
     * @param appPartnerDTO Partner信息
     * @param emailTypeEnum 邮件模版类型
     * @param variableMap   邮件变量
     * @return true/false
     */
    boolean sendEmail(AppPartnerDTO appPartnerDTO, EmailTypeEnum emailTypeEnum, Map<String, String> variableMap);

    /**
     * 发送邮件
     *
     * @param tenantDoctorDTO 诊所用户信息
     * @param emailTypeEnum 邮件模版类型
     * @param variableMap   邮件变量
     * @return true/false
     */
    boolean sendEmail(TenantDoctorDTO tenantDoctorDTO, EmailTypeEnum emailTypeEnum, Map<String, String> variableMap);

    /**
     * 发送邮件
     *
     * @param commonEmailDTO 通用邮件传输类
     * @return true/false
     */
    boolean sendEmail(CommonEmailDTO commonEmailDTO);
}

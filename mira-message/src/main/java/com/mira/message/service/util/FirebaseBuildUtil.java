package com.mira.message.service.util;

import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.message.dal.entity.SysNotificationDefineEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;

/**
 * firebase push build util
 *
 * <AUTHOR>
 */
public class FirebaseBuildUtil {
    /**
     * hidden content switch: open
     * <p>
     * 隐藏通知内容开关打开后显示的内容
     */
    private final static String HIDDEN_CONTENT = "New message waiting for you in Mira App";

    /**
     * 构建Firebase推送数据
     *
     * @param sysNotificationDefine 通知定义
     * @param hiddenContent         隐藏通知内容
     * @return 推送数据
     */
    public static FirebasePushDTO buildPushNotification(SysNotificationDefineEntity sysNotificationDefine, boolean hiddenContent) {
        return buildProperty(sysNotificationDefine, hiddenContent);
    }

    /**
     * 构建Firebase推送数据
     *
     * @param sysNotificationDefine 通知定义
     * @param hiddenContent         隐藏通知内容
     * @param noButton1             去掉button1
     * @param noButton2             去掉button2
     * @return 推送数据
     */
    public static FirebasePushDTO buildPushNotification(SysNotificationDefineEntity sysNotificationDefine,
                                                        boolean hiddenContent,
                                                        boolean noButton1, boolean noButton2) {
        FirebasePushDTO firebasePushDTO = buildProperty(sysNotificationDefine, hiddenContent);
        if (noButton1) {
            firebasePushDTO.getDataMap().put("button1", "Got it");
            firebasePushDTO.getDataMap().put("button1Link", "close");
        }
        if (noButton2) {
            firebasePushDTO.getDataMap().put("button2", "Got it");
            firebasePushDTO.getDataMap().put("button2Link", "close");
        }

        return firebasePushDTO;
    }

    private static FirebasePushDTO buildProperty(SysNotificationDefineEntity sysNotificationDefine, boolean hiddenContent) {
        FirebasePushDTO firebasePushDTO = new FirebasePushDTO();
        firebasePushDTO.setDefineId(sysNotificationDefine.getDefineId());
        firebasePushDTO.setTitle(sysNotificationDefine.getTitle());
        firebasePushDTO.setBody(sysNotificationDefine.getContent());
        firebasePushDTO.setImage(sysNotificationDefine.getIcon());
        HashMap<String, String> dataMap = new HashMap<>();
        dataMap.put("button1", sysNotificationDefine.getButton1());
        dataMap.put("button1Link", sysNotificationDefine.getButton1Link());
        dataMap.put("extra1Link", StringUtils.isBlank(sysNotificationDefine.getExtra1Link())
                ? "" : sysNotificationDefine.getExtra1Link());
        dataMap.put("button2", sysNotificationDefine.getButton2());
        dataMap.put("button2Link", sysNotificationDefine.getButton2Link());
        dataMap.put("extra2Link", StringUtils.isBlank(sysNotificationDefine.getExtra2Link())
                ? "" : sysNotificationDefine.getExtra2Link());
        dataMap.put("pictureUrl", sysNotificationDefine.getPictureUrl());
        dataMap.put("content", sysNotificationDefine.getContent());
        dataMap.put("defineId", sysNotificationDefine.getDefineId().toString());
        dataMap.put("title", sysNotificationDefine.getTitle());
        dataMap.put("type", sysNotificationDefine.getType().toString());
        dataMap.put("styleType", sysNotificationDefine.getStyleType().toString());
        dataMap.put("expireTime", sysNotificationDefine.getExpireTime().toString());
        dataMap.put("backgroundImage", sysNotificationDefine.getBackgroundImage());
        dataMap.put("backgroundColor", sysNotificationDefine.getBackgroundColor());
        firebasePushDTO.setDataMap(dataMap);

        if (hiddenContent) {
            firebasePushDTO.setTitle(HIDDEN_CONTENT);
        }

        return firebasePushDTO;
    }
}

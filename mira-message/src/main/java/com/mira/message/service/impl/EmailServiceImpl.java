package com.mira.message.service.impl;

import com.mira.api.clinic.dto.TenantDoctorDTO;
import com.mira.api.message.dto.ClinicUserEmailDTO;
import com.mira.api.message.dto.CommonEmailDTO;
import com.mira.api.message.enums.EmailRoleEnum;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.api.user.dto.user.AppPartnerDTO;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.message.dal.dao.SysEmailLogDAO;
import com.mira.message.dal.dao.UserMailUnsubscribeDAO;
import com.mira.message.dal.entity.SysEmailLogEntity;
import com.mira.message.dal.entity.UserMailUnsubscribeEntity;
import com.mira.message.properties.EmailProperties;
import com.mira.message.service.IEmailService;
import com.mira.message.service.util.EmailSendUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;

/**
 * 邮件服务接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class EmailServiceImpl implements IEmailService {
    @Resource
    private SysEmailLogDAO sysEmailLogDAO;
    @Resource
    private UserMailUnsubscribeDAO userMailUnsubscribeDAO;

    @Resource
    private EmailProperties emailProperties;
    @Resource
    private SysDictProperties sysDictProperties;

    @Resource
    private EmailSendUtil emailSendUtil;

    @Override
    public boolean sendEmail(AppUserDTO appUserDTO, EmailTypeEnum emailTypeEnum, Map<String, String> variableMap) {
        // 邮件取消订阅地址
        variableMap.put("unsubscribeUrl", emailProperties.getUnsubscribeUrl());
        // 创建邮件模版
        String templateEngine = createMailTemplate(emailTypeEnum, variableMap);
        // 发送邮件
        boolean sendStatus = sendHtmlMail(new String[]{appUserDTO.getEmail()}, emailTypeEnum, templateEngine);
        // 新增发送记录
        CompletableFuture.supplyAsync(() -> saveEmailSendLog(appUserDTO.getEmail(), appUserDTO.getId(), sendStatus,
                appUserDTO.getTimeZone(), emailTypeEnum, EmailRoleEnum.USER), ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("save email send log failure", ex);
            return true;
        });

        return sendStatus;
    }

    @Override
    public boolean sendEmail(AppPartnerDTO appPartnerDTO, EmailTypeEnum emailTypeEnum, Map<String, String> variableMap) {
        // 创建邮件模版
        String templateEngine = createMailTemplate(emailTypeEnum, variableMap);
        // 发送邮件
        boolean sendStatus = sendHtmlMail(new String[]{appPartnerDTO.getEmail()}, emailTypeEnum, templateEngine);
        // 新增发送记录
        CompletableFuture.supplyAsync(() -> saveEmailSendLog(appPartnerDTO.getEmail(), appPartnerDTO.getId(), sendStatus,
                appPartnerDTO.getTimeZone(), emailTypeEnum, EmailRoleEnum.PARTNER), ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("save email send log failure", ex);
            return true;
        });

        return sendStatus;
    }

    @Override
    public boolean sendEmail(TenantDoctorDTO tenantDoctorDTO, EmailTypeEnum emailTypeEnum, Map<String, String> variableMap) {
        // 创建邮件模版
        String templateEngine = createMailTemplate(emailTypeEnum, variableMap);
        // 发送邮件
        boolean sendStatus = sendHtmlMail(new String[]{tenantDoctorDTO.getEmail()}, emailTypeEnum, templateEngine);
        // 新增发送记录
        CompletableFuture.supplyAsync(() -> saveEmailSendLog(tenantDoctorDTO.getEmail(), tenantDoctorDTO.getId(), sendStatus,
                tenantDoctorDTO.getTimeZone(), emailTypeEnum, EmailRoleEnum.PARTNER), ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("save email send log failure", ex);
            return true;
        });

        return sendStatus;
    }

    @Override
    public boolean sendEmail(CommonEmailDTO commonEmailDTO) {
        // 创建邮件模版
        String templateEngine = createMailTemplate(commonEmailDTO.getEmailTypeEnum(), commonEmailDTO.getEmailVariable());
        // 发送邮件
        List<String> toEmailList = commonEmailDTO.getToEmailList();
        String[] toEmailArray;
        if (CollectionUtils.isNotEmpty(toEmailList)) {
            toEmailArray = toEmailList.toArray(new String[0]);
        } else {
            toEmailArray = new String[]{commonEmailDTO.getToEmail()};
        }
        boolean sendStatus = sendHtmlMail(toEmailArray, commonEmailDTO.getEmailTypeEnum(), templateEngine);
        // 新增发送记录
        CompletableFuture.supplyAsync(() -> saveEmailSendLog(Arrays.toString(toEmailArray), commonEmailDTO.getId(), sendStatus,
                commonEmailDTO.getTimeZone(), commonEmailDTO.getEmailTypeEnum(), commonEmailDTO.getEmailRoleEnum()), ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("save email send log failure", ex);
            return true;
        });

        return sendStatus;
    }

    private Boolean checkUnsubscribe(Long userId, EmailTypeEnum emailTypeEnum) {
        UserMailUnsubscribeEntity userMailUnsubscribeEntity = userMailUnsubscribeDAO.getByUserIdAndTempCode(userId, emailTypeEnum.getCode());
        Predicate<UserMailUnsubscribeEntity> existPredicate = (o) -> Objects.nonNull(o) ? Boolean.TRUE : Boolean.FALSE;
        return existPredicate.test(userMailUnsubscribeEntity);
    }

    private String createMailTemplate(EmailTypeEnum emailTypeEnum, Map<String, String> variableMap) {
        // 创建邮件正文
        Context context = new Context();
        for (Map.Entry<String, String> entry : variableMap.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }

        switch (emailTypeEnum) {
            case INVITE_DOCTOR_EMAIL:
            case INVITE_NURSE_EMAIL:
            case INVITE_PATIENT_WITH_USER_EMAIL:
            case INVITE_PATIENT_WITHOUT_USER_EMAIL:
            case NURSE_PERIOD_STARTED:
            case NURSE_PERIOD_UPDATED:
            case NURSE_FIRST_TEST:
            case NURSE_HIGH_E3G:
            case NURSE_LH_SURGE:
            case NURSE_NO_OVULATION:
            case NURSE_POSITIVE_HCG:
            case NURSE_PREGNANT_LOG:
            case CLINIC_ACTIVATE_ACCOUNT:
            case CLINIC_INVITE_ACCOUNT:
            case CLINIC_RESET_PASSWORD:
                context.setVariable("emailTenantHost", emailProperties.getTenantHost());
                break;
            default:
                context.setVariable("emailTemplateHost", emailProperties.getTemplateHost());
                break;
        }

        TemplateEngine templateEngine = new TemplateEngine();
        ClassLoaderTemplateResolver templateResolver = new ClassLoaderTemplateResolver();
        templateResolver.setPrefix("templates/mail/");
        templateResolver.setSuffix(".html");
        templateResolver.setTemplateMode(TemplateMode.HTML);
        templateResolver.setCharacterEncoding("UTF-8");
        templateResolver.setOrder(0);
        templateEngine.setTemplateResolver(templateResolver);
        String template = emailTypeEnum.getTemplate();
        return templateEngine.process(template, context);
    }

    private boolean sendHtmlMail(String[] to, EmailTypeEnum emailTypeEnum, String templateEngine) {
        boolean sendStatus = true;
        try {
            emailSendUtil.send(to, templateEngine, emailTypeEnum.getSubject());
            log.info("send html mail success! email:{}, mailType:{}", to, emailTypeEnum.getTemplate());
        } catch (Exception e) {
            log.error("send html mail occurred unknown error! email:" + Arrays.toString(to) + ", mailType:" + emailTypeEnum.getTemplate(), e);
            sendStatus = false;
        }

        return sendStatus;
    }

    private boolean saveEmailSendLog(String email, Long userId, boolean sendStatus, String timeZone,
                                     EmailTypeEnum emailTypeEnum, EmailRoleEnum emailRoleEnum) {
        SysEmailLogEntity sysEmailLogEntity = new SysEmailLogEntity();
        sysEmailLogEntity.setEmail(email);
        sysEmailLogEntity.setUserId(userId);
        sysEmailLogEntity.setType(emailTypeEnum.getCode());
        sysEmailLogEntity.setSendStatus(sendStatus ? 1 : -1);
        sysEmailLogEntity.setRole(Objects.nonNull(emailRoleEnum) ? emailRoleEnum.getCode() : -1);
        UpdateEntityTimeUtil.setBaseEntityTime(StringUtils.isBlank(timeZone) ? sysDictProperties.getTimeZone() : timeZone, sysEmailLogEntity);
        return sysEmailLogDAO.save(sysEmailLogEntity);
    }
}

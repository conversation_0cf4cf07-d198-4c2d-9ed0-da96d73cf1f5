package com.mira.message.service.impl;

import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import com.mira.message.dal.dao.SysRateMenopauseLogDAO;
import com.mira.message.dal.entity.SysRateMenopauseLogEntity;
import com.mira.message.service.IRateMenopauseLogService;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-11-28
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class RateMenopauseLogServiceImpl implements IRateMenopauseLogService {
    private final SysRateMenopauseLogDAO sysRateMenopauseLogDAO;

    @Override
    public void rateMenopause(Long userId, Integer score) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        // 添加 rate menopause 记录
        SysRateMenopauseLogEntity rateMenopauseLogEntity = sysRateMenopauseLogDAO.getByUserId(userId);
        if (rateMenopauseLogEntity == null) {
            rateMenopauseLogEntity = new SysRateMenopauseLogEntity();
            rateMenopauseLogEntity.setUserId(userId);
            rateMenopauseLogEntity.setScore(score);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, rateMenopauseLogEntity);
            try {
                sysRateMenopauseLogDAO.save(rateMenopauseLogEntity);
            } catch (DuplicateKeyException e) {
                log.warn("user:{}, sys_rate_app_menopause insert duplicate", userId);
            }
        } else {
            rateMenopauseLogEntity.setScore(score);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, rateMenopauseLogEntity);
            sysRateMenopauseLogDAO.updateById(rateMenopauseLogEntity);
        }
    }

    @Override
    public Integer getRateMenopauseScore(Long userId) {
        SysRateMenopauseLogEntity rateMenopauseLogEntity = sysRateMenopauseLogDAO.getByUserId(userId);
        if (rateMenopauseLogEntity == null) {
            return null;
        }
        return rateMenopauseLogEntity.getScore();
    }
}

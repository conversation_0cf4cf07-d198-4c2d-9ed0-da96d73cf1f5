package com.mira.message.service;

import com.mira.api.message.dto.*;

/**
 * 通知消息接口
 *
 * <AUTHOR>
 */
public interface INotificationRecordService {
    /**
     * 保存消息记录
     *
     * @param pushNotificationDTO 推送通知
     */
    void firebaseAndRecord(PushNotificationDTO pushNotificationDTO);

    /**
     * 统计消息
     *
     * @param statisticsNotificationDTO 参数
     */
    void statistics(StatisticsNotificationDTO statisticsNotificationDTO);

    /**
     * 消息是否全部已读
     *
     * @param userId 用户id
     * @return true/false
     */
    boolean isAllRead(Long userId);

    /**
     * 获取通知列表
     *
     * @param userId  用户id
     * @param current 当前页
     * @param size    每页大小
     * @return 通知列表
     */
    PageNotificationRecordDTO getNotificationList(Long userId, Integer current, Integer size);

    /**
     * 获取通知列表
     *
     * @param queryNotificationDTO 查询参数
     * @return 通知列表
     */
    PageNotificationRecordDTO getNotificationList(QueryNotificationDTO queryNotificationDTO);

    /**
     * 获取诊所通知列表
     *
     * @param userId     用户id
     * @param tenantCode 租户编码
     * @param current    当前页
     * @param size       每页大小
     * @return 通知列表
     */
    ClinicPageNotificationRecordDTO getNotificationListByClinic(Long userId, String tenantCode, Integer current,
                                                                Integer size);

    /**
     * 静默通知最新一条
     *
     * @param userId 用户id
     * @return 静默通知
     */
    SysNotificationRecordDTO getSilent(Long userId);

    /**
     * 将用户所有消息设为已读
     *
     * @param userId 用户id
     */
    void updateAllRead(Long userId);

    /**
     * 将用户某条消息设为已读
     *
     * @param userId   用户id
     * @param recordId 消息id
     */
    void updateRead(Long userId, Long recordId);

    /**
     * 将用户某条消息设为未读
     *
     * @param userId   用户id
     * @param recordId 消息id
     */
    void updateUnRead(Long userId, Long recordId);

    /**
     * 根据消息定义id将用户所有消息设为已读
     *
     * @param userId   用户id
     * @param defineId 消息定义id
     */
    void updateReadByDefineId(Long userId, Long defineId);

    /**
     * 好评邀请评价
     *
     * @param userId 用户id
     * @param score  评分
     */
    void rateTheApp(Long userId, Integer score);

    /**
     * 过期掉消息，指定defineId
     *
     * @param userId    用户id
     * @param definedId 消息定义id
     */
    void expireNotification(Long userId, Long definedId);

    /**
     * 删除消息
     *
     * @param recordId id
     */
    void delete(Long recordId);

    /**
     * 删除消息后补充新记录
     *
     * @param userId        user id
     * @param aggregateType 消息类型
     * @param recordId      id
     * @return SysNotificationRecordDTO
     */
    SysNotificationRecordDTO supply(Long userId, Integer aggregateType, Long recordId);

    /**
     * 通知调研，评价分数
     *
     * @param notificationRateApiDTO 参数
     */
    void rateByNotification(NotificationRateApiDTO notificationRateApiDTO);
}

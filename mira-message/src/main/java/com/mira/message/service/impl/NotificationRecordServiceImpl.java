package com.mira.message.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.mira.api.clinic.dto.DoctorPatientDTO;
import com.mira.api.message.dto.*;
import com.mira.api.message.enums.NotificationAggregateTypeEnum;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.message.enums.PlatformEnum;
import com.mira.api.message.enums.PushTypeEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.LocalEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.request.PageDTO;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.StringListUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.message.dal.dao.*;
import com.mira.message.dal.entity.*;
import com.mira.message.enums.RateLogNotificationDefineEnum;
import com.mira.message.service.INotificationRecordService;
import com.mira.message.service.util.FirebaseBuildUtil;
import com.mira.message.service.util.FirebasePushUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通知消息接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotificationRecordServiceImpl implements INotificationRecordService {
    @Resource
    private SysNotificationRecordDAO sysNotificationRecordDAO;
    @Resource
    private SysNotificationDefineDAO sysNotificationDefineDAO;
    @Resource
    private ClinicNotificationRecordDAO clinicNotificationRecordDAO;
    @Resource
    private SysNotificationThresholdStatisticsDAO sysNotificationThresholdStatisticsDAO;
    @Resource
    private SysRateAppLogDAO sysRateAppLogDAO;
    @Resource
    private SysRateNotificationDAO sysRateNotificationDAO;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void firebaseAndRecord(PushNotificationDTO pushNotificationDTO) {
        // build push dto
        SysNotificationDefineEntity notificationDefineEntity = sysNotificationDefineDAO
                .getByDefineId(pushNotificationDTO.getDefineId(), LocalEnum.LOCAL_US.getValue());
        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(notificationDefineEntity,
                pushNotificationDTO.getHideContent(), pushNotificationDTO.getNoButton1(), pushNotificationDTO.getNoButton2());

        // handle special case
        handleSpecialCase(pushNotificationDTO, firebasePushDTO, notificationDefineEntity);

        // firebase
        if (pushNotificationDTO.getPushFirebase() && CollectionUtils.isNotEmpty(pushNotificationDTO.getTokens())) {
            if (pushNotificationDTO.getSilent()) {
                FirebasePushUtil.sendMultiPushBySilent(firebasePushDTO, pushNotificationDTO.getTokens());
            } else {
                FirebasePushUtil.sendMultiPush(firebasePushDTO, pushNotificationDTO.getTokens());
            }
        }

        // record
        if (pushNotificationDTO.getSaveRecord()) {
            Long id = saveSysNotificationRecord(pushNotificationDTO, firebasePushDTO);
            if (NotificationDefineEnum.CLINIC_NOTIFICATION.getDefineId().equals(pushNotificationDTO.getDefineId())) {
                DoctorPatientDTO doctorPatientDTO = JsonUtil.toObject(pushNotificationDTO.getExtend().split("@")[1], DoctorPatientDTO.class);
                saveClinicNotificationRecord(id, pushNotificationDTO, doctorPatientDTO, firebasePushDTO);
            }
        }
    }

    private void saveClinicNotificationRecord(Long id,
                                              PushNotificationDTO pushNotificationDTO,
                                              DoctorPatientDTO doctorPatientDTO,
                                              FirebasePushDTO firebasePushDTO) {
        ClinicNotificationRecordEntity clinicNotificationRecordEntity = new ClinicNotificationRecordEntity();
        clinicNotificationRecordEntity.setTenantCode(doctorPatientDTO.getTenantCode());
        clinicNotificationRecordEntity.setClinicName(pushNotificationDTO.getExtend());
        clinicNotificationRecordEntity.setUserId(pushNotificationDTO.getUserId());
        clinicNotificationRecordEntity.setDoctorId(doctorPatientDTO.getDoctorId());
        clinicNotificationRecordEntity.setNotificationRecordId(id);
        clinicNotificationRecordEntity.setRead(0);
        clinicNotificationRecordEntity.setContent(firebasePushDTO.getDataMap().get("content"));
        clinicNotificationRecordEntity.setTitle(firebasePushDTO.getDataMap().get("title"));
        clinicNotificationRecordEntity.setIcon(firebasePushDTO.getDataMap().get("pictureUrl"));
        UpdateEntityTimeUtil.setBaseEntityTime(pushNotificationDTO.getTimeZone(), clinicNotificationRecordEntity);
        clinicNotificationRecordDAO.save(clinicNotificationRecordEntity);
    }

    private Long saveSysNotificationRecord(PushNotificationDTO pushNotificationDTO, FirebasePushDTO firebasePushDTO) {
        SysNotificationRecordEntity sysNotificationRecordEntity = new SysNotificationRecordEntity();
        sysNotificationRecordEntity.setUserId(pushNotificationDTO.getUserId());
        sysNotificationRecordEntity.setNotificationDefineId(pushNotificationDTO.getDefineId());
        sysNotificationRecordEntity.setPushType(getPushType(pushNotificationDTO.getPlatform()));
        sysNotificationRecordEntity.setRead(0);
        sysNotificationRecordEntity.setContent(firebasePushDTO.getDataMap().get("content"));
        UpdateEntityTimeUtil.setBaseEntityTime(pushNotificationDTO.getTimeZone(), sysNotificationRecordEntity);
        if (Objects.nonNull(pushNotificationDTO.getExpireTime())) {
            sysNotificationRecordEntity.setExpireTime(pushNotificationDTO.getExpireTime());
        }
        sysNotificationRecordDAO.save(sysNotificationRecordEntity);
        return sysNotificationRecordEntity.getId();
    }

    private void handleSpecialCase(PushNotificationDTO pushNotificationDTO, FirebasePushDTO firebasePushDTO,
                                   SysNotificationDefineEntity notificationDefineEntity) {
        Long defineId = pushNotificationDTO.getDefineId();
        if (NotificationDefineEnum.CLINIC_NOTIFICATION.getDefineId().equals(defineId)) {
            String content = "You received a new message from your Doctor. Click here to read the message";
            firebasePushDTO.setTitle("Mira");
            firebasePushDTO.setImage(pushNotificationDTO.getIcon());
            firebasePushDTO.setBody(content);
            Map<String, String> dataMap = firebasePushDTO.getDataMap();
            dataMap.replace("pictureUrl", pushNotificationDTO.getIcon());
            dataMap.replace("content", pushNotificationDTO.getContent());
            dataMap.replace("title", pushNotificationDTO.getTitle());
            dataMap.put("clinicName", pushNotificationDTO.getExtend().split("@")[0]);
            String localtimeStr = ZoneDateUtil.format(pushNotificationDTO.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
            dataMap.put("createTime", localtimeStr);
        }

        if (NotificationDefineEnum.TOO_MANY_TEST_DATA.getDefineId().equals(defineId)) {
            Map<String, String> dataMap = firebasePushDTO.getDataMap();
            dataMap.put("content", notificationDefineEntity.getContent().replace("[N]", pushNotificationDTO.getExtend()));
            firebasePushDTO.setBody(dataMap.get("content"));
        }
        if (NotificationDefineEnum.CONFIRMATION_FOR_IRREGULAR_CYCLES.getDefineId().equals(defineId)) {
            Map<String, String> dataMap = firebasePushDTO.getDataMap();
            dataMap.put("content", notificationDefineEntity.getContent().replace("[X]",
                    pushNotificationDTO.getExtend()));
            firebasePushDTO.setBody(dataMap.get("content"));
        }
    }

    private Integer getPushType(Integer platform) {
        if (Objects.isNull(platform)) {
            return PlatformEnum.IOS.getPlatform();
        }
        return PlatformEnum.IOS.getPlatform().equals(platform) ? PushTypeEnum.IOS_PUSH.getType()
                : (PlatformEnum.ANDROID.getPlatform().equals(platform) ? PushTypeEnum.ANDROID_PUSH.getType() : -1);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void statistics(StatisticsNotificationDTO statisticsNotificationDTO) {
        Long userId = statisticsNotificationDTO.getUserId();
        String timeZone = statisticsNotificationDTO.getTimeZone();
        Long defineId = statisticsNotificationDTO.getDefineId();
        Long testAgainStartPushLine = statisticsNotificationDTO.getTestAgainStartPushLine();

        SysNotificationThresholdStatisticsEntity sysNotificationThresholdStatisticsEntity = sysNotificationThresholdStatisticsDAO.getByUserId(userId);
        if (ObjectUtils.isEmpty(sysNotificationThresholdStatisticsEntity)) {
            sysNotificationThresholdStatisticsEntity = new SysNotificationThresholdStatisticsEntity();
            sysNotificationThresholdStatisticsEntity.setUserId(userId);
            sysNotificationThresholdStatisticsEntity.setNotificationDefineId(defineId);
            sysNotificationThresholdStatisticsEntity.setReminderTime(testAgainStartPushLine);
            sysNotificationThresholdStatisticsEntity.setReminderTimeStr(
                    ZoneDateUtil.format(timeZone, testAgainStartPushLine, DatePatternConst.DATE_TIME_PATTERN));
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, sysNotificationThresholdStatisticsEntity);
            sysNotificationThresholdStatisticsDAO.save(sysNotificationThresholdStatisticsEntity);
        } else {
            sysNotificationThresholdStatisticsEntity.setNotificationDefineId(defineId);
            sysNotificationThresholdStatisticsEntity.setReminderTime(testAgainStartPushLine);
            sysNotificationThresholdStatisticsEntity.setReminderTimeStr(
                    ZoneDateUtil.format(timeZone, testAgainStartPushLine, DatePatternConst.DATE_TIME_PATTERN));
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, sysNotificationThresholdStatisticsEntity);
            sysNotificationThresholdStatisticsDAO.updateById(sysNotificationThresholdStatisticsEntity);
        }
    }

    @Override
    public boolean isAllRead(Long userId) {
        NotificationAggregateTypeEnum reminders = NotificationAggregateTypeEnum.REMINDERS;
        List<Integer> typeList = reminders.getTypeList();
        return sysNotificationRecordDAO.isAllReadByUserId(userId, 6, typeList);
    }

    private Map<Long, SysNotificationDefineEntity> getDefineMap() {
        String localLanguage = ContextHolder.get(HeaderConst.LOCAL_LANGUAGE);
        return sysNotificationDefineDAO.listByLanguage(localLanguage).stream()
                                       .collect(Collectors.toMap(SysNotificationDefineEntity::getDefineId, Function.identity()));
    }

    private PageNotificationRecordDTO convertNotificationDTO(List<SysNotificationRecordEntity> notificationRecordEntityList) {
        if (CollectionUtils.isEmpty(notificationRecordEntityList)) {
            return new PageNotificationRecordDTO();
        }
        Map<Long, SysNotificationDefineEntity> defineMap = getDefineMap();

        List<SysNotificationRecordDTO> notificationRecordDTOList = notificationRecordEntityList.stream().map(record -> {
            // 过期时间，优先级 record > define，先复制定义表字段值，再复制记录表字段值
            SysNotificationRecordDTO recordDTO = new SysNotificationRecordDTO();
            BeanUtil.copyProperties(defineMap.get(record.getNotificationDefineId()), recordDTO);
            BeanUtil.copyProperties(record, recordDTO, CopyOptions.create().setIgnoreNullValue(true));
            recordDTO.setRecordId(record.getId());
            return recordDTO;
        }).collect(Collectors.toList());

        for (SysNotificationRecordDTO sysNotificationRecordDTO : notificationRecordDTOList) {
            Long defineId = sysNotificationRecordDTO.getDefineId();
            if (Objects.equals(NotificationDefineEnum.CLINIC_NOTIFICATION.getDefineId(), defineId)) {
                ClinicNotificationRecordEntity clinicNotificationRecord = clinicNotificationRecordDAO
                        .getByNotificationRecordId(sysNotificationRecordDTO.getRecordId());
                if (ObjectUtils.isNotEmpty(clinicNotificationRecord)) {
                    sysNotificationRecordDTO.setTitle(clinicNotificationRecord.getTitle());
                    sysNotificationRecordDTO.setContent(clinicNotificationRecord.getContent());
                    sysNotificationRecordDTO.setIcon(clinicNotificationRecord.getIcon());
                }
            }
        }

        PageNotificationRecordDTO pageNotificationRecordDTO = new PageNotificationRecordDTO();
        pageNotificationRecordDTO.setList(notificationRecordDTOList);

        return pageNotificationRecordDTO;
    }

    @Override
    public PageNotificationRecordDTO getNotificationList(Long userId, Integer current, Integer size) {
        int index = current * size;
        List<SysNotificationRecordEntity> notificationRecordEntityList = sysNotificationRecordDAO.pageByUserId(userId, index, size);
        PageNotificationRecordDTO pageNotificationRecordDTO = convertNotificationDTO(notificationRecordEntityList);
        pageNotificationRecordDTO.setTotal(sysNotificationRecordDAO.countByUserId(userId));
        return pageNotificationRecordDTO;
    }

    @Override
    public PageNotificationRecordDTO getNotificationList(QueryNotificationDTO queryNotificationDTO) {
        Long userId = queryNotificationDTO.getUserId();
        PageDTO pageDTO = queryNotificationDTO.getPageDTO();

        NotificationAggregateTypeEnum aggregateTypeEnum = NotificationAggregateTypeEnum.get(queryNotificationDTO.getAggregateType());
        List<Integer> notificaitonTypeList = aggregateTypeEnum.getTypeList();

        // 删除已过期并且超过1周的测试提醒
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String nowDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        Long timestamp = ZoneDateUtil.timestamp(timeZone, LocalDateUtil.plusDay(nowDate, -6,
                DatePatternConst.DATE_PATTERN), DatePatternConst.DATE_PATTERN);
        sysNotificationRecordDAO.removeByUserIdAndCreateTime(userId, NotificationAggregateTypeEnum.REMINDERS.getTypeList(), timestamp);

        Long now = System.currentTimeMillis();
        List<SysNotificationRecordEntity> notificationRecordEntityList = sysNotificationRecordDAO
                .pageByUserIdAndDefineType(userId, pageDTO, notificaitonTypeList, now);
        PageNotificationRecordDTO pageNotificationRecordDTO = convertNotificationDTO(notificationRecordEntityList);
        pageNotificationRecordDTO.setTotal(sysNotificationRecordDAO
                .countByUserIdAndDefineType(userId, notificaitonTypeList, now));
        return pageNotificationRecordDTO;
    }

    @Override
    public ClinicPageNotificationRecordDTO getNotificationListByClinic(Long userId, String tenantCode, Integer current, Integer size) {
        int index = (current - 1) * size;

        List<ClinicNotificationRecordDTO> recordDTOList = clinicNotificationRecordDAO
                .pageByUserId(userId, tenantCode, index, size).stream()
                .map(record -> BeanUtil.toBean(record, ClinicNotificationRecordDTO.class, CopyOptions.create().setIgnoreNullValue(true)))
                .collect(Collectors.toList());
        ClinicPageNotificationRecordDTO result = new ClinicPageNotificationRecordDTO();
        result.setTotal(CollectionUtils.isEmpty(recordDTOList) ? 0 : recordDTOList.size());
        result.setList(recordDTOList);

        return result;
    }

    @Override
    public SysNotificationRecordDTO getSilent(Long userId) {
        SysNotificationRecordEntity silentRecord = sysNotificationRecordDAO.getSilent(userId, System.currentTimeMillis());
        if (silentRecord == null) {
            return null;
        }

        SysNotificationDefineEntity defineEntity = sysNotificationDefineDAO.getByDefineId(silentRecord.getNotificationDefineId(), LocalEnum.LOCAL_US.getValue());
        SysNotificationRecordDTO recordDTO = new SysNotificationRecordDTO();
        BeanUtil.copyProperties(defineEntity, recordDTO);
        BeanUtil.copyProperties(silentRecord, recordDTO, CopyOptions.create().setIgnoreNullValue(true));
        recordDTO.setRecordId(silentRecord.getId());
        return recordDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAllRead(Long userId) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        List<Long> clinicNotificationRecordIds = new ArrayList<>();

        // 更新系统通知记录
        List<SysNotificationRecordEntity> recordEntityList = sysNotificationRecordDAO.listByUnRead(userId);
        for (SysNotificationRecordEntity recordEntity : recordEntityList) {
            recordEntity.setRead(1);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, recordEntity);
            if (Objects.equals(NotificationDefineEnum.CLINIC_NOTIFICATION.getDefineId(), recordEntity.getNotificationDefineId())) {
                clinicNotificationRecordIds.add(recordEntity.getId());
            }
        }

        if (CollectionUtils.isNotEmpty(recordEntityList)) {
            sysNotificationRecordDAO.updateBatchById(recordEntityList);
        }

        // 更新诊所通知记录
        if (CollectionUtils.isNotEmpty(clinicNotificationRecordIds)) {
            List<ClinicNotificationRecordEntity> notificationRecordEntityList = clinicNotificationRecordDAO
                    .listByNotificationRecordIds(clinicNotificationRecordIds);
            for (ClinicNotificationRecordEntity notificationRecordEntity : notificationRecordEntityList) {
                notificationRecordEntity.setRead(1);
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, notificationRecordEntity);
            }
            if (CollectionUtils.isNotEmpty(notificationRecordEntityList)) {
                clinicNotificationRecordDAO.updateBatchById(notificationRecordEntityList);
            }
        }
    }

    @Override
    public void updateRead(Long userId, Long recordId) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // -1 全部已读
        if (-1 == recordId) {
            sysNotificationRecordDAO.updateAllReadByUserId(userId);
            return;
        }

        SysNotificationRecordEntity recordEntity = sysNotificationRecordDAO.getById(recordId);
        if (recordEntity == null) {
            return;
        }

        recordEntity.setRead(1);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, recordEntity);
        sysNotificationRecordDAO.updateById(recordEntity);

        // 添加 rate log 记录
        NotificationDefineEnum defineEnum = NotificationDefineEnum.get(recordEntity.getNotificationDefineId());
        if (defineEnum != null) {
            addRateAppLog(userId, timeZone, -1);
        }

        // 更新诊所通知记录
        if (Objects.equals(NotificationDefineEnum.CLINIC_NOTIFICATION.getDefineId(), recordEntity.getNotificationDefineId())) {
            ClinicNotificationRecordEntity notificationRecordEntity = clinicNotificationRecordDAO.getByNotificationRecordId(recordId);
            if (notificationRecordEntity != null) {
                notificationRecordEntity.setRead(1);
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, notificationRecordEntity);
                clinicNotificationRecordDAO.updateById(notificationRecordEntity);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUnRead(Long userId, Long recordId) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        SysNotificationRecordEntity recordEntity = sysNotificationRecordDAO.getById(recordId);
        if (recordEntity == null) {
            return;
        }

        recordEntity.setRead(2);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, recordEntity);
        sysNotificationRecordDAO.updateById(recordEntity);

        // 更新诊所通知记录
        if (Objects.equals(NotificationDefineEnum.CLINIC_NOTIFICATION.getDefineId(), recordEntity.getNotificationDefineId())) {
            ClinicNotificationRecordEntity notificationRecordEntity = clinicNotificationRecordDAO.getByNotificationRecordId(recordId);
            if (notificationRecordEntity != null) {
                notificationRecordEntity.setRead(2);
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, notificationRecordEntity);
                clinicNotificationRecordDAO.updateById(notificationRecordEntity);
            }
        }
    }

    public void addRateAppLog(Long userId, String timeZone, Integer score) {
        SysRateAppLogEntity rateAppLogEntity = sysRateAppLogDAO.getByUserId(userId);
        if (rateAppLogEntity == null) {
            rateAppLogEntity = new SysRateAppLogEntity();
            rateAppLogEntity.setUserId(userId);
            rateAppLogEntity.setScore(score);
            rateAppLogEntity.setScoreHis(String.valueOf(score));
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, rateAppLogEntity);
            try {
                sysRateAppLogDAO.save(rateAppLogEntity);
            } catch (DuplicateKeyException e) {
                log.warn("user:{}, sys_rate_app_log insert duplicate", userId);
            }
        } else {
            rateAppLogEntity.setScore(score);
            List<Integer> newScoreHis = StringListUtil.strToIntegerList(rateAppLogEntity.getScoreHis(), ",");
            if (newScoreHis.size() >= 50) {
                newScoreHis.remove(0);
            }
            newScoreHis.add(score);
            rateAppLogEntity.setScoreHis(StringListUtil.listToString(newScoreHis, ","));
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, rateAppLogEntity);
            sysRateAppLogDAO.updateById(rateAppLogEntity);
        }
    }

    @Override
    public void updateReadByDefineId(Long userId, Long defineId) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        List<SysNotificationRecordEntity> recordEntities = sysNotificationRecordDAO.listByUserIdAndDefineId(userId, defineId);
        for (SysNotificationRecordEntity recordEntity : recordEntities) {
            recordEntity.setRead(1);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, recordEntity);
        }
        if (CollectionUtils.isNotEmpty(recordEntities)) {
            sysNotificationRecordDAO.updateBatchById(recordEntities);
        }
        List<Long> recordIds = recordEntities.stream()
                                             .map(SysNotificationRecordEntity::getId).collect(Collectors.toList());

        // 添加 rate log 记录
        addRateAppLog(userId, timeZone, -1);

        // 更新诊所通知记录
        if (Objects.equals(NotificationDefineEnum.CLINIC_NOTIFICATION.getDefineId(), defineId)) {
            List<ClinicNotificationRecordEntity> notificationRecordEntitys = clinicNotificationRecordDAO.listByNotificationRecordIds(recordIds);
            if (CollectionUtils.isNotEmpty(notificationRecordEntitys)) {
                for (ClinicNotificationRecordEntity notificationRecordEntity : notificationRecordEntitys) {
                    notificationRecordEntity.setRead(1);
                    UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, notificationRecordEntity);
                }
                clinicNotificationRecordDAO.updateBatchById(notificationRecordEntitys);
            }
        }

        List<Long> defineIds = new ArrayList<>();
        if (NotificationDefineEnum.HELP_MIRA_GET_BETTER.getDefineId().equals(defineId)) {
            defineIds.add(NotificationDefineEnum.WE_MISS_YOU.getDefineId());
            defineIds.add(NotificationDefineEnum.RETURN_TO_APP.getDefineId());
        }
        if (NotificationDefineEnum.RETURN_TO_APP.getDefineId().equals(defineId)) {
            defineIds.add(NotificationDefineEnum.WE_MISS_YOU.getDefineId());
        }

        if (CollectionUtils.isNotEmpty(defineIds)) {
            List<SysNotificationRecordEntity> sysNotificationRecordEntities = sysNotificationRecordDAO.listUnReadInDefineIds(userId, defineIds);
            if (CollectionUtils.isNotEmpty(sysNotificationRecordEntities)) {
                for (SysNotificationRecordEntity sysNotificationRecordEntity : sysNotificationRecordEntities) {
                    sysNotificationRecordEntity.setRead(1);
                    UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, sysNotificationRecordEntity);
                }
                sysNotificationRecordDAO.updateBatchById(sysNotificationRecordEntities);
            }
        }
    }

    @Override
    public void rateTheApp(Long userId, Integer score) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // 添加 rate log 记录
        addRateAppLog(userId, timeZone, score);

        List<Long> rateAppDefineIds = RateLogNotificationDefineEnum.getDefineIdList();
        List<SysNotificationRecordEntity> recordEntities = sysNotificationRecordDAO.listUnReadInDefineIds(userId, rateAppDefineIds);
        for (SysNotificationRecordEntity recordEntity : recordEntities) {
            recordEntity.setRead(1);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, recordEntity);
        }
        if (CollectionUtils.isNotEmpty(recordEntities)) {
            sysNotificationRecordDAO.updateBatchById(recordEntities);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void expireNotification(Long userId, Long definedId) {
        long now = System.currentTimeMillis();
        List<SysNotificationRecordEntity> recordEntities = sysNotificationRecordDAO.listByUserIdAndDefineId(userId, definedId);
        for (SysNotificationRecordEntity recordEntity : recordEntities) {
            recordEntity.setExpireTime(now);
        }
        if (CollectionUtils.isNotEmpty(recordEntities)) {
            sysNotificationRecordDAO.updateBatchById(recordEntities);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long recordId) {
        sysNotificationRecordDAO.removeById(recordId);
    }

    @Override
    public SysNotificationRecordDTO supply(Long userId, Integer aggregateType, Long recordId) {
        NotificationAggregateTypeEnum aggregateTypeEnum = NotificationAggregateTypeEnum.get(aggregateType);
        List<Integer> notificaitonTypeList = aggregateTypeEnum.getTypeList();

        SysNotificationRecordEntity resultEntity = sysNotificationRecordDAO.getByUserIdAndDefineType(userId, notificaitonTypeList, recordId);
        if (resultEntity == null) {
            return null;
        }
        Map<Long, SysNotificationDefineEntity> defineMap = getDefineMap();
        // 过期时间，优先级 record > define，先复制定义表字段值，再复制记录表字段值
        SysNotificationRecordDTO recordDTO = new SysNotificationRecordDTO();
        BeanUtil.copyProperties(defineMap.get(resultEntity.getNotificationDefineId()), recordDTO);
        BeanUtil.copyProperties(resultEntity, recordDTO, CopyOptions.create().setIgnoreNullValue(true));
        recordDTO.setRecordId(resultEntity.getId());

        return recordDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void rateByNotification(NotificationRateApiDTO notificationRateApiDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        Long userId = notificationRateApiDTO.getUserId();
        Integer type = notificationRateApiDTO.getType();
        Integer score = notificationRateApiDTO.getScore();

        try {
            SysRateNotificationEntity newEntity = new SysRateNotificationEntity();
            newEntity.setUserId(userId);
            newEntity.setType(type);
            newEntity.setScore(score);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, newEntity);
            sysRateNotificationDAO.save(newEntity);
        } catch (DuplicateKeyException e) {
            log.info("user:{}, type:{}, score:{}, rate notification record already exists, updating", userId, type, score);
            SysRateNotificationEntity existingEntity = sysRateNotificationDAO.getByUserIdAndType(userId, type);
            if (existingEntity != null) {
                existingEntity.setScore(score);
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, existingEntity);
                sysRateNotificationDAO.updateById(existingEntity);
            }
        }
    }
}

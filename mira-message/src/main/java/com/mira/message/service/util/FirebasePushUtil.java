package com.mira.message.service.util;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.*;
import com.mira.api.message.dto.FirebasePushDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * firebase push util
 */
@Slf4j
@Component
public class FirebasePushUtil {
    public FirebasePushUtil() {
        Resource resource = new ClassPathResource("mira-a0908-firebase-adminsdk-9wd94-2c7318f0a8.json");
        FirebaseOptions options = null;
        try {
            options = FirebaseOptions.builder()
                    .setCredentials(GoogleCredentials.fromStream(resource.getInputStream()))
                    .setDatabaseUrl("https://mira-a0908.firebaseio.com")
                    .build();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        FirebaseApp.initializeApp(options);
    }

    /**
     * This registration token comes from the client FCM SDKs.
     */
    public static void sendMultiPush(FirebasePushDTO pushDTO, List<String> registrationTokens) {
        BatchResponse batchResponse = null;

        // See documentation on defining a message payload.
        MulticastMessage multicastMessage = MulticastMessage.builder()
                .setNotification(Notification.builder()
                        .setTitle(pushDTO.getTitle())
                        .setBody(pushDTO.getBody())
                        .setImage(pushDTO.getImage())
                        .build()
                )
                .setAndroidConfig(AndroidConfig.builder()
                        .setPriority(AndroidConfig.Priority.NORMAL)
                        .setNotification(AndroidNotification.builder()
                                .setTitle(pushDTO.getTitle())
                                .setBody(pushDTO.getBody())
                                .setImage(pushDTO.getImage())
                                .build())
                        .build())
                .setApnsConfig(ApnsConfig.builder()
                        .setAps(Aps.builder()
                                .setBadge(1)
                                .setAlert(ApsAlert.builder()
                                        .setTitle(pushDTO.getTitle())
                                        .setBody(pushDTO.getBody())
                                        .build())
                                .build())
                        .setFcmOptions(ApnsFcmOptions.builder()
                                .setImage(pushDTO.getImage())
                                .build())
                        .build())
                .putAllData(pushDTO.getDataMap())
                .addAllTokens(registrationTokens)
                .build();

        // Send a message to the device corresponding to the provided
        // registration token.
        FirebaseMessaging firebaseMessaging = FirebaseMessaging.getInstance();
        try {
            batchResponse = firebaseMessaging.sendEachForMulticast(multicastMessage);
            if (batchResponse.getFailureCount() > 0) {
                List<SendResponse> responses = batchResponse.getResponses();
                List<String> failedTokens = new ArrayList<>();
                for (int i = 0; i < responses.size(); i++) {
                    if (!responses.get(i).isSuccessful()) {
                        // The order of responses corresponds to the order of the registration tokens.
                        failedTokens.add(registrationTokens.get(i));
                    }
                }
            }
        } catch (Exception e) {
            log.error("sendMultiPush Exception error:{}", e.getMessage(), e);
        }

        // Response is a message ID string.
        if (batchResponse != null) {
            log.info("sent message end: successCount:{}, failureCount:{} ", batchResponse.getSuccessCount(), batchResponse.getFailureCount());
        }
    }

    public static void sendMultiPushBySilent(FirebasePushDTO pushDTO, List<String> registrationTokens) {
        BatchResponse batchResponse = null;

        // See documentation on defining a message payload.
        MulticastMessage multicastMessage = MulticastMessage.builder()
                .setAndroidConfig(AndroidConfig.builder()
                        .setPriority(AndroidConfig.Priority.NORMAL)
                        .build())
                .setApnsConfig(ApnsConfig.builder()
                        .setAps(Aps.builder()
                                .setContentAvailable(true)
                                .build())
                        .build())
                .putAllData(pushDTO.getDataMap())
                .addAllTokens(registrationTokens)
                .build();

        // Send a message to the device corresponding to the provided
        // registration token.
        FirebaseMessaging firebaseMessaging = FirebaseMessaging.getInstance();
        try {
            batchResponse = firebaseMessaging.sendEachForMulticast(multicastMessage);
            if (batchResponse.getFailureCount() > 0) {
                List<SendResponse> responses = batchResponse.getResponses();
                List<String> failedTokens = new ArrayList<>();
                for (int i = 0; i < responses.size(); i++) {
                    if (!responses.get(i).isSuccessful()) {
                        // The order of responses corresponds to the order of the registration tokens.
                        failedTokens.add(registrationTokens.get(i));
                    }
                }
            }
        } catch (Exception e) {
            log.error("sendMultiPush Exception error:{}", e.getMessage(), e);
        }

        // Response is a message ID string.
        if (batchResponse != null) {
            log.info("sent message end: successCount:{}, failureCount:{} ", batchResponse.getSuccessCount(), batchResponse.getFailureCount());
        }
    }
}

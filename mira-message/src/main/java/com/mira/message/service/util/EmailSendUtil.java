package com.mira.message.service.util;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder;
import com.amazonaws.services.simpleemail.model.*;
import com.mira.message.properties.AwsSesProperties;
import com.mira.message.properties.EmailProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * email send util
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class EmailSendUtil {
    private final AwsSesProperties awsSesProperties;
    private final EmailProperties emailProperties;

    public EmailSendUtil(AwsSesProperties awsSesProperties, EmailProperties emailProperties) {
        this.awsSesProperties = awsSesProperties;
        this.emailProperties = emailProperties;
    }

    public void send(String[] to, String body, String subject) {
        AWSCredentials credentials = new BasicAWSCredentials(awsSesProperties.getAccessKey(), awsSesProperties.getSecretKey());
        AWSCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setMaxErrorRetry(awsSesProperties.getMaxErrorRetry());

        AmazonSimpleEmailService client = AmazonSimpleEmailServiceClientBuilder.standard()
                .withClientConfiguration(clientConfiguration)
                .withCredentials(credentialsProvider)
                .withRegion(awsSesProperties.getRegion()).build();
        SendEmailRequest request = new SendEmailRequest()
                .withDestination(new Destination().withToAddresses(to))
                .withMessage(new Message()
                        .withBody(new Body().withHtml(new Content().withCharset("UTF-8").withData(body)))
                        .withSubject(new Content().withCharset("UTF-8").withData(subject)))
                .withSource(emailProperties.getFromNickname() + "<" + emailProperties.getFromEmail() + ">");
        client.sendEmail(request);
    }
}

package com.mira.message.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.core.request.PageDTO;
import com.mira.message.dal.entity.SysNotificationRecordEntity;
import com.mira.message.dal.mapper.SysNotificationRecordMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * sys_notification_record DAO
 *
 * <AUTHOR>
 */
@Repository
public class SysNotificationRecordDAO extends ServiceImpl<SysNotificationRecordMapper, SysNotificationRecordEntity> {
    public boolean isAllReadByUserId(Long userId, Integer days,
                                     List<Integer> notificaitonTypeList) {
        Long now = System.currentTimeMillis();
        Integer count = getBaseMapper().isAllReadByUserId(userId, days, notificaitonTypeList, now);
        return count == 0;
    }

    public long countByUserId(Long userId) {
        return count(Wrappers.<SysNotificationRecordEntity>lambdaQuery()
                .eq(SysNotificationRecordEntity::getUserId, userId));
    }

    public List<SysNotificationRecordEntity> listByUserIdAndDefineId(Long userId, Long defineId) {
        return list(Wrappers.<SysNotificationRecordEntity>lambdaQuery()
                .eq(SysNotificationRecordEntity::getUserId, userId)
                .eq(SysNotificationRecordEntity::getNotificationDefineId, defineId));
    }

    public List<SysNotificationRecordEntity> listUnReadInDefineIds(Long userId, List<Long> defineIds) {
        return list(Wrappers.<SysNotificationRecordEntity>lambdaQuery()
                .eq(SysNotificationRecordEntity::getUserId, userId)
                .eq(SysNotificationRecordEntity::getRead, 0)
                .in(SysNotificationRecordEntity::getNotificationDefineId, defineIds));
    }

    public List<SysNotificationRecordEntity> pageByUserId(Long userId, int index, int pageSize) {
        return list(Wrappers.<SysNotificationRecordEntity>lambdaQuery()
                .eq(SysNotificationRecordEntity::getUserId, userId)
                .orderByDesc(SysNotificationRecordEntity::getCreateTime)
                .last("limit " + index + "," + pageSize));
    }

    public List<SysNotificationRecordEntity> pageByUserIdAndDefineType(Long userId, PageDTO pageDTO,
                                                                       List<Integer> notificaitonTypeList,
                                                                       Long now) {
        Integer index = pageDTO.getCurrent() * pageDTO.getSize();
        return getBaseMapper().pageByUserIdAndDefineType(userId, index, pageDTO.getSize(), notificaitonTypeList, now);
    }

    public SysNotificationRecordEntity getSilent(Long userId, Long now) {
        return getBaseMapper().getSilent(userId, now);
    }

    public Integer countByUserIdAndDefineType(Long userId,
                                              List<Integer> notificaitonTypeList,
                                              Long now) {
        return getBaseMapper().countByUserIdAndDefineType(userId, notificaitonTypeList, now);
    }

    public SysNotificationRecordEntity getByUserIdAndDefineType(Long userId,
                                                                List<Integer> notificaitonTypeList,
                                                                Long recordId) {
        return getBaseMapper().getByUserIdAndDefineType(userId, notificaitonTypeList, recordId, System.currentTimeMillis());
    }

    public List<SysNotificationRecordEntity> listByUnRead(Long userId) {
        return list(Wrappers.<SysNotificationRecordEntity>lambdaQuery()
                .eq(SysNotificationRecordEntity::getUserId, userId)
                .eq(SysNotificationRecordEntity::getRead, 0));
    }

    public void removeByUserIdAndCreateTime(Long userId, List<Integer> notificaitonTypeList, Long timestamp) {
        getBaseMapper().removeByUserIdAndCreateTime(userId, notificaitonTypeList, timestamp);
    }

    public void updateAllReadByUserId(Long userId) {
        getBaseMapper().updateAllReadByUserId(userId);
    }
}

package com.mira.message.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户邮件发送记录表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("sys_email_log")
public class SysEmailLogEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 邮件类型
     */
    private Integer type;

    /**
     * 发送状态：1 成功，-1 失败
     */
    private Integer sendStatus;

    /**
     * 角色：0 user(默认)，1 partner，2 doctor，3 nurse
     */
    private Integer role;
}

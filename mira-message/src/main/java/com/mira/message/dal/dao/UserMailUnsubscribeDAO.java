package com.mira.message.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.message.dal.entity.UserMailUnsubscribeEntity;
import com.mira.message.dal.mapper.UserMailUnsubscribeMapper;
import org.springframework.stereotype.Repository;

/**
 * app_user DAO
 *
 * <AUTHOR>
 */
@Repository
public class UserMailUnsubscribeDAO extends ServiceImpl<UserMailUnsubscribeMapper, UserMailUnsubscribeEntity> {
    public UserMailUnsubscribeEntity getByUserIdAndTempCode(Long userId, Integer templateCode) {
        return getOne(Wrappers.<UserMailUnsubscribeEntity>lambdaQuery()
                .eq(UserMailUnsubscribeEntity::getUserId, userId)
                .in(UserMailUnsubscribeEntity::getMailTemplateCode, templateCode, -1)
                .last("limit 1"));
    }
}

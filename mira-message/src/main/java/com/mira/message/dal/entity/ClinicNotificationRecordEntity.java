package com.mira.message.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 诊所系统通知记录
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("clinic_notification_record")
public class ClinicNotificationRecordEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * 诊所名称
     */
    private String clinicName;

    /**
     * 租户管理人员id
     */
    private Long doctorId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 通知记录id
     */
    private Long notificationRecordId;

    /**
     * 是否已读：0-未读, 1-已读, 2-由已读重新标记为未读
     */
    @TableField("`read`")
    private Integer read;

    /**
     * 推送的文字内容
     */
    private String content;

    /**
     * 标题
     */
    private String title;

    /**
     * icon
     */
    private String icon;


}

package com.mira.message.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户好评邀请评价记录
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("sys_rate_app_log")
public class SysRateAppLogEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * score
     */
    private Integer score;

    /**
     * 历史得分列表
     */
    private String scoreHis;
}

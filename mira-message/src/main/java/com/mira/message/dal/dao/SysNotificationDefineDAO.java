package com.mira.message.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.message.dal.entity.SysNotificationDefineEntity;
import com.mira.message.dal.mapper.SysNotificationDefineMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * sys_notification_define DAO
 *
 * <AUTHOR>
 */
@Repository
public class SysNotificationDefineDAO extends ServiceImpl<SysNotificationDefineMapper, SysNotificationDefineEntity> {
    public SysNotificationDefineEntity getByDefineId(Long defineId, String language) {
        return getOne(Wrappers.<SysNotificationDefineEntity>lambdaQuery()
                .eq(SysNotificationDefineEntity::getDefineId, defineId)
                .eq(SysNotificationDefineEntity::getLanguage, language));
    }

    public List<SysNotificationDefineEntity> listByLanguage(String language) {
        return list(Wrappers.<SysNotificationDefineEntity>lambdaQuery()
                .eq(SysNotificationDefineEntity::getLanguage, language));
    }
}

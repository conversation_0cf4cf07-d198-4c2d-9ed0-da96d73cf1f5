package com.mira.message.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.message.dal.entity.SysRateAppLogEntity;
import com.mira.message.dal.mapper.SysRateAppLogMapper;
import org.springframework.stereotype.Repository;

/**
 * sys_rate_app_log DAO
 *
 * <AUTHOR>
 */
@Repository
public class SysRateAppLogDAO extends ServiceImpl<SysRateAppLogMapper, SysRateAppLogEntity> {
    public SysRateAppLogEntity getByUserId(Long userId) {
        return getOne(Wrappers.<SysRateAppLogEntity>lambdaQuery()
                .eq(SysRateAppLogEntity::getUserId, userId));
    }
}

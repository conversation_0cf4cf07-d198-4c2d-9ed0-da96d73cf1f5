package com.mira.message.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.message.dal.entity.SysRateMenopauseLogEntity;
import com.mira.message.dal.mapper.SysRateMenopauseLogMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-11-28
 **/
@Repository
public class SysRateMenopauseLogDAO extends ServiceImpl<SysRateMenopauseLogMapper, SysRateMenopauseLogEntity> {
    public SysRateMenopauseLogEntity getByUserId(Long userId) {
        return getOne(Wrappers.<SysRateMenopauseLogEntity>lambdaQuery()
                              .eq(SysRateMenopauseLogEntity::getUserId, userId));
    }
}

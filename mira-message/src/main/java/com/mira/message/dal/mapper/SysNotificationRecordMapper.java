package com.mira.message.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.core.request.PageDTO;
import com.mira.message.dal.entity.SysNotificationRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * sys_notification_record
 *
 * <AUTHOR>
 */
@Mapper
public interface SysNotificationRecordMapper extends BaseMapper<SysNotificationRecordEntity> {
    List<SysNotificationRecordEntity> pageByUserIdAndDefineType(@Param("userId") Long userId,
                                                                @Param("index") Integer index, @Param("size") Integer size,
                                                                @Param("notificaitonTypeList") List<Integer> notificaitonTypeList,
                                                                @Param("expireTime") Long expireTime);

    Integer countByUserIdAndDefineType(@Param("userId") Long userId,
                                       @Param("notificaitonTypeList") List<Integer> notificaitonTypeList,
                                       @Param("expireTime") Long expireTime);

    Integer isAllReadByUserId(@Param("userId") Long userId,
                              @Param("days") Integer days,
                              @Param("notificaitonTypeList") List<Integer> notificaitonTypeList,
                              @Param("expireTime") Long expireTime);

    SysNotificationRecordEntity getByUserIdAndDefineType(@Param("userId") Long userId,
                                                         @Param("notificaitonTypeList") List<Integer> notificaitonTypeList,
                                                         @Param("recordId") Long recordId,
                                                         @Param("expireTime") Long expireTime);

    SysNotificationRecordEntity getSilent(@Param("userId") Long userId,
                                          @Param("expireTime") Long expireTime);

    void removeByUserIdAndCreateTime(@Param("userId") Long userId,
                                     @Param("notificaitonTypeList") List<Integer> notificaitonTypeList,
                                     @Param("timestamp") Long timestamp);

    void updateAllReadByUserId(@Param("userId") Long userId);
}

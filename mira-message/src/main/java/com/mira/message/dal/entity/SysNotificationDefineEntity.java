package com.mira.message.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.core.consts.enums.LocalEnum;
import com.mira.api.message.enums.SysNotificationTypeEnum;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 通知定义
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("sys_notification_define")
public class SysNotificationDefineEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 定义id
     */
    private Long defineId;

    /**
     * 标题
     */
    private String title;

    /**
     * 图标
     */
    private String icon;

    /**
     * 推送类型
     *
     * @see SysNotificationTypeEnum
     */
    private Integer type;

    /**
     * 推送子类型
     */
    private Integer subType;

    /**
     * 语言
     *
     * @see LocalEnum
     */
    private String language;

    /**
     * 推送的内容类型
     */
    private Integer contentType;

    /**
     * 推送的文字内容
     */
    private String content;

    /**
     * 样式类型，0-默认，1-全屏
     */
    private Integer styleType;

    /**
     * 按钮1
     */
    private String button1;

    /**
     * 按钮2
     */
    private String button2;

    /**
     * 链接
     */
    private String button1Link;

    /**
     * 链接
     */
    private String button2Link;

    /**
     * 额外链接
     */
    private String extra1Link;

    /**
     * 额外链接
     */
    private String extra2Link;

    /**
     * 图片链接
     */
    private String pictureUrl;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否有效，默认1：有效；为0表示无效，可以继续给对应用户推送
     */
    private Integer enable;

    /**
     * 过期时间,-1表示永不过期
     */
    private Long expireTime;

    /**
     * 背景图
     */
    private String backgroundImage;

    /**
     * 背景颜色
     */
    private String backgroundColor;
}

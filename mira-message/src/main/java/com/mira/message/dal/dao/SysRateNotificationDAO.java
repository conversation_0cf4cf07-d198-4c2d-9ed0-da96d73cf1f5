package com.mira.message.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.message.dal.entity.SysRateNotificationEntity;
import com.mira.message.dal.mapper.SysRateNotificationMapper;
import org.springframework.stereotype.Repository;

@Repository
public class SysRateNotificationDAO extends ServiceImpl<SysRateNotificationMapper, SysRateNotificationEntity> {
    public SysRateNotificationEntity getByUserIdAndType(Long userId, Integer type) {
        return getOne(Wrappers.<SysRateNotificationEntity>lambdaQuery()
                .eq(SysRateNotificationEntity::getUserId, userId)
                .eq(SysRateNotificationEntity::getType, type));
    }
}

package com.mira.message.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.message.dal.entity.SysNotificationThresholdStatisticsEntity;
import com.mira.message.dal.mapper.SysNotificationThresholdStatisticsMapper;
import org.springframework.stereotype.Repository;

@Repository
public class SysNotificationThresholdStatisticsDAO
        extends ServiceImpl<SysNotificationThresholdStatisticsMapper, SysNotificationThresholdStatisticsEntity> {
    public SysNotificationThresholdStatisticsEntity getByUserId(Long userId) {
        return getOne(Wrappers.<SysNotificationThresholdStatisticsEntity>lambdaQuery()
                .eq(SysNotificationThresholdStatisticsEntity::getUserId, userId));
    }
}

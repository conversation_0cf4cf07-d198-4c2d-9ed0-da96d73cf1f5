package com.mira.message.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户邮件模版取消订阅表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("user_mail_unsubscribe")
public class UserMailUnsubscribeEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 邮件模版编号
     */
    private Integer mailTemplateCode;

    /**
     * 邮件模版主题
     */
    private String mailTemplateSubject;
}

package com.mira.message.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.message.dal.entity.ClinicNotificationRecordEntity;
import com.mira.message.dal.mapper.ClinicNotificationRecordMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * clinic_notification_record DAO
 *
 * <AUTHOR>
 */
@Repository
public class ClinicNotificationRecordDAO extends ServiceImpl<ClinicNotificationRecordMapper, ClinicNotificationRecordEntity> {
    public ClinicNotificationRecordEntity getByNotificationRecordId(Long notificationRecordId) {
        return getOne(Wrappers.<ClinicNotificationRecordEntity>lambdaQuery()
                              .eq(ClinicNotificationRecordEntity::getNotificationRecordId, notificationRecordId));
    }

    public List<ClinicNotificationRecordEntity> listByNotificationRecordIds(List<Long> notificationRecordIds) {
        return list(Wrappers.<ClinicNotificationRecordEntity>lambdaQuery()
                            .in(ClinicNotificationRecordEntity::getNotificationRecordId, notificationRecordIds));
    }

    public List<ClinicNotificationRecordEntity> pageByUserId(Long userId, String tenantCode, int index, int pageSize) {
        return list(Wrappers.<ClinicNotificationRecordEntity>lambdaQuery()
                            .eq(ClinicNotificationRecordEntity::getUserId, userId)
                            .eq(ClinicNotificationRecordEntity::getTenantCode, tenantCode)
                            .orderByDesc(ClinicNotificationRecordEntity::getCreateTime)
                            .last("limit " + index + "," + pageSize));
    }
}

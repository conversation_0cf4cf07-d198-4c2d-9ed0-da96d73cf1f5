package com.mira.message.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * menopause好评邀请评价记录
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("sys_rate_menopause_log")
public class SysRateMenopauseLogEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * score
     */
    private Integer score;


}

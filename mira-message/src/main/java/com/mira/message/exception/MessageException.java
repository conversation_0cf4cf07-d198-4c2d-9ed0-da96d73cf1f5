package com.mira.message.exception;

import com.mira.core.response.enums.BaseCodeEnum;
import com.mira.core.response.enums.ICodeEnum;
import lombok.Getter;

/**
 * 消息服务相关异常
 *
 * <AUTHOR>
 */
@Getter
public class MessageException extends RuntimeException {
    private final Integer code;
    private final String msg;

    public MessageException(ICodeEnum iCodeEnum) {
        super(iCodeEnum.getMsg());
        this.code = iCodeEnum.getCode();
        this.msg = iCodeEnum.getMsg();
    }

    public MessageException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public MessageException(String msg) {
        super(msg);
        this.code = BaseCodeEnum.INTERNAL_SERVER_ERROR.getCode();
        this.msg = msg;
    }
}

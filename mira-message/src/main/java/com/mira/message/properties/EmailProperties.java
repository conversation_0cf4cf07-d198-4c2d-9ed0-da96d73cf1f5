package com.mira.message.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * email properties
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
public class EmailProperties {
    /**
     * 取消订阅地址
     */
    @Value("${email.unsubscribeUrl}")
    private String unsubscribeUrl;

    /**
     * tenant host
     */
    @Value("${email.tenantHost}")
    private String tenantHost;

    /**
     * template host
     */
    @Value("${email.templateHost}")
    private String templateHost;

    /**
     * 发件邮箱
     */
    @Value("${email.fromEmail}")
    private String fromEmail;

    /**
     * 发件人昵称
     */
    @Value("${email.fromNickname}")
    private String fromNickname;
}

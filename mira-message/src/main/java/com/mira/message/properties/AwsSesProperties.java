package com.mira.message.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * aws ses 配置
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "aws.ses")
public class AwsSesProperties {
    /**
     * id，唯一标识
     */
    private String accessKey;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 区域
     */
    private String region;

    /**
     * 失败重试次数
     */
    private Integer maxErrorRetry;
}
